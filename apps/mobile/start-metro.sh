#!/bin/bash

echo "🐟 Fishivo Metro Bundler Başlatılıyor..."

# Environment Variables
export JAVA_HOME=/usr/local/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home
export PATH=$JAVA_HOME/bin:$PATH
export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools
export REACT_NATIVE_PACKAGER_HOSTNAME=0.0.0.0
export RCT_METRO_PORT=8090

# Clean cache
echo "🧹 Cache temizleniyor..."
rm -rf .metro-cache
rm -rf node_modules/.cache
watchman watch-del-all 2>/dev/null || echo "Watchman bulunamadı, devam ediliyor..."

# Kill existing Metro instances
echo "🔪 Eski Metro process'leri durduruluyor..."
pkill -f "metro" || echo "Metro process bulunamadı"
pkill -f "react-native start" || echo "React Native process bulunamadı"

# Port check
echo "🔍 Port 8090 kontrol ediliyor..."
lsof -i :8090 && pkill -9 $(lsof -t -i:8090) || echo "Port 8090 boş"

# Start Metro with detailed logging
echo "🚀 Metro başlatılıyor..."
echo "Port: 8090"
echo "Host: 0.0.0.0 (tüm network interface'leri)"

# Run Metro
npx react-native start \
  --port 8090 \
  --host 0.0.0.0 \
  --reset-cache \
  --verbose

echo "Metro kapandı!" 