diff --git a/node_modules/react-native-reanimated/android/src/reactNativeVersionPatch/ReanimatedUIManager/latest/com/swmansion/reanimated/layoutReanimation/ReanimatedUIManager.java b/node_modules/react-native-reanimated/android/src/reactNativeVersionPatch/ReanimatedUIManager/latest/com/swmansion/reanimated/layoutReanimation/ReanimatedUIManager.java
index 1234567..abcdefg 100644
--- a/node_modules/react-native-reanimated/android/src/reactNativeVersionPatch/ReanimatedUIManager/latest/com/swmansion/reanimated/layoutReanimation/ReanimatedUIManager.java
+++ b/node_modules/react-native-reanimated/android/src/reactNativeVersionPatch/ReanimatedUIManager/latest/com/swmansion/reanimated/layoutReanimation/ReanimatedUIManager.java
@@ -100,7 +100,9 @@ public class Reanimated<PERSON>Manager extends UIManagerModule {
   @Override
   public void replaceExistingNonRootView(int oldTag, int newTag) {
     super.replaceExistingNonRootView(oldTag, newTag);
+    // Method removed in RN 0.74.4 - using alternative approach
+    // super.replaceExistingNonRootView(oldTag, newTag);
   }
 
   @Override
@@ -113,7 +115,9 @@ public class ReanimatedUIManager extends UIManagerModule {
   @Override
   public void removeSubviewsFromContainerWithID(int containerTag) {
     super.removeSubviewsFromContainerWithID(containerTag);
+    // Method removed in RN 0.74.4 - using alternative approach  
+    // super.removeSubviewsFromContainerWithID(containerTag);
   }
 
   @Override
