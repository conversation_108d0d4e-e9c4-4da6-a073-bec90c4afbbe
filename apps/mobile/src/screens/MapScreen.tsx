import React from 'react';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MapComponent, ScreenContainer } from '@fishivo/ui';
import { theme } from '@fishivo/shared';

const MapScreen: React.FC = () => {
  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
      <ScreenContainer paddingHorizontal="none" paddingVertical="none">
        <MapComponent
          showCrosshair={true}
          showCoordinates={true}
          showLocationButton={true}
          showLayerSelector={true}
          show3DToggle={true}
        />
      </ScreenContainer>
    </SafeAreaView>
  );
};

export default MapScreen;