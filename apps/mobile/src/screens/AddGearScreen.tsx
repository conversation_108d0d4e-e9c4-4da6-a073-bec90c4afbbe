import React, { useState, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  Modal,
  Image,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Icon, Add<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ScreenContainer, SuccessModal } from '@fishivo/ui';
import { theme } from '@fishivo/shared';
import { apiService } from '@fishivo/services';

interface AddGearScreenProps {
  navigation: any;
}

interface GearCategory {
  id: string;
  name: string;
  icon: string;
  items: string[];
}

interface Brand {
  id: string;
  name: string;
}

interface ExistingGear {
  id: string;
  name: string;
  brand: string;
  category: string;
  productLine?: string;
  modelName: string;
  imageUrl?: string;
  usageCount: number;
}

interface CategoryBrand {
  brand: string;
  productCount: number;
}

type ScreenState = 'categories' | 'brands' | 'products' | 'customForm';

const AddGearScreen: React.FC<AddGearScreenProps> = ({ navigation }) => {
  const [currentScreen, setCurrentScreen] = useState<ScreenState>('categories');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedBrand, setSelectedBrand] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [categorySearchQuery, setCategorySearchQuery] = useState('');
  const [brandSearchQuery, setBrandSearchQuery] = useState('');
  
  const [gearData, setGearData] = useState({
    name: '',
    category: '',
    brand: '',
    productLine: '',
    modelName: '',

    notes: '',
    imageUri: '',
  });

  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showBrandModal, setShowBrandModal] = useState(false);
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [customBrandName, setCustomBrandName] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<ExistingGear | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const gearCategories: GearCategory[] = [
    {
      id: 'lure',
      name: 'Yapay Yem',
      icon: 'fishing-lure',
      items: ['Rapala', 'Silikon Yem', 'Metal Jig', 'Spinner', 'Spoon', 'Popper', 'Wobbler']
    },
    {
      id: 'hook',
      name: 'İğne & Terminal',
      icon: 'fishing-hook',
      items: ['Circle Hook', 'J Hook', 'Treble Hook', 'Kurşun', 'Swivel', 'Snap']
    },
    {
      id: 'line',
      name: 'Misina & İp',
      icon: 'fishing-line',
      items: ['Monofilament', 'Braided', 'Fluorocarbon', 'Steel Leader']
    },
    {
      id: 'reel',
      name: 'Makine',
      icon: 'fishing-reel',
      items: ['Spinning Makine', 'Casting Makine', 'Surf Makine', 'Trolling Makine']
    },
    {
      id: 'rod',
      name: 'Olta Kamışı',
      icon: 'fishing-rod',
      items: ['Spinning Kamış', 'Casting Kamış', 'Surf Kamış', 'Trolling Kamış', 'Fly Kamış']
    },
    {
      id: 'accessory',
      name: 'Aksesuarlar',
      icon: 'fishing-net',
      items: ['Çanta', 'Kutu', 'Pinset', 'Net', 'Balık Tartısı', 'Kesici', 'İğne Kesici']
    },
    {
      id: 'clothing',
      name: 'Giyim & Güvenlik',
      icon: 'fishing-vest',
      items: ['Yelek', 'Çizme', 'Eldiven', 'Şapka', 'Gözlük', 'Can Yeleği']
    }
  ];

  // API'den ekipman veritabanını çek
  const [existingGearDatabase, setExistingGearDatabase] = useState<ExistingGear[]>([]);
  
  useEffect(() => {
    const loadGearDatabase = async () => {
      try {
        // Real API çağrısı
        const gearData = await apiService.getGearDatabase();
        setExistingGearDatabase(gearData.items || []);
      } catch (error) {
        console.error('Gear database yüklenirken hata:', error);
        setExistingGearDatabase([]);
      }
    };
    
    loadGearDatabase();
  }, []);


  // Filter categories based on search
  const filteredCategories = useMemo(() => {
    if (!categorySearchQuery) return gearCategories;
    
    return gearCategories.filter(category =>
      category.name.toLowerCase().includes(categorySearchQuery.toLowerCase()) ||
      category.items.some(item => 
        item.toLowerCase().includes(categorySearchQuery.toLowerCase())
      )
    );
  }, [categorySearchQuery]);

  // Get brands for selected category
  const categoryBrands = useMemo(() => {
    if (!selectedCategory) return [];
    
    const brandsInCategory = existingGearDatabase
      .filter(gear => gear.category === selectedCategory)
      .reduce((acc, gear) => {
        const existingBrand = acc.find(b => b.brand === gear.brand);
        if (existingBrand) {
          existingBrand.productCount++;
        } else {
          acc.push({ brand: gear.brand, productCount: 1 });
        }
        return acc;
      }, [] as CategoryBrand[]);

    if (!brandSearchQuery) return brandsInCategory;
    
    return brandsInCategory.filter(brand =>
      brand.brand.toLowerCase().includes(brandSearchQuery.toLowerCase())
    );
  }, [selectedCategory, brandSearchQuery]);

  // Get products for selected category and brand
  const categoryProducts = useMemo(() => {
    if (!selectedCategory || !selectedBrand) return [];
    
    return existingGearDatabase
      .filter(gear => gear.category === selectedCategory && gear.brand === selectedBrand);
  }, [selectedCategory, selectedBrand]);

  const handleSelectCategory = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setCurrentScreen('brands');
  };

  const handleSelectBrand = (brand: string) => {
    setSelectedBrand(brand);
    setCurrentScreen('products');
  };

  const handleSelectProduct = (gear: ExistingGear) => {
    setSelectedProduct(gear);
    setShowConfirmModal(true);
  };

  const handleConfirmAddGear = () => {
    setShowConfirmModal(false);
    // Burada API call yapılacak
    setTimeout(() => {
      setShowSuccessModal(true);
    }, 100);
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    navigation.goBack();
  };

  const handleGoToCustomForm = () => {
    setCurrentScreen('customForm');
  };

  const handleGoBack = () => {
    if (currentScreen === 'brands') {
      setCurrentScreen('categories');
      setSelectedCategory('');
    } else if (currentScreen === 'products') {
      setCurrentScreen('brands');
      setSelectedBrand('');
    } else if (currentScreen === 'customForm') {
      if (selectedBrand) {
        setCurrentScreen('products');
      } else if (selectedCategory) {
        setCurrentScreen('brands');
      } else {
        setCurrentScreen('categories');
      }
    } else {
      navigation.goBack();
    }
  };

  const handleSave = async () => {
    if (!gearData.name || !gearData.category || !gearData.brand || !gearData.modelName) {
      setErrorMessage('Lütfen zorunlu alanları doldurun (Kategori, Marka, Model Adı, Ekipman Adı)');
      setShowErrorModal(true);
      return;
    }

    try {
      setSuccessMessage('Yeni ekipman başarıyla eklendi ve moderasyona gönderildi');
      setShowSuccessModal(true);
    } catch (error) {
      setErrorMessage('Ekipman eklenirken bir hata oluştu');
      setShowErrorModal(true);
    }
  };

  const selectedCategoryData = gearCategories.find(cat => cat.id === selectedCategory);
  const selectedFormCategory = gearCategories.find(cat => cat.id === gearData.category);

  const renderCategoryItem = ({ item }: { item: GearCategory }) => (
    <TouchableOpacity
      style={styles.categoryListItem}
      onPress={() => handleSelectCategory(item.id)}
    >
      <View style={styles.categoryIconContainer}>
        <Icon name={item.icon} size={24} color={theme.colors.primary} />
      </View>
      <View style={styles.categoryContent}>
        <Text style={styles.categoryName}>{item.name}</Text>
        <Text style={styles.categoryItems}>
          {item.items.slice(0, 3).join(', ')}
          {item.items.length > 3 && '...'}
        </Text>
      </View>
      <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );

  const renderBrandItem = ({ item }: { item: CategoryBrand }) => (
    <TouchableOpacity
      style={styles.brandListItem}
      onPress={() => handleSelectBrand(item.brand)}
    >
      <Text style={styles.brandName}>{item.brand}</Text>
      <View style={styles.brandRight}>
        <Text style={styles.brandCount}>{item.productCount} ürün</Text>
        <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
      </View>
    </TouchableOpacity>
  );

  const renderProductItem = ({ item }: { item: ExistingGear }) => (
    <TouchableOpacity
      style={styles.productListItem}
      onPress={() => handleSelectProduct(item)}
    >
      <View style={styles.productImageContainer}>
        {item.imageUrl ? (
          <Image source={{ uri: item.imageUrl }} style={styles.productImage} />
        ) : (
          <View style={styles.productPlaceholder}>
            <Icon name="package" size={24} color={theme.colors.textSecondary} />
          </View>
        )}
      </View>
      <View style={styles.productContent}>
        <Text style={styles.productName}>{item.name}</Text>
        <Text style={styles.productDetails}>{item.modelName}</Text>
        <Text style={styles.productUsage}>{item.usageCount} kişi kullanıyor</Text>
      </View>
      <Icon name="chevron-right" size={20} color={theme.colors.textSecondary} />
    </TouchableOpacity>
  );

  const getHeaderTitle = () => {
    switch (currentScreen) {
      case 'categories': return 'Kategori Seçin';
      case 'brands': return selectedCategoryData?.name || 'Marka Seçin';
      case 'products': return `${selectedBrand} Ürünleri`;
      case 'customForm': return 'Yeni Ekipman Ekle';
      default: return 'Ekipman Ekle';
    }
  };

  if (currentScreen === 'customForm') {
    return (
      <SafeAreaView style={styles.container}>
        <AppHeader
          title={getHeaderTitle()}
          leftButtons={[
            {
              icon: 'arrow-left',
              onPress: handleGoBack
            }
          ]}
          rightComponent={
            <TouchableOpacity
              style={styles.saveButton}
              onPress={handleSave}
            >
              <Text style={styles.saveButtonText}>Kaydet</Text>
            </TouchableOpacity>
          }
        />

        <ScreenContainer>
          <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Ekipman Adı *</Text>
            <TextInput
              style={styles.input}
              value={gearData.name}
              onChangeText={(text) => setGearData(prev => ({ ...prev, name: text }))}
              placeholder="Ekipman adını girin"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kategori *</Text>
            <TouchableOpacity 
              style={styles.categoryButton}
              onPress={() => setShowCategoryModal(true)}
            >
              <View style={styles.categoryLeft}>
                {selectedFormCategory ? (
                  <>
                    <View style={styles.categoryIcon}>
                      <Icon name={selectedFormCategory.icon} size={20} color={theme.colors.primary} />
                    </View>
                    <Text style={styles.categoryText}>{selectedFormCategory.name}</Text>
                  </>
                ) : (
                  <Text style={styles.categoryPlaceholder}>Kategori seçin</Text>
                )}
              </View>
              <Icon name="chevron-down" size={16} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Marka *</Text>
            <TextInput
              style={styles.input}
              value={gearData.brand}
              onChangeText={(text) => setGearData(prev => ({ ...prev, brand: text }))}
              placeholder="Marka adını girin"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Model Adı *</Text>
            <TextInput
              style={styles.input}
              value={gearData.modelName}
              onChangeText={(text) => setGearData(prev => ({ ...prev, modelName: text }))}
              placeholder="Model adını girin"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>



          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notlar</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={gearData.notes}
              onChangeText={(text) => setGearData(prev => ({ ...prev, notes: text }))}
              placeholder="Ekipman hakkında notlar..."
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              textAlignVertical="top"
            />
          </View>
          </ScrollView>
        </ScreenContainer>

        {/* Category Modal */}
        <Modal
          visible={showCategoryModal}
          transparent
          animationType="slide"
          onRequestClose={() => setShowCategoryModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>Kategori Seçin</Text>
                <TouchableOpacity onPress={() => setShowCategoryModal(false)}>
                  <Icon name="x" size={24} color={theme.colors.text} />
                </TouchableOpacity>
              </View>
              <ScrollView style={styles.modalBody}>
                {gearCategories.map((category) => (
                  <TouchableOpacity
                    key={category.id}
                    style={styles.categoryModalItem}
                    onPress={() => {
                      setGearData(prev => ({ ...prev, category: category.id }));
                      setShowCategoryModal(false);
                    }}
                  >
                    <View style={styles.categoryIconContainer}>
                      <Icon name={category.icon} size={20} color={theme.colors.primary} />
                    </View>
                    <Text style={styles.categoryName}>{category.name}</Text>
                    <Icon name="chevron-right" size={16} color={theme.colors.textSecondary} />
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        </Modal>

        <SuccessModal
          visible={showSuccessModal}
          title="Başarılı!"
          message={successMessage}
          onClose={() => {
            setShowSuccessModal(false);
            navigation.goBack();
          }}
        />

        <SuccessModal
          visible={showErrorModal}
          title="Hata"
          message={errorMessage}
          onClose={() => setShowErrorModal(false)}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title={getHeaderTitle()}
        leftButtons={[
          {
            icon: 'arrow-left',
            onPress: handleGoBack
          }
        ]}
      />

      {currentScreen === 'categories' && (
        <>
          {/* Search Box */}
          <View style={styles.searchContainer}>
            <Icon name="search" size={16} color={theme.colors.textSecondary} />
            <TextInput
              style={styles.searchInput}
              value={categorySearchQuery}
              onChangeText={setCategorySearchQuery}
              placeholder="Kategori ara..."
              placeholderTextColor={theme.colors.textSecondary}
            />
            {categorySearchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setCategorySearchQuery('')}>
                <Icon name="x" size={16} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>

          {/* Quick Add Section - Moved above categories list */}
          <View style={styles.quickAddContainer}>
            <View style={styles.quickAddIconContainer}>
              <Icon name="help-circle" size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.quickAddContent}>
              <Text style={styles.quickAddTitle}>Ekipmanı bulamadınız mı?</Text>
              <Text style={styles.quickAddSubtitle}>
                Yeni ekipman ekleyip topluluğa katkıda bulunun
              </Text>
            </View>
            <AddButton 
              variant="filled"
              onPress={handleGoToCustomForm}
            />
          </View>

          {/* Categories List */}
          <FlatList
            data={filteredCategories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id}
            style={styles.list}
            showsVerticalScrollIndicator={false}
          />
        </>
      )}

      {currentScreen === 'brands' && (
        <>
          {/* Search Box */}
          <View style={styles.searchContainer}>
            <Icon name="search" size={16} color={theme.colors.textSecondary} />
            <TextInput
              style={styles.searchInput}
              value={brandSearchQuery}
              onChangeText={setBrandSearchQuery}
              placeholder="Marka ara..."
              placeholderTextColor={theme.colors.textSecondary}
            />
            {brandSearchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setBrandSearchQuery('')}>
                <Icon name="x" size={16} color={theme.colors.textSecondary} />
              </TouchableOpacity>
            )}
          </View>

          {/* Quick Add Section - Moved above brands list */}
          <View style={styles.quickAddContainer}>
            <View style={styles.quickAddIconContainer}>
              <Icon name="help-circle" size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.quickAddContent}>
              <Text style={styles.quickAddTitle}>Markanızı bulamadınız mı?</Text>
              <Text style={styles.quickAddSubtitle}>
                Yeni ekipman ekleyip topluluğa katkıda bulunun
              </Text>
            </View>
            <AddButton 
              variant="filled"
              onPress={handleGoToCustomForm}
            />
          </View>

          {/* Brands List */}
          <FlatList
            data={categoryBrands}
            renderItem={renderBrandItem}
            keyExtractor={(item) => item.brand}
            style={styles.list}
            showsVerticalScrollIndicator={false}
          />
        </>
      )}

      {currentScreen === 'products' && (
        <>
          {/* Quick Add Section - Moved above products list */}
          <View style={styles.quickAddContainer}>
            <View style={styles.quickAddIconContainer}>
              <Icon name="help-circle" size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.quickAddContent}>
              <Text style={styles.quickAddTitle}>Modeli bulamadınız mı?</Text>
              <Text style={styles.quickAddSubtitle}>
                Yeni ekipman ekleyip topluluğa katkıda bulunun
              </Text>
            </View>
            <AddButton 
              variant="filled"
              onPress={handleGoToCustomForm}
            />
          </View>

          {/* Products List */}
          <FlatList
            data={categoryProducts}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            style={styles.list}
            showsVerticalScrollIndicator={false}
          />
        </>
      )}

      {/* Confirmation Modal */}
      <Modal
        visible={showConfirmModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowConfirmModal(false)}
      >
        <View style={styles.confirmModalOverlay}>
          <View style={styles.confirmModalContent}>
            {/* Product Preview */}
            <View style={styles.confirmProductContainer}>
              <View style={styles.confirmProductImageContainer}>
                {selectedProduct?.imageUrl ? (
                  <Image 
                    source={{ uri: selectedProduct.imageUrl }} 
                    style={styles.confirmProductImage} 
                  />
                ) : (
                  <View style={styles.confirmProductPlaceholder}>
                    <Text style={styles.confirmProductIcon}>🎣</Text>
                  </View>
                )}
              </View>
              <View style={styles.confirmProductInfo}>
                <Text style={styles.confirmProductName}>
                  {selectedProduct?.name}
                </Text>
                <Text style={styles.confirmProductBrand}>
                  {selectedProduct?.brand}
                </Text>
                <Text style={styles.confirmProductUsage}>
                  {selectedProduct?.usageCount} kişi kullanıyor
                </Text>
              </View>
            </View>

            {/* Title */}
            <Text style={styles.confirmModalTitle}>
              Bu ekipmanı eklemek istiyor musunuz?
            </Text>
            
            {/* Description */}
            <Text style={styles.confirmModalDescription}>
              Seçtiğiniz ekipman profilinize eklenecek ve diğer kullanıcılar tarafından görülebilecek.
            </Text>

            {/* Action Buttons */}
            <View style={styles.confirmModalActions}>
              <TouchableOpacity
                style={styles.confirmCancelButton}
                onPress={() => setShowConfirmModal(false)}
              >
                <Text style={styles.confirmCancelText}>İptal</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.confirmAddButton}
                onPress={handleConfirmAddGear}
              >
                <Icon name="check" size={16} color={theme.colors.background} />
                <Text style={styles.confirmAddText}>Ekle</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Success Modal */}
      <Modal
        visible={showSuccessModal}
        transparent
        animationType="fade"
        onRequestClose={handleSuccessModalClose}
      >
        <View style={styles.confirmModalOverlay}>
          <View style={styles.confirmModalContent}>
            {/* Success Icon */}
            <View style={styles.successIconContainer}>
              <Icon name="check" size={32} color={theme.colors.primary} />
            </View>

            {/* Title */}
            <Text style={styles.confirmModalTitle}>
              Başarılı!
            </Text>
            
            {/* Description */}
            <Text style={styles.confirmModalDescription}>
              Ekipman profilinize başarıyla eklendi.
            </Text>

            {/* Action Button */}
            <TouchableOpacity
              style={styles.successButton}
              onPress={handleSuccessModalClose}
            >
              <Text style={styles.successButtonText}>Tamam</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',

    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  backButton: {
    width: 32,
    height: 32,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: theme.typography.xl,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
  },
  saveButton: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
  },
  saveButtonText: {
    fontSize: theme.typography.sm,
    color: theme.colors.background,
    fontWeight: theme.typography.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing.sm,
  },
  section: {
    marginTop: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: theme.spacing.sm,
  },
  categoryButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  categoryLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: `${theme.colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  categoryText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  categoryPlaceholder: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
  },
  input: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    fontSize: theme.typography.base,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  textArea: {
    height: 100,
    paddingTop: theme.spacing.md,
  },

  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: theme.borderRadius.xl,
    borderTopRightRadius: theme.borderRadius.xl,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
    fontWeight: theme.typography.bold,
  },
  modalBody: {
    padding: theme.spacing.md,
  },
  categoryModalItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  list: {
    flex: 1,
    paddingHorizontal: theme.spacing.sm,
  },
  categoryListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    marginVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  categoryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: `${theme.colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  categoryContent: {
    flex: 1,
  },
  categoryName: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: 4,
  },
  categoryItems: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    lineHeight: 18,
  },
  brandListItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    marginVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  brandName: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    flex: 1,
  },
  brandRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  brandCount: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  productListItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    marginVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  productImageContainer: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.md,
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.md,
  },
  productPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productIcon: {
    fontSize: 24,
  },
  productContent: {
    flex: 1,
  },
  productName: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: 4,
  },
  productDetails: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  productUsage: {
    fontSize: theme.typography.xs,
    color: theme.colors.primary,
  },
  quickAddContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    margin: theme.spacing.md,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  quickAddIconContainer: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: `${theme.colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  quickAddContent: {
    flex: 1,
    marginRight: theme.spacing.md,
  },
  quickAddTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.semibold,
    marginBottom: 2,
  },
  quickAddSubtitle: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    lineHeight: 16,
  },

  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,

    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    borderColor: theme.colors.border,
    height: 44,
  },
  searchInput: {
    flex: 1,
    fontSize: theme.typography.sm,
    color: theme.colors.text,

    paddingVertical: 0,
  },
  confirmModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.lg,
  },
  confirmModalContent: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.xl,
    width: '100%',
    maxWidth: 340,
    alignItems: 'center',
  },
  confirmProductContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    width: '100%',
  },
  confirmProductImageContainer: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  confirmProductImage: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.md,
  },
  confirmProductPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmProductIcon: {
    fontSize: 24,
  },
  confirmProductInfo: {
    flex: 1,
  },
  confirmProductName: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.semibold,
    color: theme.colors.text,
    marginBottom: 4,
  },
  confirmProductBrand: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  confirmProductUsage: {
    fontSize: theme.typography.xs,
    color: theme.colors.primary,
  },
  confirmModalTitle: {
    fontSize: theme.typography.xl,
    fontWeight: theme.typography.semibold,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  confirmModalDescription: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: 22,
  },
  confirmModalActions: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    width: '100%',
  },
  confirmCancelButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surfaceVariant,
    alignItems: 'center',
  },
  confirmCancelText: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.text,
  },
  confirmAddButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmAddText: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.background,
    marginLeft: theme.spacing.sm,
  },
  successIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: `${theme.colors.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  successButton: {
    width: '100%',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
  },
  successButtonText: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.medium,
    color: theme.colors.background,
  },
  saveButtonContainer: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
});

export default AddGearScreen; 