import { Platform } from 'react-native';

// Supabase Configuration
export const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
export const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Google Configuration - Only Web Client ID needed per Supabase docs
export const GOOGLE_WEB_CLIENT_ID = process.env.GOOGLE_WEB_CLIENT_ID || '';

// API Configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

console.log('Environment Info:', {
  platform: Platform.OS,
  supabaseUrl: SUPABASE_URL ? 'configured' : 'missing',
  googleWebClientId: GOOGLE_WEB_CLIENT_ID ? 'configured' : 'missing',
  apiUrl: API_BASE_URL
}); 