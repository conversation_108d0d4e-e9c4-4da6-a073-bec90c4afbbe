// Metro configuration for Turborepo monorepo with React Native CLI
const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const path = require("path");

/**
 * Metro configuration for Turborepo monorepo
 * OFFICIAL VERCEL TURBOREPO TEMPLATE CONFIG
 * Source: https://github.com/vercel/turborepo/tree/main/examples/with-react-native-web
 *
 * @type {import('metro-config').MetroConfig}
 */

// Monorepo paths
const workspaceRoot = path.resolve(__dirname, '../..');
const projectRoot = __dirname;

const config = {
  // Watch all files in the monorepo
  watchFolders: [workspaceRoot],
  
  // Configure the packager server
  server: {
    enhanceMiddleware: (middleware) => {
      return middleware;
    },
  },
  
  resolver: {
    // Make sure we look for modules in the right places
    nodeModulesPaths: [
      path.resolve(projectRoot, 'node_modules'),
      path.resolve(workspaceRoot, 'node_modules'),
    ],
    
    // Resolve workspace packages directly
    extraNodeModules: new Proxy({}, {
      get: (target, name) => {
        // Handle @fishivo scope packages
        if (name.startsWith('@fishivo/')) {
          const packageName = name.replace('@fishivo/', '');
          return path.join(workspaceRoot, 'packages', packageName);
        }
        
        // Try mobile node_modules first
        const mobilePath = path.join(projectRoot, 'node_modules', name);
        try {
          require.resolve(path.join(mobilePath, 'package.json'));
          return mobilePath;
        } catch (e) {}
        
        // Then try root node_modules
        const rootPath = path.join(workspaceRoot, 'node_modules', name);
        try {
          require.resolve(path.join(rootPath, 'package.json'));
          return rootPath;
        } catch (e) {}
        
        // Return root path as fallback
        return rootPath;
      }
    }),
    
    // Disable hierarchical lookup to avoid confusion
    disableHierarchicalLookup: true,
    
    // Enable symlinks for monorepo
    unstable_enableSymlinks: true,
  },
  
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
