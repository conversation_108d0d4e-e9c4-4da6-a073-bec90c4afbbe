#!/bin/bash

# Kill any existing Metro processes
pkill -9 -f "node.*metro" 2>/dev/null || true
pkill -9 -f "node.*react-native" 2>/dev/null || true

# Clear all caches
rm -rf $TMPDIR/metro-* 2>/dev/null || true
rm -rf $TMPDIR/react-* 2>/dev/null || true
rm -rf $TMPDIR/haste-* 2>/dev/null || true
rm -rf .metro-cache
rm -rf node_modules/.cache

# Wait a moment
sleep 2

# Start Metro with reset cache
echo "Starting Metro bundler..."
yarn react-native start --reset-cache --port 8090 