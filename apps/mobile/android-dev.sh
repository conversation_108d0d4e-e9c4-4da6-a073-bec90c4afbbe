#!/bin/bash

echo "🐟 Fishivo Android Development Environment"
echo "=========================================="

# 1. Environment Setup
echo "📦 Setting up environment variables..."
export JAVA_HOME=/usr/local/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home
export PATH=$JAVA_HOME/bin:$PATH
export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools
export REACT_NATIVE_PACKAGER_HOSTNAME=***********
export RCT_METRO_PORT=8090

# 2. Clean Everything
echo "🧹 Cleaning caches..."
rm -rf .metro-cache
rm -rf node_modules/.cache
rm -rf $TMPDIR/react-*
rm -rf $TMPDIR/metro-*
rm -rf $TMPDIR/haste-*
watchman watch-del-all 2>/dev/null || true

# 3. Kill All Processes
echo "🔪 Killing existing processes..."
pkill -f "metro" || true
pkill -f "react-native" || true
pkill -f "node.*8090" || true

# 4. ADB Setup
echo "📱 Setting up ADB..."
adb kill-server
adb start-server
adb devices

# 5. Port Forwarding
echo "🔌 Setting up port forwarding..."
adb reverse tcp:8090 tcp:8090
adb reverse tcp:8081 tcp:8081
adb reverse tcp:3001 tcp:3001

# 6. Start Metro in new terminal
echo "🚀 Starting Metro bundler..."
osascript -e 'tell app "Terminal" to do script "cd '$PWD' && npx react-native start --port 8090 --reset-cache"'

# 7. Wait for Metro
echo "⏳ Waiting for Metro to start..."
sleep 10

# 8. Check Metro Status
echo "🔍 Checking Metro status..."
curl -s http://localhost:8090/status || echo "Metro not responding yet..."

# 9. Build and Install App
echo "📲 Building and installing app..."
cd android
./gradlew clean
./gradlew assembleDebug
adb install -r app/build/outputs/apk/debug/app-debug.apk
cd ..

# 10. Start App
echo "🎯 Starting app..."
adb shell am start -n com.fishivo/.MainActivity

echo "✅ Done! Check your device."
echo "📱 If you see red screen, shake device and tap 'Reload'" 