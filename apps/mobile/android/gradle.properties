# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx512m -XX:MaxMetaspaceSize=256m
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true

# Enable AAPT2 PNG crunching
android.enablePngCrunchInReleaseBuilds=true

# Use this property to specify which architecture you want to build.
# You can also override it from the CLI using
# ./gradlew <task> -PreactNativeArchitectures=x86_64
reactNativeArchitectures=armeabi-v7a,arm64-v8a,x86,x86_64

# Use this property to enable support to the new architecture.
# This will allow you to use TurboModules and the Fabric render in
# your application. You should enable this flag either if you want
# to write custom TurboModules/Fabric components OR use libraries that
# are providing them.
newArchEnabled=false

# Use this property to enable or disable the Hermes JS engine.
# If set to false, you will be using JSC instead.
hermesEnabled=true

# Enable GIF support in React Native images (~200 B increase)
android.enableGifSupport=true
# Enable webp support in React Native images (~85 KB increase)
android.enableWebpSupport=true
android.suppressUnsupportedCompileSdk=35
android.enableJetifier=true

# Mapbox configuration - ESSENTIAL for the app
expoRNMapboxMapsVersion=11.0.0
# MAPBOX_DOWNLOADS_TOKEN moved to environment variable for security

# Increase memory for Gradle and Jetifier
org.gradle.daemon=true

# Disable configuration cache for React Native compatibility
org.gradle.configuration-cache=false
org.gradle.unsafe.configuration-cache=false

# Enable build cache
org.gradle.caching=true

# Java version configuration - Homebrew Java 17
org.gradle.java.home=/usr/local/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home

# Force Gradle to use Java 17 for all tasks
org.gradle.java.installations.auto-detect=false
org.gradle.java.installations.fromEnv=JAVA_HOME
org.gradle.java.installations.paths=/usr/local/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home
