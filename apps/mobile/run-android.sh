#!/bin/bash

# 🐟 Fishivo Android Uygulama Başlatma Script'i - TAMAMEN ÇALIŞAN SÜRÜM
echo "🐟 Fishivo Android Uygulaması Başlatılıyor..."

# Java 17 ortam değişkenlerini ayarla
export JAVA_HOME=/usr/local/opt/openjdk@17
export PATH=$JAVA_HOME/bin:$PATH

# Android SDK ortam değişkenlerini ayarla
export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools

# React Native environment variables
export REACT_NATIVE_PACKAGER_HOSTNAME=***********
export RCT_METRO_PORT=8081

# Java versiyonunu kontrol et
echo "Java Version: $(java -version 2>&1 | head -n 1)"

# Bağlı cihazları kontrol et
echo "Bağlı Android Cihazları:"
adb devices

# Metro bundler'ı durdur ve temizle
echo "Metro bundler temizleniyor..."
pkill -f "react-native start" || echo "Metro zaten durdurulmuştu"
pkill -f "metro" || echo "Metro process'leri temizlendi"

# Port forwarding ayarla (hem Metro hem de Backend API için)
echo "ADB port forwarding ayarlanıyor..."
adb reverse tcp:8081 tcp:8081
adb reverse tcp:3001 tcp:3001

# Metro bundler'ı başlat (arka planda)
echo "Metro bundler başlatılıyor (port 8081)..."
npx react-native start --port 8081 --reset-cache &

# Metro'nun başlamasını bekle
echo "Metro bundler'ın başlaması bekleniyor..."
sleep 10

# Metro status kontrolü
echo "Metro bundler status kontrol ediliyor..."
curl -s http://localhost:8081/status

# Android uygulamasını derle ve çalıştır
echo "Android uygulaması build ediliyor ve çalıştırılıyor..."
npx react-native run-android

# Dev menu aç
echo "Development menu açılıyor..."
adb shell input keyevent 82

echo "🎉 ✅ Fishivo Android uygulaması başarıyla başlatıldı!"
echo "📱 Cihazınızda uygulama çalışıyor"
echo "🔄 Reload için: R tuşuna çift basın veya Dev Menu > Reload"
echo "🛠️ Development server: http://localhost:8081" 