import reactNativeConfig from '@react-native/eslint-config';

export default [
  {
    files: ['**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx'],
    ...reactNativeConfig,
    languageOptions: {
      ecmaVersion: 2021,
      sourceType: 'module',
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    rules: {
      // React Native 0.78 optimized rules
      'react-native/no-unused-styles': 'warn',
      'react-native/split-platform-components': 'warn',  
      'react-native/no-inline-styles': 'off', // Allow for flexibility
      'react-native/no-color-literals': 'warn',
      'react-native/no-raw-text': 'off', // Too restrictive
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/no-explicit-any': 'off', // Allow any for gradual typing
      'prettier/prettier': 'off', // Prevent conflicts
      'react/react-in-jsx-scope': 'off', // React 19 doesn't need this
      'react/jsx-uses-react': 'off', // React 19 doesn't need this
      'no-undef': 'off', // TypeScript handles this
      'semi': ['error', 'always'],
      'quotes': ['error', 'single'],
      'comma-dangle': ['error', 'always-multiline'],
    },
  },
  {
    ignores: [
      'node_modules/**',
      'packages/*/node_modules/**',
      '**/*.d.ts',
      'android/**',
      'ios/**',
      'patches/**',
    ],
  },
]; 