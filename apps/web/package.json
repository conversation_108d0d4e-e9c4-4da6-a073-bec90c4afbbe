{"name": "@fishivo/web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@fishivo/config": "1.0.0", "@fishivo/hooks": "1.0.0", "@fishivo/services": "1.0.0", "@fishivo/shared": "1.0.0", "@fishivo/ui": "1.0.0", "@fishivo/utils": "1.0.0", "@supabase/supabase-js": "^2.50.2", "axios": "1.6.7", "next": "13.4.19", "react": "18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "20.11.5", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "autoprefixer": "^10.4.16", "eslint": "8.56.0", "eslint-config-next": "13.4.19", "postcss": "^8.4.31", "prettier": "3.0.3", "tailwindcss": "^3.4.12", "typescript": "5.3.3"}, "keywords": ["fishivo", "fishing", "social", "platform", "web", "nextjs"]}