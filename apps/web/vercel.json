{"$schema": "https://openapi.vercel.sh/vercel.json", "framework": "nextjs", "buildCommand": "cd ../.. && turbo run build --filter=@fishivo/web", "installCommand": "cd ../.. && yarn install", "outputDirectory": ".next", "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "env": {"NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "NEXT_PUBLIC_MAPBOX_TOKEN": "@next_public_mapbox_token", "NEXT_PUBLIC_GOOGLE_CLIENT_ID": "@next_public_google_client_id", "NEXT_PUBLIC_FACEBOOK_APP_ID": "@next_public_facebook_app_id"}, "build": {"env": {"SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key"}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}], "regions": ["fra1"]}