import './globals.css'
import type { Metadata } from 'next'
import { ThemeProvider } from '../components/ThemeProvider'

export const metadata: Metadata = {
  title: 'Fishivo - Balıkçıların Sosyal Ağı',
  description: 'Avlarını paylaş, balıkçı topluluğuna katıl, yeni teknikler öğren. En büyük balıkçılık sosyal platformu.',
  keywords: 'balıkçılık, fishing, sosyal ağ, balık avı, balık paylaşım, balıkçı topluluğu',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr" className="scroll-smooth" suppressHydrationWarning>
      <body className="antialiased">
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
} 