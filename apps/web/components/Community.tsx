'use client'

export default function Community() {
  const testimonials = [
    {
      name: '<PERSON><PERSON> Yılmaz',
      location: 'İstanbul, Türkiye',
      avatar: '👨‍🦳',
      text: 'Fishivo sayesinde İstanbul\'un en iyi avlama noktalarını öğrendim. Topluluk çok yardımsever!',
      catch: '<PERSON><PERSON> - 2.5kg',
      isPro: true
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      location: 'Antalya, Türkiye',
      avatar: '👨‍🎣',
      text: 'Hava durumu özelliği gerçekten işe yarıyor. Artık boş dönmüyorum.',
      catch: 'Çupra - 1.8kg',
      isPro: false
    },
    {
      name: '<PERSON><PERSON>',
      location: 'İzmir, Türkiye',
      avatar: '👩‍🦰',
      text: 'Kad<PERSON>n balıkçı olarak bu toplulukta kendimi evimde hissediyorum. Harika bir platform!',
      catch: 'Kefal - 1.2kg',
      isPro: true
    }
  ]

  return (
    <section id="community" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full mb-8">
            <span className="w-2 h-2 bg-nature rounded-full mr-2 animate-pulse"></span>
            Topluluk
          </div>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Türkiye'nin En Büyük
            <span className="gradient-text block">Balıkçı Topluluğu</span>
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Binlerce deneyimli balıkçı ile tanış, bilgi paylaş ve balık avı tutkunlarıyla birlikte ol.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {[
            { icon: '👥', count: '50K+', label: 'Aktif Üye', color: 'text-blue-500' },
            { icon: '🎣', count: '100K+', label: 'Paylaşılan Av', color: 'text-green-500' },
            { icon: '📍', count: '1K+', label: 'Avlama Noktası', color: 'text-red-500' }, 
            { icon: '💬', count: '25K+', label: 'Günlük Yorum', color: 'text-purple-500' }
          ].map((stat, index) => (
            <div key={index}
                 className="text-center p-6 rounded-2xl shadow-md">
              <div className="text-4xl mb-3">{stat.icon}</div>
              <div className={`text-2xl font-bold ${stat.color} mb-1`}>{stat.count}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index}
                 className="p-8 rounded-2xl shadow-md">
              {/* Stars */}
              <div className="flex items-center mb-4">
                {[...Array(5)].map((_, i) => (
                  <svg key={i} className="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>

              <blockquote className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                "{testimonial.text}"
              </blockquote>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary 
                                 rounded-full flex items-center justify-center text-white text-xl">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-gray-900 dark:text-white">
                        {testimonial.name}
                      </h4>
                      {testimonial.isPro && (
                        <span className="px-2 py-1 bg-gradient-to-r from-yellow-400 to-yellow-600 
                                       text-white text-xs font-bold rounded-full">PRO</span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {testimonial.location}
                    </p>
                  </div>
                </div>
              </div>

              <div className="absolute top-4 right-4">
                <div className="px-3 py-1 text-fish font-semibold rounded-full text-xs">
                  🎣 {testimonial.catch}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
} 