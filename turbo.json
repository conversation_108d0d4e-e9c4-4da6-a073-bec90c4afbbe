{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local", "**/.env"], "globalEnv": ["NODE_ENV", "NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY"], "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "build/**", "android/app/build/**"], "env": ["NEXT_PUBLIC_SUPABASE_URL", "NEXT_PUBLIC_SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE_KEY"]}, "build:android": {"dependsOn": ["^build"], "outputs": ["android/app/build/**"], "cache": false}, "dev": {"cache": false, "persistent": true}, "start": {"dependsOn": ["build"], "cache": false, "persistent": true}, "lint": {"outputs": []}, "test": {"outputs": ["coverage/**"]}, "clean": {"cache": false}, "android": {"cache": false, "persistent": true}, "ios": {"cache": false, "persistent": true}, "pods": {"cache": false}}}