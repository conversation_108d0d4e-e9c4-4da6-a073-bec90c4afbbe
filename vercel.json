{"$schema": "https://openapi.vercel.sh/vercel.json", "version": 2, "buildCommand": "turbo run build --filter=@fishivo/web", "outputDirectory": "apps/web/.next", "installCommand": "yarn install", "framework": "nextjs", "functions": {"apps/web/app/api/**/*.ts": {"runtime": "nodejs18.x"}}, "env": {"TURBO_TOKEN": "@turbo_token", "TURBO_TEAM": "@turbo_team"}, "build": {"env": {"NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@next_public_supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "NEXT_PUBLIC_MAPBOX_TOKEN": "@next_public_mapbox_token", "NEXT_PUBLIC_GOOGLE_CLIENT_ID": "@next_public_google_client_id", "NEXT_PUBLIC_FACEBOOK_APP_ID": "@next_public_facebook_app_id"}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}], "regions": ["fra1"], "github": {"silent": true}}