# =============================================================================
# FISHIVO PROJECT GITIGNORE
# =============================================================================

# =============================================================================
# ENVIRONMENT & SECRETS
# =============================================================================
# Environment files (NEVER commit these!)
.env
.env.local
.env.development
.env.staging
.env.production
.env.test

# Backup env files
.env.backup
.env.*.backup

# =============================================================================
# NODE.JS & NPM
# =============================================================================
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# =============================================================================
# REACT NATIVE
# =============================================================================
# Metro bundler cache
.metro-health-check*

# React Native packager cache
.react-native/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# =============================================================================
# EXPO (if used)
# =============================================================================
.expo/
dist/
web-build/

# =============================================================================
# ANDROID
# =============================================================================
# Built application files
*.apk
*.aar
*.ap_
*.aab

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# IntelliJ
*.iml
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
.idea/caches

# Keystore files
*.jks
*.keystore

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild
.cxx/

# Google Services (e.g. APIs or Firebase)
google-services.json

# =============================================================================
# IOS
# =============================================================================
# Xcode
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# CocoaPods
Pods/

# Carthage
Carthage/Build

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots/**/*.png
fastlane/test_output

# =============================================================================
# NEXT.JS (Web Frontend)
# =============================================================================
# Next.js build output
.next/
out/

# Vercel
.vercel

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# VS Code
.vscode/

# JetBrains IDEs
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# =============================================================================
# OPERATING SYSTEM
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# =============================================================================
# LOGS & DATABASES
# =============================================================================
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# =============================================================================
# UPLOADS & TEMPORARY FILES
# =============================================================================
# Upload directories
uploads/
temp/
tmp/

# Backup files
*.backup
*.bak
*.swp
*.swo

# =============================================================================
# TESTING
# =============================================================================
# Test coverage
coverage/
.nyc_output/

# Jest
jest_coverage/

# =============================================================================
# PRODUCTION & DEPLOYMENT
# =============================================================================
# Production builds
dist/
build/

# Docker
.dockerignore

# =============================================================================
# CUSTOM PROJECT FILES
# =============================================================================
# Custom ignore patterns for Fishivo
test-*.txt
*.test.js
small-test.txt 