#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function checkDatabase() {
  try {
    console.log('🔍 Checking Supabase database status...');
    console.log('URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    
    // Try to check existing tables
    const tables = ['users', 'fish_species', 'spots', 'posts', 'unit_categories', 'unit_definitions'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) {
          console.log(`❌ Table '${table}': ${error.message}`);
        } else {
          console.log(`✅ Table '${table}': OK (${data.length} sample records checked)`);
        }
      } catch (err) {
        console.log(`❌ Table '${table}': ${err.message}`);
      }
    }
    
    // Try to get some basic stats
    try {
      const { data: userData } = await supabase.from('users').select('*', { count: 'exact' });
      const { data: postsData } = await supabase.from('posts').select('*', { count: 'exact' });
      
      console.log('\n📊 Database Statistics:');
      console.log(`   Users: ${userData?.length || 0}`);
      console.log(`   Posts: ${postsData?.length || 0}`);
    } catch (err) {
      console.log('⚠️  Could not get statistics:', err.message);
    }
    
  } catch (error) {
    console.log('❌ Database check failed:', error.message);
  }
}

checkDatabase(); 