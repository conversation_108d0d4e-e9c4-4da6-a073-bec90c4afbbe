#!/usr/bin/env node

/**
 * Google Auth Configuration Checker
 * This script checks the configuration for Google Sign-In with Supabase
 */

const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env') });

// Configuration variables to check
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'NEXT_PUBLIC_GOOGLE_CLIENT_ID',
  'GOOGLE_WEB_CLIENT_ID'
];

// Optional but recommended variables
const recommendedEnvVars = [
  'SUPABASE_SERVICE_ROLE_KEY',
  'GOOGLE_CLIENT_SECRET',
  'GOOGLE_CLIENT_ID'
];

// Check environment variables
console.log('🔍 Checking environment variables...');
const missingRequired = [];
const missingRecommended = [];

requiredEnvVars.forEach(varName => {
  if (!process.env[varName]) {
    missingRequired.push(varName);
  }
});

recommendedEnvVars.forEach(varName => {
  if (!process.env[varName]) {
    missingRecommended.push(varName);
  }
});

// Display results for environment variables
if (missingRequired.length > 0) {
  console.error('❌ Missing required environment variables:');
  missingRequired.forEach(varName => console.error(`  - ${varName}`));
} else {
  console.log('✅ All required environment variables are set');
}

if (missingRecommended.length > 0) {
  console.warn('⚠️  Missing recommended environment variables:');
  missingRecommended.forEach(varName => console.warn(`  - ${varName}`));
} else {
  console.log('✅ All recommended environment variables are set');
}

// Check Supabase connection
async function checkSupabaseConnection() {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.error('❌ Cannot check Supabase connection due to missing credentials');
    return;
  }

  try {
    console.log('🔍 Checking Supabase connection...');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    );
    
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('❌ Supabase connection error:', error.message);
    } else {
      console.log('✅ Supabase connection successful');
    }
  } catch (err) {
    console.error('❌ Error checking Supabase connection:', err.message);
  }
}

// Check database schema
async function checkDatabaseSchema() {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Cannot check database schema due to missing credentials');
    return;
  }

  try {
    console.log('🔍 Checking database schema for OAuth fields...');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    // Check if the users table has the required OAuth columns
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql: `
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name IN ('google_id', 'facebook_id', 'provider');
      `
    });
    
    if (error) {
      console.error('❌ Database schema check error:', error.message);
      return;
    }
    
    const columns = data || [];
    const hasGoogleId = columns.some(col => col.column_name === 'google_id');
    const hasFacebookId = columns.some(col => col.column_name === 'facebook_id');
    const hasProvider = columns.some(col => col.column_name === 'provider');
    
    if (hasGoogleId && hasFacebookId && hasProvider) {
      console.log('✅ Database schema has all required OAuth fields');
    } else {
      console.warn('⚠️  Missing OAuth fields in database schema:');
      if (!hasGoogleId) console.warn('  - google_id');
      if (!hasFacebookId) console.warn('  - facebook_id');
      if (!hasProvider) console.warn('  - provider');
      
      console.log('\n📝 Run the OAuth fields migration:');
      console.log('  node scripts/run-oauth-migration.js');
    }
  } catch (err) {
    console.error('❌ Error checking database schema:', err.message);
  }
}

// Check Google configuration in Supabase
async function checkGoogleConfig() {
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Cannot check Google configuration due to missing credentials');
    return;
  }

  try {
    console.log('🔍 Checking Google OAuth configuration in Supabase...');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );
    
    // This is a simplified check - in a real scenario, you'd need to use Supabase Management API
    // which requires additional permissions
    console.log('⚠️  Manual verification required:');
    console.log('  1. Go to https://app.supabase.com');
    console.log('  2. Select your project');
    console.log('  3. Navigate to Authentication > Providers');
    console.log('  4. Check if Google is enabled');
    console.log('  5. Verify Client ID and Client Secret are set correctly');
    console.log('  6. Verify the Web Client ID is in the Authorized Client IDs list');
  } catch (err) {
    console.error('❌ Error checking Google configuration:', err.message);
  }
}

// Run all checks
async function runAllChecks() {
  await checkSupabaseConnection();
  await checkDatabaseSchema();
  await checkGoogleConfig();
  
  console.log('\n📝 Summary:');
  if (missingRequired.length > 0) {
    console.error('❌ Fix missing required environment variables before proceeding');
  } else {
    console.log('✅ Basic configuration looks good');
    console.log('📱 You can now test Google Sign-In in your app');
  }
}

runAllChecks(); 