#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Tüm screen dosyalarını bul
const screenFiles = glob.sync('src/screens/*.tsx');

screenFiles.forEach(filePath => {
  console.log(`Processing: ${filePath}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Eğer ScreenContainer zaten import edilmişse atla
  if (content.includes('ScreenContainer')) {
    console.log(`  ✓ Already has ScreenContainer`);
    return;
  }
  
  // ScreenContainer import'unu ekle
  if (content.includes("import { theme }")) {
    content = content.replace(
      "import { theme }",
      "import { theme }\nimport { ScreenContainer }"
    );
  } else if (content.includes("from '../theme")) {
    content = content.replace(
      /from '\.\.\/theme[^']*';/,
      "$&\nimport { ScreenContainer } from '../components';"
    );
  } else {
    // Theme import'u yoksa, ilk import'tan sonra ekle
    const lines = content.split('\n');
    let insertIndex = -1;
    
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ') && !lines[i].includes('React')) {
        insertIndex = i + 1;
        break;
      }
    }
    
    if (insertIndex > -1) {
      lines.splice(insertIndex, 0, "import { ScreenContainer } from '../components';");
      content = lines.join('\n');
    }
  }
  
  console.log(`  ✓ Added ScreenContainer import`);
  
  // Dosyayı kaydet
  fs.writeFileSync(filePath, content);
});

console.log('\n✅ All screen files processed!');
console.log('\n📝 Next steps:');
console.log('1. Manually wrap main content with <ScreenContainer>');
console.log('2. Remove manual paddingHorizontal styles');
console.log('3. Test the app'); 