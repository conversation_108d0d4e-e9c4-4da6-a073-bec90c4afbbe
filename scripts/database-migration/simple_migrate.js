#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
// Ana dizindeki .env dosyasını yükle  
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runSQL(sql) {
  const { data, error } = await supabase.rpc('exec_sql', { sql });
  if (error) {
    // Try alternative method
    const { data: data2, error: error2 } = await supabase
      .from('_temp')
      .select('*')
      .limit(0);
    
    if (error2) {
      console.error('❌ SQL Error:', error.message);
      throw error;
    }
  }
  return data;
}

async function cleanup() {
  console.log('🧹 Cleaning up existing unit system tables...');
  
  const cleanupSQL = fs.readFileSync('cleanup_tables.sql', 'utf8');
  
  try {
    await runSQL(cleanupSQL);
    console.log('✅ Cleanup completed');
  } catch (error) {
    console.log('⚠️  Cleanup had some errors (this is normal if tables don\'t exist)');
  }
}

async function migrate() {
  console.log('🚀 Running unit system migrations...');
  
  try {
    // Run 007_unit_system.sql
    console.log('📄 Running 007_unit_system.sql...');
    const sql007 = fs.readFileSync('packages/database/migrations/007_unit_system.sql', 'utf8');
    await runSQL(sql007);
    console.log('✅ 007_unit_system.sql completed');
    
    // Run 008_unit_system_data.sql
    console.log('📄 Running 008_unit_system_data.sql...');
    const sql008 = fs.readFileSync('packages/database/migrations/008_unit_system_data.sql', 'utf8');
    await runSQL(sql008);
    console.log('✅ 008_unit_system_data.sql completed');
    
    console.log('🎉 All migrations completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

async function main() {
  const command = process.argv[2];
  
  if (command === 'cleanup') {
    await cleanup();
  } else if (command === 'migrate') {
    await migrate();
  } else if (command === 'full') {
    await cleanup();
    await migrate();
  } else {
    console.log('Usage:');
    console.log('  node simple_migrate.js cleanup  # Clean existing tables');
    console.log('  node simple_migrate.js migrate  # Run migrations');
    console.log('  node simple_migrate.js full     # Cleanup + migrate');
  }
}

main().catch(console.error); 