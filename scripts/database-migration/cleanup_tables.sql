-- =====================================================
-- CLEANUP SCRIPT - Remove existing unit system tables
-- =====================================================

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS unit_usage_analytics CASCADE;
DROP TABLE IF EXISTS unit_conversion_cache CASCADE;
DROP TABLE IF EXISTS regional_unit_defaults CASCADE;
DROP TABLE IF EXISTS unit_validation_rules CASCADE;
DROP TABLE IF EXISTS unit_conversion_rules CASCADE;
DROP TABLE IF EXISTS user_unit_preferences CASCADE;
DROP TABLE IF EXISTS unit_definitions CASCADE;
DROP TABLE IF EXISTS unit_categories CASCADE;

-- Drop any existing functions
DROP FUNCTION IF EXISTS update_user_unit_preferences_timestamp() CASCADE;
DROP FUNCTION IF EXISTS update_conversion_cache_hit() CASCADE;
DROP FUNCTION IF EXISTS clean_conversion_cache() CASCADE;

-- Clean up any existing policies
DROP POLICY IF EXISTS "Unit categories are viewable by everyone" ON unit_categories;
DROP POLICY IF EXISTS "Unit definitions are viewable by everyone" ON unit_definitions;
DROP POLICY IF EXISTS "Users can view their own unit preferences" ON user_unit_preferences;
DROP POLICY IF EXISTS "Users can update their own unit preferences" ON user_unit_preferences;
DROP POLICY IF EXISTS "Users can insert their own unit preferences" ON user_unit_preferences;
DROP POLICY IF EXISTS "Conversion cache is viewable by everyone" ON unit_conversion_cache;

-- Remove migration records for unit system
DELETE FROM migrations WHERE filename IN ('007_unit_system.sql', '008_unit_system_data.sql');

-- Success message
SELECT 'Unit system tables cleaned up successfully!' as status; 