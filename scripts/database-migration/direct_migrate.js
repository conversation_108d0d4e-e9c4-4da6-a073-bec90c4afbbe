#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');
// Ana dizindeki .env dosyasını yükle
require('dotenv').config({ path: require('path').join(__dirname, '../../.env') });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

function executeSQL(sql, description) {
  console.log(`📄 ${description}...`);
  
  // Split SQL into statements and execute each one
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i];
    if (statement.trim()) {
      try {
        const curlCommand = `curl -k -s -X POST "${supabaseUrl}/rest/v1/rpc/exec_sql" ` +
          `-H "apikey: ${serviceKey}" ` +
          `-H "Authorization: Bearer ${serviceKey}" ` +
          `-H "Content-Type: application/json" ` +
          `-d '{"sql": ${JSON.stringify(statement)}}'`;
        
        const result = execSync(curlCommand, { encoding: 'utf8' });
        
        if (result.includes('error') || result.includes('Error')) {
          console.log(`⚠️  Statement ${i+1}: ${result}`);
        } else {
          console.log(`✅ Statement ${i+1} executed`);
        }
      } catch (error) {
        console.log(`⚠️  Statement ${i+1} had issues (might be normal)`);
      }
    }
  }
}

async function main() {
  console.log('🚀 Starting direct Supabase migration...');
  
  try {
    // 1. Cleanup
    console.log('\n🧹 Step 1: Cleanup existing tables...');
    const cleanupSQL = fs.readFileSync('cleanup_tables.sql', 'utf8');
    executeSQL(cleanupSQL, 'Cleaning up existing unit system tables');
    
    // 2. Create tables
    console.log('\n🏗️  Step 2: Creating unit system tables...');
    const sql007 = fs.readFileSync('packages/database/migrations/007_unit_system.sql', 'utf8');
    executeSQL(sql007, 'Creating unit system tables');
    
    // 3. Insert data
    console.log('\n📊 Step 3: Inserting unit system data...');
    const sql008 = fs.readFileSync('packages/database/migrations/008_unit_system_data.sql', 'utf8');
    executeSQL(sql008, 'Inserting unit system data');
    
    console.log('\n🎉 Migration completed! Verifying...');
    
    // 4. Verify
    const verifyCommand = `curl -k -s "${supabaseUrl}/rest/v1/unit_categories?select=*" ` +
      `-H "apikey: ${serviceKey}" ` +
      `-H "Authorization: Bearer ${serviceKey}"`;
    
    const verification = execSync(verifyCommand, { encoding: 'utf8' });
    console.log('📋 Unit categories:', verification);
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
  }
}

main(); 