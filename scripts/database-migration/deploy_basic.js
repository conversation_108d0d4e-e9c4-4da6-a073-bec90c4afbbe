const https = require('https');
const fs = require('fs');
const path = require('path');

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://kqvhyiexevsdlhzcnhrc.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

// Execute SQL via Supabase
async function executeSQL(sql, description = 'SQL Query') {
  return new Promise((resolve, reject) => {
    console.log(`🔄 Executing: ${description}`);
    
    const postData = JSON.stringify({ sql: sql });
    const url = new URL(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`);
    
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY
      },
      rejectUnauthorized: false
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            console.log(`✅ ${description} - SUCCESS`);
            resolve(JSON.parse(data));
          } else {
            console.error(`❌ ${description} - FAILED (${res.statusCode}): ${data}`);
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        } catch (error) {
          console.error(`❌ ${description} - PARSE ERROR:`, error.message);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ ${description} - NETWORK ERROR:`, error.message);
      reject(error);
    });

    req.setTimeout(30000);
    req.write(postData);
    req.end();
  });
}

async function deployBasicUnits() {
  try {
    console.log('🎣 FISHIVO BASIC UNITS DEPLOYMENT');
    console.log('===================================');
    
    // Read the SQL file
    const sqlFile = path.join(__dirname, 'unit_basic_tables.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    console.log(`📖 Loaded SQL file: ${sql.length} characters`);
    
    // Execute the migration
    await executeSQL(sql, 'Basic Units Tables Migration');
    
    console.log('\n🔍 Verifying tables...');
    
    // Verify tables exist
    const verifySQL = `
      SELECT tablename, schemaname 
      FROM pg_tables 
      WHERE schemaname = 'public' 
      AND tablename IN ('unit_categories', 'unit_definitions', 'regional_unit_defaults')
      ORDER BY tablename;
    `;
    
    await executeSQL(verifySQL, 'Verify Tables');
    
    console.log('\n🎉 Basic Units System deployed successfully!');
    
  } catch (error) {
    console.error('\n💥 Deployment failed:', error.message);
    process.exit(1);
  }
}

deployBasicUnits(); 