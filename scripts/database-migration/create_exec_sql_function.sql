-- =====================================================
-- CREATE exec_sql FUNCTION FOR SUPABASE
-- =====================================================

-- Create the exec_sql function that allows dynamic SQL execution
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result json;
BEGIN
    -- Execute the dynamic SQL
    EXECUTE sql;
    
    -- Return success message
    result := json_build_object('status', 'success', 'message', 'SQL executed successfully');
    RETURN result;
EXCEPTION
    WHEN OTHERS THEN
        -- Return error message
        result := json_build_object(
            'status', 'error', 
            'message', SQLERRM,
            'code', SQLSTATE
        );
        RETURN result;
END;
$$;

-- <PERSON> execute permission to service role
GRANT EXECUTE ON FUNCTION public.exec_sql(text) TO service_role;

-- <PERSON> execute permission to authenticated users (optional, for admin use)
GRANT EXECUTE ON FUNCTION public.exec_sql(text) TO authenticated;

-- Create a comment
COMMENT ON FUNCTION public.exec_sql(text) IS 'Execute dynamic SQL statements - USE WITH CAUTION';

-- Test the function
SELECT public.exec_sql('SELECT 1 as test') as test_result; 