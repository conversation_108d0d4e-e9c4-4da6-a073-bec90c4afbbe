-- =====================================================
-- MIGRATION 007: UNIT SYSTEM
-- Created: 2024-01-20
-- Description: Database-driven unit conversion and user preferences system
-- =====================================================

-- =====================================================
-- 1. UNIT DEFINITIONS SYSTEM
-- =====================================================

-- Unit categories (weight, length, temperature, etc.)
CREATE TABLE IF NOT EXISTS unit_categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_en TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  base_unit TEXT NOT NULL, -- The base unit for this category
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Unit definitions (kg, lbs, cm, inch, etc.)
CREATE TABLE IF NOT EXISTS unit_definitions (
  id TEXT PRIMARY KEY,
  category_id TEXT REFERENCES unit_categories(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  name_en TEXT NOT NULL,
  symbol TEXT NOT NULL,
  
  -- Conversion properties
  is_base_unit BOOLEAN DEFAULT FALSE,
  conversion_factor DECIMAL(20,10), -- Multiplier to convert to base unit
  conversion_formula TEXT, -- For complex conversions like temperature
  reverse_formula TEXT, -- For reverse conversions
  
  -- Display properties
  precision_digits INTEGER DEFAULT 2,
  min_value DECIMAL(20,10),
  max_value DECIMAL(20,10),
  
  -- Regional usage
  regions JSONB DEFAULT '[]', -- ['TR', 'US', 'EU', 'UK']
  popularity INTEGER DEFAULT 0, -- 0-100 popularity score
  default_for_regions JSONB DEFAULT '[]', -- Regions where this is default
  
  -- Metadata
  use_case TEXT, -- 'general', 'marine', 'scientific', 'traditional'
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(category_id, symbol),
  CHECK (conversion_factor > 0 OR conversion_formula IS NOT NULL)
);

-- =====================================================
-- 2. USER PREFERENCES SYSTEM
-- =====================================================

-- User unit preferences
CREATE TABLE IF NOT EXISTS user_unit_preferences (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Unit preferences by category
  weight_unit TEXT REFERENCES unit_definitions(id),
  length_unit TEXT REFERENCES unit_definitions(id),
  distance_unit TEXT REFERENCES unit_definitions(id),
  temperature_unit TEXT REFERENCES unit_definitions(id),
  depth_unit TEXT REFERENCES unit_definitions(id),
  speed_unit TEXT REFERENCES unit_definitions(id),
  pressure_unit TEXT REFERENCES unit_definitions(id),
  
  -- Regional context
  region TEXT DEFAULT 'TR', -- User's region/country
  auto_detect_region BOOLEAN DEFAULT TRUE,
  
  -- Preferences metadata
  last_updated_at TIMESTAMPTZ DEFAULT NOW(),
  updated_by_user BOOLEAN DEFAULT FALSE, -- FALSE if auto-set by region
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- =====================================================
-- 3. CONVERSION RULES & VALIDATION
-- =====================================================

-- Conversion rules for automatic unit switching (1000g -> 1kg)
CREATE TABLE IF NOT EXISTS unit_conversion_rules (
  id BIGSERIAL PRIMARY KEY,
  category_id TEXT REFERENCES unit_categories(id) ON DELETE CASCADE,
  from_unit TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  to_unit TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  
  -- Rule conditions
  threshold_value DECIMAL(20,10) NOT NULL, -- When to trigger conversion
  rule_type TEXT NOT NULL CHECK (rule_type IN ('auto_switch', 'suggestion', 'validation')),
  
  -- Context
  context TEXT, -- 'input', 'display', 'storage'
  priority INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Validation rules for unit values
CREATE TABLE IF NOT EXISTS unit_validation_rules (
  id BIGSERIAL PRIMARY KEY,
  unit_id TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  
  -- Validation constraints
  min_value DECIMAL(20,10),
  max_value DECIMAL(20,10),
  precision_digits INTEGER DEFAULT 2,
  
  -- Context-specific rules
  context TEXT, -- 'catch_weight', 'fish_length', 'water_temperature', etc.
  error_message TEXT,
  warning_message TEXT,
  
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 4. REGIONAL DEFAULTS SYSTEM
-- =====================================================

-- Regional unit defaults
CREATE TABLE IF NOT EXISTS regional_unit_defaults (
  id BIGSERIAL PRIMARY KEY,
  region_code TEXT NOT NULL, -- 'TR', 'US', 'UK', 'EU', etc.
  region_name TEXT NOT NULL,
  
  -- Default units by category
  weight_unit TEXT REFERENCES unit_definitions(id),
  length_unit TEXT REFERENCES unit_definitions(id),
  distance_unit TEXT REFERENCES unit_definitions(id),
  temperature_unit TEXT REFERENCES unit_definitions(id),
  depth_unit TEXT REFERENCES unit_definitions(id),
  speed_unit TEXT REFERENCES unit_definitions(id),
  pressure_unit TEXT REFERENCES unit_definitions(id),
  
  -- Regional metadata
  currency TEXT DEFAULT 'USD',
  date_format TEXT DEFAULT 'DD/MM/YYYY',
  number_format JSONB DEFAULT '{"decimal": ".", "thousands": ","}',
  
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(region_code)
);

-- =====================================================
-- 5. CONVERSION CACHE & PERFORMANCE
-- =====================================================

-- Conversion cache for frequently used conversions
CREATE TABLE IF NOT EXISTS unit_conversion_cache (
  id BIGSERIAL PRIMARY KEY,
  from_unit TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  to_unit TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  from_value DECIMAL(20,10),
  to_value DECIMAL(20,10),
  
  -- Cache metadata
  hit_count INTEGER DEFAULT 1,
  last_accessed TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(from_unit, to_unit, from_value)
);

-- =====================================================
-- 6. UNIT SYSTEM ANALYTICS
-- =====================================================

-- Track unit usage for analytics
CREATE TABLE IF NOT EXISTS unit_usage_analytics (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  unit_id TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  category_id TEXT REFERENCES unit_categories(id) ON DELETE CASCADE,
  
  -- Usage context
  action_type TEXT NOT NULL, -- 'input', 'display', 'conversion'
  context TEXT, -- 'catch_entry', 'weather_display', 'profile_view'
  
  -- Session info
  session_id TEXT,
  ip_address INET,
  user_agent TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 7. INDEXES FOR PERFORMANCE
-- =====================================================

-- Unit categories indexes
CREATE INDEX IF NOT EXISTS idx_unit_categories_active ON unit_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_unit_categories_sort ON unit_categories(sort_order);

-- Unit definitions indexes
CREATE INDEX IF NOT EXISTS idx_unit_definitions_category ON unit_definitions(category_id);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_base_unit ON unit_definitions(is_base_unit);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_active ON unit_definitions(is_active);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_regions ON unit_definitions USING GIN(regions);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_popularity ON unit_definitions(popularity DESC);

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_unit_preferences_user_id ON user_unit_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_unit_preferences_region ON user_unit_preferences(region);
CREATE INDEX IF NOT EXISTS idx_user_unit_preferences_updated ON user_unit_preferences(updated_at DESC);

-- Conversion rules indexes
CREATE INDEX IF NOT EXISTS idx_unit_conversion_rules_category ON unit_conversion_rules(category_id);
CREATE INDEX IF NOT EXISTS idx_unit_conversion_rules_from_to ON unit_conversion_rules(from_unit, to_unit);
CREATE INDEX IF NOT EXISTS idx_unit_conversion_rules_active ON unit_conversion_rules(is_active);

-- Validation rules indexes
CREATE INDEX IF NOT EXISTS idx_unit_validation_rules_unit ON unit_validation_rules(unit_id);
CREATE INDEX IF NOT EXISTS idx_unit_validation_rules_context ON unit_validation_rules(context);

-- Regional defaults indexes
CREATE INDEX IF NOT EXISTS idx_regional_unit_defaults_region ON regional_unit_defaults(region_code);
CREATE INDEX IF NOT EXISTS idx_regional_unit_defaults_active ON regional_unit_defaults(is_active);

-- Conversion cache indexes
CREATE INDEX IF NOT EXISTS idx_unit_conversion_cache_from_to ON unit_conversion_cache(from_unit, to_unit);
CREATE INDEX IF NOT EXISTS idx_unit_conversion_cache_accessed ON unit_conversion_cache(last_accessed DESC);
CREATE INDEX IF NOT EXISTS idx_unit_conversion_cache_hits ON unit_conversion_cache(hit_count DESC);

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_user ON unit_usage_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_unit ON unit_usage_analytics(unit_id);
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_category ON unit_usage_analytics(category_id);
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_created ON unit_usage_analytics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_context ON unit_usage_analytics(context);

-- =====================================================
-- 8. TRIGGERS & FUNCTIONS
-- =====================================================

-- Function to update user preferences timestamp
CREATE OR REPLACE FUNCTION update_user_unit_preferences_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  NEW.last_updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for user preferences updates
CREATE TRIGGER trigger_update_user_unit_preferences_timestamp
  BEFORE UPDATE ON user_unit_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_user_unit_preferences_timestamp();

-- Function to update conversion cache hit count
CREATE OR REPLACE FUNCTION update_conversion_cache_hit()
RETURNS TRIGGER AS $$
BEGIN
  NEW.hit_count = OLD.hit_count + 1;
  NEW.last_accessed = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to clean old cache entries
CREATE OR REPLACE FUNCTION clean_conversion_cache()
RETURNS void AS $$
BEGIN
  -- Keep only top 10000 most accessed entries
  DELETE FROM unit_conversion_cache
  WHERE id NOT IN (
    SELECT id FROM unit_conversion_cache
    ORDER BY hit_count DESC, last_accessed DESC
    LIMIT 10000
  );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 9. RLS POLICIES
-- =====================================================

-- Enable RLS on all unit system tables
ALTER TABLE unit_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_unit_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_conversion_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_validation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE regional_unit_defaults ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_conversion_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_usage_analytics ENABLE ROW LEVEL SECURITY;

-- Unit categories and definitions are public (read-only)
CREATE POLICY "Unit categories are viewable by everyone" ON unit_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Unit definitions are viewable by everyone" ON unit_definitions
  FOR SELECT USING (is_active = true);

-- User preferences are private to each user
CREATE POLICY "Users can view their own unit preferences" ON user_unit_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own unit preferences" ON user_unit_preferences
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own unit preferences" ON user_unit_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Conversion rules and validation rules are public (read-only)
CREATE POLICY "Conversion rules are viewable by everyone" ON unit_conversion_rules
  FOR SELECT USING (is_active = true);

CREATE POLICY "Validation rules are viewable by everyone" ON unit_validation_rules
  FOR SELECT USING (is_active = true);

-- Regional defaults are public (read-only)
CREATE POLICY "Regional defaults are viewable by everyone" ON regional_unit_defaults
  FOR SELECT USING (is_active = true);

-- Conversion cache is public (read-only for users, write for system)
CREATE POLICY "Conversion cache is viewable by everyone" ON unit_conversion_cache
  FOR SELECT USING (true);

-- Analytics are private (only system can write)
CREATE POLICY "Users can view their own usage analytics" ON unit_usage_analytics
  FOR SELECT USING (auth.uid() = user_id);

-- =====================================================
-- 10. COMMENTS & DOCUMENTATION
-- =====================================================

COMMENT ON TABLE unit_categories IS 'Defines unit categories like weight, length, temperature';
COMMENT ON TABLE unit_definitions IS 'Defines individual units with conversion factors and properties';
COMMENT ON TABLE user_unit_preferences IS 'Stores each user''s preferred units by category';
COMMENT ON TABLE unit_conversion_rules IS 'Rules for automatic unit conversions and suggestions';
COMMENT ON TABLE unit_validation_rules IS 'Validation constraints for unit values in different contexts';
COMMENT ON TABLE regional_unit_defaults IS 'Default unit preferences by region/country';
COMMENT ON TABLE unit_conversion_cache IS 'Cache for frequently used unit conversions';
COMMENT ON TABLE unit_usage_analytics IS 'Analytics data for unit system usage patterns';

COMMENT ON COLUMN unit_definitions.conversion_factor IS 'Multiplier to convert from this unit to base unit';
COMMENT ON COLUMN unit_definitions.conversion_formula IS 'Formula for complex conversions (e.g., temperature)';
COMMENT ON COLUMN unit_definitions.precision_digits IS 'Number of decimal places to display';
COMMENT ON COLUMN user_unit_preferences.auto_detect_region IS 'Whether to auto-update units based on detected region';
COMMENT ON COLUMN unit_conversion_rules.threshold_value IS 'Value threshold that triggers the conversion rule'; 

-- =====================================================
-- UNIT SYSTEM DATA
-- =====================================================
-- =====================================================
-- MIGRATION 008: UNIT SYSTEM DEFAULT DATA
-- Created: 2024-01-20
-- Description: Default data for unit system (categories, units, regional defaults)
-- =====================================================

-- =====================================================
-- 1. UNIT CATEGORIES
-- =====================================================

INSERT INTO unit_categories (id, name, name_en, description, icon, base_unit, sort_order) VALUES
('weight', 'Ağırlık', 'Weight', 'Balık ağırlığı ve ekipman ağırlığı ölçü birimleri', 'scale', 'kg', 1),
('length', 'Uzunluk', 'Length', 'Balık boyu ve ekipman uzunluğu ölçü birimleri', 'ruler', 'cm', 2),
('distance', 'Mesafe', 'Distance', 'Konum ve seyahat mesafesi ölçü birimleri', 'map-pin', 'km', 3),
('temperature', 'Sıcaklık', 'Temperature', 'Su sıcaklığı ve hava sıcaklığı ölçü birimleri', 'thermometer', 'celsius', 4),
('depth', 'Derinlik', 'Depth', 'Su derinliği ölçü birimleri', 'arrow-down', 'meters', 5),
('speed', 'Hız', 'Speed', 'Rüzgar hızı ve tekne hızı ölçü birimleri', 'zap', 'kmh', 6),
('pressure', 'Basınç', 'Pressure', 'Hava basıncı ölçü birimleri', 'gauge', 'hpa', 7)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  description = EXCLUDED.description,
  icon = EXCLUDED.icon,
  base_unit = EXCLUDED.base_unit,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 2. WEIGHT UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('kg', 'weight', 'Kilogram', 'Kilogram', 'kg', true, 1.0, 2, 0.001, 1000, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 1),
('g', 'weight', 'Gram', 'Gram', 'g', false, 0.001, 0, 1, 1000000, '["TR", "EU", "METRIC", "GLOBAL"]', 60, '[]', 2),
('lbs', 'weight', 'Pound', 'Pound', 'lbs', false, 2.20462, 2, 0.002, 2204, '["US", "UK", "IMPERIAL"]', 75, '["US", "UK", "CA"]', 3),
('oz', 'weight', 'Ons', 'Ounce', 'oz', false, 35.274, 1, 0.035, 35274, '["US", "UK", "IMPERIAL"]', 45, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 3. LENGTH UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('cm', 'length', 'Santimetre', 'Centimeter', 'cm', true, 1.0, 1, 0.1, 1000, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 1),
('m', 'length', 'Metre', 'Meter', 'm', false, 100.0, 2, 0.001, 10, '["TR", "EU", "METRIC", "GLOBAL"]', 70, '[]', 2),
('inch', 'length', 'İnç', 'Inch', 'in', false, 0.393701, 1, 0.04, 394, '["US", "UK", "IMPERIAL"]', 75, '["US", "UK", "CA"]', 3),
('ft', 'length', 'Feet', 'Feet', 'ft', false, 3.28084, 1, 0.003, 33, '["US", "UK", "IMPERIAL"]', 60, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 4. DISTANCE UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('km', 'distance', 'Kilometre', 'Kilometer', 'km', true, 1.0, 2, 0.001, 50000, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 1),
('m_dist', 'distance', 'Metre', 'Meter', 'm', false, 0.001, 0, 1, 50000000, '["TR", "EU", "METRIC", "GLOBAL"]', 60, '[]', 2),
('miles', 'distance', 'Mil', 'Miles', 'mi', false, 0.621371, 2, 0.001, 31069, '["US", "UK", "IMPERIAL"]', 75, '["US", "UK", "CA"]', 3),
('nm', 'distance', 'Deniz Mili', 'Nautical Mile', 'nm', false, 0.539957, 2, 0.001, 26998, '["MARINE", "AVIATION"]', 50, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 5. TEMPERATURE UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, conversion_formula, reverse_formula, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('celsius', 'temperature', 'Santigrat', 'Celsius', '°C', true, NULL, NULL, NULL, 1, -50, 60, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP", "UK"]', 1),
('fahrenheit', 'temperature', 'Fahrenheit', 'Fahrenheit', '°F', false, NULL, '(C * 9/5) + 32', '(F - 32) * 5/9', 1, -58, 140, '["US", "IMPERIAL"]', 70, '["US"]', 2)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  conversion_formula = EXCLUDED.conversion_formula,
  reverse_formula = EXCLUDED.reverse_formula,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 6. DEPTH UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, use_case, sort_order) VALUES
('meters', 'depth', 'Metre', 'Meter', 'm', true, 1.0, 1, 0.1, 11000, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 'general', 1),
('feet', 'depth', 'Feet', 'Feet', 'ft', false, 3.28084, 1, 0.3, 36000, '["US", "UK", "IMPERIAL"]', 70, '["US", "UK", "CA"]', 'general', 2),
('fathoms', 'depth', 'Kulaç', 'Fathom', 'fm', false, 0.546806649, 1, 0.05, 6000, '["MARINE", "TRADITIONAL"]', 25, '[]', 'marine_depth', 3)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  use_case = EXCLUDED.use_case,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 7. SPEED UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('kmh', 'speed', 'Kilometre/Saat', 'Kilometer per Hour', 'km/h', true, 1.0, 1, 0, 500, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 1),
('mph', 'speed', 'Mil/Saat', 'Miles per Hour', 'mph', false, 1.60934, 1, 0, 311, '["US", "UK", "IMPERIAL"]', 75, '["US", "UK", "CA"]', 2),
('knots', 'speed', 'Knot', 'Knots', 'kn', false, 1.852, 1, 0, 270, '["MARINE", "AVIATION"]', 60, '[]', 3),
('ms', 'speed', 'Metre/Saniye', 'Meter per Second', 'm/s', false, 0.277777778, 1, 0, 139, '["SCIENTIFIC", "METRIC"]', 40, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 8. PRESSURE UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('hpa', 'pressure', 'Hektopaskal', 'Hectopascal', 'hPa', true, 1.0, 0, 800, 1100, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP", "UK"]', 1),
('mbar', 'pressure', 'Milibar', 'Millibar', 'mbar', false, 1.0, 0, 800, 1100, '["EU", "METRIC"]', 70, '[]', 2),
('inhg', 'pressure', 'İnç Civa', 'Inches of Mercury', 'inHg', false, 33.8639, 2, 23.62, 32.48, '["US", "IMPERIAL"]', 65, '["US"]', 3),
('mmhg', 'pressure', 'Milimetre Civa', 'Millimeters of Mercury', 'mmHg', false, 1.33322, 0, 600, 825, '["MEDICAL", "SCIENTIFIC"]', 40, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 9. REGIONAL DEFAULTS
-- =====================================================

INSERT INTO regional_unit_defaults (region_code, region_name, weight_unit, length_unit, distance_unit, temperature_unit, depth_unit, speed_unit, pressure_unit, currency, date_format, number_format) VALUES
('TR', 'Türkiye', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'TRY', 'DD/MM/YYYY', '{"decimal": ",", "thousands": "."}'),
('US', 'United States', 'lbs', 'inch', 'miles', 'fahrenheit', 'feet', 'mph', 'inhg', 'USD', 'MM/DD/YYYY', '{"decimal": ".", "thousands": ","}'),
('UK', 'United Kingdom', 'lbs', 'inch', 'miles', 'celsius', 'feet', 'mph', 'hpa', 'GBP', 'DD/MM/YYYY', '{"decimal": ".", "thousands": ","}'),
('EU', 'European Union', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'EUR', 'DD/MM/YYYY', '{"decimal": ",", "thousands": "."}'),
('CA', 'Canada', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'CAD', 'DD/MM/YYYY', '{"decimal": ".", "thousands": ","}'),
('AU', 'Australia', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'AUD', 'DD/MM/YYYY', '{"decimal": ".", "thousands": ","}'),
('JP', 'Japan', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'JPY', 'YYYY/MM/DD', '{"decimal": ".", "thousands": ","}'),
('GLOBAL', 'Global Default', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'USD', 'DD/MM/YYYY', '{"decimal": ".", "thousands": ","}')
ON CONFLICT (region_code) DO UPDATE SET
  region_name = EXCLUDED.region_name,
  weight_unit = EXCLUDED.weight_unit,
  length_unit = EXCLUDED.length_unit,
  distance_unit = EXCLUDED.distance_unit,
  temperature_unit = EXCLUDED.temperature_unit,
  depth_unit = EXCLUDED.depth_unit,
  speed_unit = EXCLUDED.speed_unit,
  pressure_unit = EXCLUDED.pressure_unit,
  currency = EXCLUDED.currency,
  date_format = EXCLUDED.date_format,
  number_format = EXCLUDED.number_format;

-- =====================================================
-- 10. CONVERSION RULES
-- =====================================================

-- Auto-switch rules for better UX
INSERT INTO unit_conversion_rules (category_id, from_unit, to_unit, threshold_value, rule_type, context, priority) VALUES
-- Weight conversions
('weight', 'g', 'kg', 1000, 'auto_switch', 'display', 1),
('weight', 'oz', 'lbs', 16, 'auto_switch', 'display', 1),
('weight', 'kg', 'g', 0.1, 'suggestion', 'input', 2),
('weight', 'lbs', 'oz', 0.25, 'suggestion', 'input', 2),

-- Length conversions
('length', 'cm', 'm', 100, 'auto_switch', 'display', 1),
('length', 'inch', 'ft', 12, 'auto_switch', 'display', 1),
('length', 'm', 'cm', 1, 'suggestion', 'input', 2),
('length', 'ft', 'inch', 1, 'suggestion', 'input', 2),

-- Distance conversions
('distance', 'm_dist', 'km', 1000, 'auto_switch', 'display', 1),
('distance', 'km', 'm_dist', 1, 'suggestion', 'input', 2)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 11. VALIDATION RULES
-- =====================================================

-- Context-specific validation rules
INSERT INTO unit_validation_rules (unit_id, min_value, max_value, precision_digits, context, error_message, warning_message) VALUES
-- Catch weight validation
('kg', 0.01, 500, 2, 'catch_weight', 'Balık ağırlığı 0.01kg - 500kg arasında olmalıdır', 'Çok büyük bir balık! Emin misiniz?'),
('lbs', 0.02, 1100, 2, 'catch_weight', 'Fish weight must be between 0.02lbs - 1100lbs', 'Very large fish! Are you sure?'),
('g', 10, 500000, 0, 'catch_weight', 'Balık ağırlığı 10g - 500kg arasında olmalıdır', 'Çok büyük bir balık! Emin misiniz?'),

-- Fish length validation
('cm', 1, 500, 1, 'fish_length', 'Balık boyu 1cm - 500cm arasında olmalıdır', 'Çok büyük bir balık! Emin misiniz?'),
('inch', 0.4, 197, 1, 'fish_length', 'Fish length must be between 0.4in - 197in', 'Very large fish! Are you sure?'),
('m', 0.01, 5, 2, 'fish_length', 'Balık boyu 1cm - 5m arasında olmalıdır', 'Çok büyük bir balık! Emin misiniz?'),

-- Water temperature validation
('celsius', -2, 45, 1, 'water_temperature', 'Su sıcaklığı -2°C - 45°C arasında olmalıdır', 'Olağandışı su sıcaklığı'),
('fahrenheit', 28, 113, 1, 'water_temperature', 'Water temperature must be between 28°F - 113°F', 'Unusual water temperature'),

-- Depth validation
('meters', 0.1, 11000, 1, 'fishing_depth', 'Derinlik 0.1m - 11000m arasında olmalıdır', 'Çok derin! Emin misiniz?'),
('feet', 0.3, 36000, 1, 'fishing_depth', 'Depth must be between 0.3ft - 36000ft', 'Very deep! Are you sure?'),
('fathoms', 0.05, 6000, 1, 'fishing_depth', 'Derinlik 0.05fm - 6000fm arasında olmalıdır', 'Çok derin! Emin misiniz?')
ON CONFLICT DO NOTHING;

-- =====================================================
-- 12. PERFORMANCE OPTIMIZATIONS
-- =====================================================

-- Pre-calculate common conversions for cache
INSERT INTO unit_conversion_cache (from_unit, to_unit, from_value, to_value, hit_count) VALUES
-- Common weight conversions
('kg', 'lbs', 1, 2.20462, 100),
('kg', 'lbs', 2, 4.40924, 100),
('kg', 'lbs', 5, 11.0231, 100),
('lbs', 'kg', 1, 0.453592, 100),
('lbs', 'kg', 5, 2.26796, 100),
('lbs', 'kg', 10, 4.53592, 100),

-- Common length conversions
('cm', 'inch', 10, 3.93701, 100),
('cm', 'inch', 20, 7.87402, 100),
('cm', 'inch', 50, 19.685, 100),
('inch', 'cm', 1, 2.54, 100),
('inch', 'cm', 12, 30.48, 100),
('inch', 'cm', 24, 60.96, 100),

-- Common temperature conversions
('celsius', 'fahrenheit', 0, 32, 100),
('celsius', 'fahrenheit', 10, 50, 100),
('celsius', 'fahrenheit', 20, 68, 100),
('fahrenheit', 'celsius', 32, 0, 100),
('fahrenheit', 'celsius', 68, 20, 100),
('fahrenheit', 'celsius', 86, 30, 100)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 13. SYSTEM SETTINGS
-- =====================================================

-- Insert unit system settings into system_settings table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_settings') THEN
    INSERT INTO system_settings (key, value, description, category) VALUES
    ('unit_system.cache_enabled', 'true', 'Enable unit conversion caching', 'units'),
    ('unit_system.cache_max_size', '10000', 'Maximum number of cached conversions', 'units'),
    ('unit_system.cache_ttl_hours', '24', 'Cache time-to-live in hours', 'units'),
    ('unit_system.auto_detect_region', 'true', 'Auto-detect user region for unit defaults', 'units'),
    ('unit_system.analytics_enabled', 'true', 'Enable unit usage analytics', 'units'),
    ('unit_system.validation_strict', 'false', 'Enable strict validation for unit values', 'units')
    ON CONFLICT (key) DO UPDATE SET
      value = EXCLUDED.value,
      description = EXCLUDED.description,
      updated_at = NOW();
  END IF;
END $$; 