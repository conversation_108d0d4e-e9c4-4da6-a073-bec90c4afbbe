-- =====================================================
-- FISHIVO - FULL DATABASE CLEANUP
-- =====================================================
-- Bu script Supabase'deki tüm Fishivo tablolarını temizler
-- SADECE DEVELOPMENT ortamında kullanın!
-- =====================================================

-- Disable foreign key checks temporarily
SET session_replication_role = replica;

-- =====================================================
-- 1. DROP ALL FISHIVO TABLES
-- =====================================================

-- Unit System Tables
DROP TABLE IF EXISTS unit_usage_analytics CASCADE;
DROP TABLE IF EXISTS unit_conversion_cache CASCADE;
DROP TABLE IF EXISTS regional_unit_defaults CASCADE;
DROP TABLE IF EXISTS unit_validation_rules CASCADE;
DROP TABLE IF EXISTS unit_conversion_rules CASCADE;
DROP TABLE IF EXISTS user_unit_preferences CASCADE;
DROP TABLE IF EXISTS unit_definitions CASCADE;
DROP TABLE IF EXISTS unit_categories CASCADE;

-- Admin System Tables
DROP TABLE IF EXISTS admin_logs CASCADE;
DROP TABLE IF EXISTS reports CASCADE;
DROP TABLE IF EXISTS system_settings CASCADE;
DROP TABLE IF EXISTS user_warnings CASCADE;
DROP TABLE IF EXISTS blocked_users CASCADE;
DROP TABLE IF EXISTS content_moderation_queue CASCADE;
DROP TABLE IF EXISTS feature_flags CASCADE;
DROP TABLE IF EXISTS api_rate_limits CASCADE;

-- Equipment System Tables
DROP TABLE IF EXISTS user_equipment CASCADE;
DROP TABLE IF EXISTS equipment_reviews CASCADE;
DROP TABLE IF EXISTS equipment_wishlist CASCADE;
DROP TABLE IF EXISTS equipment CASCADE;
DROP TABLE IF EXISTS equipment_categories CASCADE;
DROP TABLE IF EXISTS equipment_brands CASCADE;

-- Messaging & Badge System Tables
DROP TABLE IF EXISTS message_participants CASCADE;
DROP TABLE IF EXISTS messages CASCADE;
DROP TABLE IF EXISTS message_threads CASCADE;
DROP TABLE IF EXISTS user_badges CASCADE;
DROP TABLE IF EXISTS badge_progress CASCADE;
DROP TABLE IF EXISTS badge_definitions CASCADE;
DROP TABLE IF EXISTS leaderboard_entries CASCADE;
DROP TABLE IF EXISTS leaderboards CASCADE;
DROP TABLE IF EXISTS user_achievement_stats CASCADE;

-- Core Social System Tables
DROP TABLE IF EXISTS likes CASCADE;
DROP TABLE IF EXISTS comments CASCADE;
DROP TABLE IF EXISTS follows CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS post_media CASCADE;
DROP TABLE IF EXISTS posts CASCADE;

-- Location & Fish System Tables
DROP TABLE IF EXISTS spot_reviews CASCADE;
DROP TABLE IF EXISTS spots CASCADE;
DROP TABLE IF EXISTS fish_species CASCADE;

-- Core User System Tables
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Migration tracking table
DROP TABLE IF EXISTS migrations CASCADE;

-- =====================================================
-- 2. DROP ALL FUNCTIONS
-- =====================================================

DROP FUNCTION IF EXISTS update_user_unit_preferences_timestamp() CASCADE;
DROP FUNCTION IF EXISTS update_conversion_cache_hit() CASCADE;
DROP FUNCTION IF EXISTS clean_conversion_cache() CASCADE;
DROP FUNCTION IF EXISTS update_user_stats() CASCADE;
DROP FUNCTION IF EXISTS update_post_stats() CASCADE;
DROP FUNCTION IF EXISTS update_equipment_stats() CASCADE;
DROP FUNCTION IF EXISTS calculate_badge_progress() CASCADE;
DROP FUNCTION IF EXISTS update_leaderboard() CASCADE;
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS handle_user_delete() CASCADE;

-- =====================================================
-- 3. DROP ALL TRIGGERS
-- =====================================================

-- (Triggers will be dropped automatically with CASCADE)

-- =====================================================
-- 4. DROP ALL POLICIES
-- =====================================================

-- RLS policies will be dropped automatically with tables

-- =====================================================
-- 5. RESET SEQUENCES
-- =====================================================

-- Reset any sequences that might remain
DO $$
DECLARE
    seq_name text;
BEGIN
    FOR seq_name IN 
        SELECT sequence_name FROM information_schema.sequences 
        WHERE sequence_schema = 'public'
    LOOP
        EXECUTE 'DROP SEQUENCE IF EXISTS ' || seq_name || ' CASCADE';
    END LOOP;
END $$;

-- Re-enable foreign key checks
SET session_replication_role = DEFAULT;

-- =====================================================
-- 6. CREATE MIGRATION TRACKING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS migrations (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL UNIQUE,
    executed_at TIMESTAMP DEFAULT NOW()
);

-- Success message
SELECT 'Fishivo database completely cleaned! Ready for fresh migration.' as status; 