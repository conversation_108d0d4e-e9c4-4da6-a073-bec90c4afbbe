-- =====================================================
-- FISHIVO BASIC UNITS SYSTEM - NO FK DEPENDENCIES
-- Only core tables without users FK references
-- =====================================================

-- =====================================================
-- 1. UNIT CATEGORIES
-- =====================================================
CREATE TABLE IF NOT EXISTS unit_categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_en TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  base_unit TEXT NOT NULL,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 2. UNIT DEFINITIONS
-- =====================================================
CREATE TABLE IF NOT EXISTS unit_definitions (
  id TEXT PRIMARY KEY,
  category_id TEXT REFERENCES unit_categories(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  name_en TEXT NOT NULL,
  symbol TEXT NOT NULL,
  
  -- Conversion properties
  is_base_unit BOOLEAN DEFAULT FALSE,
  conversion_factor DECIMAL(20,10),
  conversion_formula TEXT,
  reverse_formula TEXT,
  
  -- Display properties
  precision_digits INTEGER DEFAULT 2,
  min_value DECIMAL(20,10),
  max_value DECIMAL(20,10),
  
  -- Regional usage
  regions JSONB DEFAULT '[]',
  popularity INTEGER DEFAULT 0,
  default_for_regions JSONB DEFAULT '[]',
  
  -- Metadata
  use_case TEXT,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(category_id, symbol),
  CHECK (conversion_factor > 0 OR conversion_formula IS NOT NULL)
);

-- =====================================================
-- 3. REGIONAL DEFAULTS (NO FK DEPENDENCIES)
-- =====================================================
CREATE TABLE IF NOT EXISTS regional_unit_defaults (
  id BIGSERIAL PRIMARY KEY,
  region_code TEXT NOT NULL,
  region_name TEXT NOT NULL,
  
  -- Default units by category (TEXT only, no FK for now)
  weight_unit TEXT,
  length_unit TEXT,
  distance_unit TEXT,
  temperature_unit TEXT,
  depth_unit TEXT,
  speed_unit TEXT,
  pressure_unit TEXT,
  
  -- Regional metadata
  currency TEXT DEFAULT 'USD',
  date_format TEXT DEFAULT 'DD/MM/YYYY',
  number_format JSONB DEFAULT '{"decimal": ".", "thousands": ","}',
  
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(region_code)
);

-- =====================================================
-- 4. INDEXES
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_unit_categories_active ON unit_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_unit_categories_sort ON unit_categories(sort_order);

CREATE INDEX IF NOT EXISTS idx_unit_definitions_category ON unit_definitions(category_id);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_base_unit ON unit_definitions(is_base_unit);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_active ON unit_definitions(is_active);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_regions ON unit_definitions USING GIN(regions);

CREATE INDEX IF NOT EXISTS idx_regional_unit_defaults_region ON regional_unit_defaults(region_code);
CREATE INDEX IF NOT EXISTS idx_regional_unit_defaults_active ON regional_unit_defaults(is_active);

-- =====================================================
-- 5. DATA POPULATION
-- =====================================================

-- Insert unit categories
INSERT INTO unit_categories (id, name, name_en, description, icon, base_unit, sort_order) VALUES
('weight', 'Ağırlık', 'Weight', 'Balık ağırlığı ölçümü', '⚖️', 'kg', 1),
('length', 'Uzunluk', 'Length', 'Balık boyu ölçümü', '📏', 'cm', 2),
('distance', 'Mesafe', 'Distance', 'Lokasyon mesafeleri', '📍', 'km', 3),
('temperature', 'Sıcaklık', 'Temperature', 'Su ve hava sıcaklığı', '🌡️', 'celsius', 4),
('depth', 'Derinlik', 'Depth', 'Su derinliği', '🌊', 'meters', 5),
('speed', 'Hız', 'Speed', 'Rüzgar ve tekne hızı', '💨', 'kmh', 6),
('pressure', 'Basınç', 'Pressure', 'Hava basıncı', '🌀', 'hpa', 7)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  description = EXCLUDED.description,
  icon = EXCLUDED.icon,
  base_unit = EXCLUDED.base_unit,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- Insert weight units
INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, regions, popularity, default_for_regions, sort_order) VALUES
('kg', 'weight', 'Kilogram', 'Kilogram', 'kg', TRUE, 1.0, 2, '["TR", "EU", "GLOBAL"]', 95, '["TR", "EU"]', 1),
('lbs', 'weight', 'Pound', 'Pound', 'lbs', FALSE, 2.20462, 2, '["US", "UK"]', 85, '["US", "UK"]', 2),
('g', 'weight', 'Gram', 'Gram', 'g', FALSE, 0.001, 0, '["TR", "EU", "GLOBAL"]', 75, '[]', 3),
('oz', 'weight', 'Ons', 'Ounce', 'oz', FALSE, 35.274, 1, '["US", "UK"]', 65, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- Insert length units
INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, regions, popularity, default_for_regions, sort_order) VALUES
('cm', 'length', 'Santimetre', 'Centimeter', 'cm', TRUE, 1.0, 1, '["TR", "EU", "GLOBAL"]', 95, '["TR", "EU"]', 1),
('inch', 'length', 'İnç', 'Inch', 'in', FALSE, 0.393701, 1, '["US", "UK"]', 85, '["US", "UK"]', 2),
('m', 'length', 'Metre', 'Meter', 'm', FALSE, 100.0, 2, '["TR", "EU", "GLOBAL"]', 75, '[]', 3),
('ft', 'length', 'Feet', 'Feet', 'ft', FALSE, 3.28084, 1, '["US", "UK"]', 65, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- Insert distance units
INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, regions, popularity, default_for_regions, sort_order) VALUES
('km', 'distance', 'Kilometre', 'Kilometer', 'km', TRUE, 1.0, 2, '["TR", "EU", "GLOBAL"]', 95, '["TR", "EU"]', 1),
('miles', 'distance', 'Mil', 'Miles', 'mi', FALSE, 0.621371, 2, '["US", "UK"]', 85, '["US", "UK"]', 2),
('m_dist', 'distance', 'Metre', 'Meter', 'm', FALSE, 0.001, 0, '["TR", "EU", "GLOBAL"]', 75, '[]', 3),
('nm', 'distance', 'Deniz Mili', 'Nautical Mile', 'nm', FALSE, 0.539957, 2, '["MARINE"]', 65, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- Insert temperature units
INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, conversion_formula, reverse_formula, precision_digits, regions, popularity, default_for_regions, sort_order) VALUES
('celsius', 'temperature', 'Santigrat', 'Celsius', '°C', TRUE, NULL, NULL, NULL, 1, '["TR", "EU", "GLOBAL"]', 95, '["TR", "EU"]', 1),
('fahrenheit', 'temperature', 'Fahrenheit', 'Fahrenheit', '°F', FALSE, NULL, '(${value} - 32) * 5/9', '(${value} * 9/5) + 32', 1, '["US"]', 85, '["US"]', 2)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  conversion_formula = EXCLUDED.conversion_formula,
  reverse_formula = EXCLUDED.reverse_formula,
  precision_digits = EXCLUDED.precision_digits,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- Insert depth units
INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, regions, popularity, default_for_regions, sort_order) VALUES
('meters', 'depth', 'Metre', 'Meters', 'm', TRUE, 1.0, 1, '["TR", "EU", "GLOBAL"]', 95, '["TR", "EU"]', 1),
('feet', 'depth', 'Feet', 'Feet', 'ft', FALSE, 3.28084, 1, '["US", "UK"]', 90, '["US", "UK"]', 2),
('fathoms', 'depth', 'Kulaç', 'Fathoms', 'fath', FALSE, 0.546806649, 1, '["MARINE"]', 70, '[]', 3)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- Insert speed units
INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, regions, popularity, default_for_regions, sort_order) VALUES
('kmh', 'speed', 'Kilometre/Saat', 'Kilometer per Hour', 'km/h', TRUE, 1.0, 1, '["TR", "EU", "GLOBAL"]', 95, '["TR", "EU"]', 1),
('mph', 'speed', 'Mil/Saat', 'Miles per Hour', 'mph', FALSE, 1.60934, 1, '["US", "UK"]', 85, '["US", "UK"]', 2),
('ms', 'speed', 'Metre/Saniye', 'Meter per Second', 'm/s', FALSE, 0.277777778, 1, '["SCIENTIFIC"]', 75, '[]', 3),
('knots', 'speed', 'Knot', 'Knots', 'kn', FALSE, 1.852, 1, '["MARINE"]', 65, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- Insert pressure units
INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, regions, popularity, default_for_regions, sort_order) VALUES
('hpa', 'pressure', 'Hektopaskal', 'Hectopascal', 'hPa', TRUE, 1.0, 1, '["TR", "EU", "GLOBAL"]', 95, '["TR", "EU"]', 1),
('inhg', 'pressure', 'İnç Civa', 'Inches of Mercury', 'inHg', FALSE, 33.8639, 2, '["US"]', 85, '["US"]', 2),
('mbar', 'pressure', 'Milibar', 'Millibar', 'mbar', FALSE, 1.0, 1, '["AVIATION"]', 75, '[]', 3),
('mmhg', 'pressure', 'Milimetre Civa', 'Millimeters of Mercury', 'mmHg', FALSE, 1.33322, 0, '["MEDICAL"]', 65, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- Insert regional defaults
INSERT INTO regional_unit_defaults (region_code, region_name, weight_unit, length_unit, distance_unit, temperature_unit, depth_unit, speed_unit, pressure_unit) VALUES
('TR', 'Türkiye', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa'),
('US', 'United States', 'lbs', 'inch', 'miles', 'fahrenheit', 'feet', 'mph', 'inhg'),
('UK', 'United Kingdom', 'lbs', 'inch', 'miles', 'celsius', 'feet', 'mph', 'hpa'),
('EU', 'European Union', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa')
ON CONFLICT (region_code) DO UPDATE SET
  region_name = EXCLUDED.region_name,
  weight_unit = EXCLUDED.weight_unit,
  length_unit = EXCLUDED.length_unit,
  distance_unit = EXCLUDED.distance_unit,
  temperature_unit = EXCLUDED.temperature_unit,
  depth_unit = EXCLUDED.depth_unit,
  speed_unit = EXCLUDED.speed_unit,
  pressure_unit = EXCLUDED.pressure_unit;

-- Success message
SELECT 'Fishivo Basic Units System migration completed successfully!' as message; 