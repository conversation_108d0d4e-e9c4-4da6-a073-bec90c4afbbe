#!/usr/bin/env node

const fs = require('fs');
const https = require('https');
const { URL } = require('url');
const path = require('path');

// Ana dizindeki .env dosyasını yükle
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

// Custom HTTPS agent
const httpsAgent = new https.Agent({
  rejectUnauthorized: false,
  keepAlive: true,
  timeout: 30000
});

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

// Execute SQL via Supabase REST API
async function executeSQL(sql, description = 'SQL Query') {
  return new Promise((resolve, reject) => {
    log(`Executing: ${description}`);
    
    const postData = JSON.stringify({ sql: sql });
    const url = new URL(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`);
    
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY,
        'User-Agent': 'Fishivo-Schema-Migration/1.0'
      },
      agent: httpsAgent
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            log(`✅ ${description} completed successfully`, 'success');
            const result = data ? JSON.parse(data) : { success: true };
            resolve(result);
          } else {
            log(`❌ ${description} failed with status ${res.statusCode}`, 'error');
            log(`Response: ${data}`, 'error');
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        } catch (parseError) {
          log(`❌ Failed to parse response for ${description}: ${parseError.message}`, 'error');
          reject(parseError);
        }
      });
    });

    req.on('error', (error) => {
      log(`❌ Network error during ${description}: ${error.message}`, 'error');
      reject(error);
    });

    req.setTimeout(30000);
    req.write(postData);
    req.end();
  });
}

async function runSchemaMigration() {
  try {
    log('🚀 Running Fishivo schema migration...');
    
    // Read and execute initial schema
    const schemaPath = path.join(__dirname, '../../packages/database/migrations/001_initial_schema.sql');
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    await executeSQL(schemaSQL, 'Initial Schema Migration');
    
    log('✅ Schema migration completed successfully!', 'success');
    
  } catch (error) {
    log(`❌ Schema migration failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

runSchemaMigration(); 