#!/usr/bin/env node

const fs = require('fs');
const https = require('https');
const { URL } = require('url');
const path = require('path');
// Ana dizindeki .env dosyasını yükle
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

// Custom HTTPS agent to handle SSL issues
const httpsAgent = new https.Agent({
  rejectUnauthorized: false, // Bypass SSL certificate verification
  keepAlive: true,
  timeout: 30000
});

// Enhanced error handling and logging
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

// Read SQL file with error handling
function readSQLFile(filename) {
  try {
    const filePath = path.join(__dirname, filename);
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filename}`);
    }
    const content = fs.readFileSync(filePath, 'utf8');
    log(`Successfully read ${filename} (${content.length} characters)`);
    return content;
  } catch (error) {
    log(`Failed to read ${filename}: ${error.message}`, 'error');
    throw error;
  }
}

// Execute SQL via Supabase REST API
async function executeSQL(sql, description = 'SQL Query') {
  return new Promise((resolve, reject) => {
    log(`Executing: ${description}`);
    
    // Prepare request data
    const postData = JSON.stringify({ sql: sql });
    
    // Parse URL for request options
    const url = new URL(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`);
    
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY,
        'User-Agent': 'Fishivo-Migration-Script/1.0'
      },
      agent: httpsAgent
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            log(`✅ ${description} completed successfully`, 'success');
            const result = data ? JSON.parse(data) : { success: true };
            resolve(result);
          } else {
            log(`❌ ${description} failed with status ${res.statusCode}`, 'error');
            log(`Response: ${data}`, 'error');
            reject(new Error(`HTTP ${res.statusCode}: ${data}`));
          }
        } catch (parseError) {
          log(`❌ Failed to parse response for ${description}: ${parseError.message}`, 'error');
          reject(parseError);
        }
      });
    });

    req.on('error', (error) => {
      log(`❌ Network error during ${description}: ${error.message}`, 'error');
      reject(error);
    });

    req.on('timeout', () => {
      log(`❌ Timeout during ${description}`, 'error');
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.setTimeout(30000); // 30 second timeout
    req.write(postData);
    req.end();
  });
}

// Test Supabase connection
async function testConnection() {
  try {
    log('Testing Supabase connection...');
    
    // Simple test query
    const testSQL = 'SELECT 1 as test';
    const result = await executeSQL(testSQL, 'Connection Test');
    
    log(`Connection test result: ${JSON.stringify(result)}`);
    
    if (result && (result.status === 'success' || result.test || Array.isArray(result))) {
      log(`✅ Connection successful!`, 'success');
      return true;
    } else {
      log('❌ Connection test returned unexpected result', 'error');
      return false;
    }
  } catch (error) {
    log(`❌ Connection test failed: ${error.message}`, 'error');
    return false;
  }
}

// Create exec_sql function if it doesn't exist
async function ensureExecSqlFunction() {
  try {
    log('Ensuring exec_sql function exists...');
    
    const createFunctionSQL = readSQLFile('create_exec_sql_function.sql');
    await executeSQL(createFunctionSQL, 'Create exec_sql function');
    
    log('✅ exec_sql function is ready', 'success');
    return true;
  } catch (error) {
    log(`❌ Failed to create exec_sql function: ${error.message}`, 'error');
    return false;
  }
}

// Main migration function
async function runMigration() {
  try {
    log('🚀 Starting Fishivo database migration...');
    log(`Target: ${SUPABASE_URL}`);
    
    // Step 1: Test connection
    const connectionOk = await testConnection();
    if (!connectionOk) {
      throw new Error('Cannot establish connection to Supabase');
    }
    
    // Step 2: Ensure exec_sql function exists
    const functionOk = await ensureExecSqlFunction();
    if (!functionOk) {
      throw new Error('Cannot create exec_sql function');
    }
    
    // Step 3: Clean up existing tables (optional)
    try {
      log('Cleaning up existing tables...');
      const cleanupSQL = readSQLFile('cleanup_tables.sql');
      await executeSQL(cleanupSQL, 'Cleanup existing tables');
    } catch (error) {
      log(`⚠️ Cleanup warning (this is usually OK): ${error.message}`);
    }
    
    // Step 4: Create complete unit system schema
    log('Creating unit system schema...');
    const schemaSQL = readSQLFile('unit_system_complete.sql');
    await executeSQL(schemaSQL, 'Create unit system schema');
    
    // Step 5: Verify installation
    log('Verifying installation...');
    const verifySQL = `
      SELECT 
        schemaname, 
        tablename, 
        tableowner 
      FROM pg_tables 
      WHERE schemaname = 'public' 
        AND tablename IN ('unit_categories', 'unit_definitions', 'unit_conversions', 'user_unit_preferences')
      ORDER BY tablename;
    `;
    
    const tables = await executeSQL(verifySQL, 'Verify tables');
    
    if (tables && tables.length >= 4) {
      log('✅ All unit system tables created successfully!', 'success');
      log(`Created tables: ${tables.map(t => t.tablename).join(', ')}`);
    } else {
      log('⚠️ Some tables may be missing. Check the logs above.', 'error');
    }
    
    log('🎉 Migration completed successfully!', 'success');
    
  } catch (error) {
    log(`💥 Migration failed: ${error.message}`, 'error');
    process.exit(1);
  }
}

// Alternative connection methods for troubleshooting
async function tryAlternativeConnection() {
  log('Trying alternative connection methods...');
  
  // Method 1: Direct REST API without exec_sql
  try {
    log('Trying direct REST API...');
    const url = new URL(`${SUPABASE_URL}/rest/v1/`);
    
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
        'apikey': SUPABASE_SERVICE_ROLE_KEY
      },
      agent: httpsAgent
    };

    return new Promise((resolve, reject) => {
      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          if (res.statusCode === 200) {
            log('✅ Direct REST API connection successful', 'success');
            resolve(true);
          } else {
            log(`❌ Direct REST API failed: ${res.statusCode}`, 'error');
            resolve(false);
          }
        });
      });
      
      req.on('error', error => {
        log(`❌ Direct REST API error: ${error.message}`, 'error');
        resolve(false);
      });
      
      req.setTimeout(10000);
      req.end();
    });
    
  } catch (error) {
    log(`❌ Alternative connection failed: ${error.message}`, 'error');
    return false;
  }
}

// Main execution
if (require.main === module) {
  log('🎣 Fishivo Database Migration Tool');
  log('==================================');
  
  // Check environment variables
  if (!SUPABASE_URL || SUPABASE_URL.includes('your-project')) {
    log('❌ Please set SUPABASE_URL environment variable', 'error');
    process.exit(1);
  }
  
  if (!SUPABASE_SERVICE_ROLE_KEY || SUPABASE_SERVICE_ROLE_KEY.includes('your-service')) {
    log('❌ Please set SUPABASE_SERVICE_ROLE_KEY environment variable', 'error');
    process.exit(1);
  }
  
  // Run migration
  runMigration().catch(async (error) => {
    log(`💥 Main migration failed: ${error.message}`, 'error');
    
    // Try alternative connection as fallback
    log('Attempting alternative connection methods...');
    const altSuccess = await tryAlternativeConnection();
    
    if (altSuccess) {
      log('ℹ️ Alternative connection successful. You may need to run migration manually via Supabase Dashboard.');
    } else {
      log('💥 All connection methods failed. Please check your network and Supabase configuration.');
    }
    
    process.exit(1);
  });
} 