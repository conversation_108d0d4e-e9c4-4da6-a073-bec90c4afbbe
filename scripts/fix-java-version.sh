#!/bin/bash

# Fix Java Version Issues for React Native Android Build
# This script resolves "Unsupported class file major version 68" error

echo "🔧 Fixing Java version configuration for React Native Android build..."

# Set Java 17 as the active Java version
export JAVA_HOME="/usr/local/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home"
export PATH="$JAVA_HOME/bin:$PATH"

echo "✅ Java Home set to: $JAVA_HOME"

# Verify Java version
echo "📋 Current Java version:"
java -version

echo ""
echo "📋 Current Gradle Java version:"
javac -version

# Navigate to Android directory
cd apps/mobile/android

echo ""
echo "🧹 Cleaning Gradle cache and build artifacts..."

# Clean Gradle cache
./gradlew clean
rm -rf .gradle
rm -rf build
rm -rf app/build

# Clean global Gradle cache
rm -rf ~/.gradle/caches
rm -rf ~/.gradle/wrapper

echo ""
echo "🔄 Refreshing Gradle wrapper..."
./gradlew wrapper --gradle-version=8.10.2

echo ""
echo "📦 Rebuilding with correct Java version..."
./gradlew build --info

echo ""
echo "✅ Java version fix completed!"
echo "💡 If you still encounter issues, try running:"
echo "   export JAVA_HOME=/usr/local/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home"
echo "   export PATH=\$JAVA_HOME/bin:\$PATH"
echo "   cd apps/mobile/android && ./gradlew clean build"
