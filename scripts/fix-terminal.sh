#!/bin/bash

# Terminal Sorunlarini Cozme Script'i
# Bu script PATH ve encoding sorunlarini cozer

echo "=== Terminal Sorunlari Cozuluyor ==="

# 1. Mevcut durumu kontrol et
echo "1. Mevcut PATH kontrol ediliyor..."
echo "PATH: $PATH"
echo ""

# 2. Default PATH'i ayarla
echo "2. Default PATH ayarlaniyor..."
export PATH="/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
echo "Yeni PATH: $PATH"
echo ""

# 3. Locale ayarlarini kontrol et
echo "3. Locale ayarlari kontrol ediliyor..."
locale
echo ""

# 4. UTF-8 encoding'i ayarla
echo "4. UTF-8 encoding ayarlaniyor..."
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
echo "Locale ayarlandi: $LANG"
echo ""

# 5. Shell profilini yedekle
echo "5. Shell profili yedekleniyor..."
if [ -f ~/.bash_profile ]; then
    cp ~/.bash_profile ~/.bash_profile.backup
    echo "bash_profile yedeklendi"
fi

if [ -f ~/.zshrc ]; then
    cp ~/.zshrc ~/.zshrc.backup
    echo "zshrc yedeklendi"
fi
echo ""

# 6. Temiz PATH'i profile'a yaz
echo "6. Temiz PATH profile'a yaziliyor..."

# Hangi shell kullaniliyor kontrol et
if [ "$SHELL" = "/bin/zsh" ] || [ "$SHELL" = "/usr/bin/zsh" ]; then
    echo "ZSH shell tespit edildi"
    cat > ~/.zshrc << 'EOF'
# Default PATH
export PATH="/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"

# Homebrew
if [ -d "/opt/homebrew/bin" ]; then
    export PATH="/opt/homebrew/bin:$PATH"
fi

# Node.js (if installed)
if [ -d "/usr/local/lib/node_modules" ]; then
    export PATH="/usr/local/lib/node_modules/.bin:$PATH"
fi

# Yarn global
if [ -d "$HOME/.yarn/bin" ]; then
    export PATH="$HOME/.yarn/bin:$PATH"
fi

# Android SDK
if [ -d "$HOME/Library/Android/sdk" ]; then
    export ANDROID_HOME="$HOME/Library/Android/sdk"
    export PATH="$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools"
fi

# Locale settings
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
EOF
    echo "ZSH profili guncellendi"
else
    echo "Bash shell tespit edildi"
    cat > ~/.bash_profile << 'EOF'
# Default PATH
export PATH="/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"

# Homebrew
if [ -d "/opt/homebrew/bin" ]; then
    export PATH="/opt/homebrew/bin:$PATH"
fi

# Node.js (if installed)
if [ -d "/usr/local/lib/node_modules" ]; then
    export PATH="/usr/local/lib/node_modules/.bin:$PATH"
fi

# Yarn global
if [ -d "$HOME/.yarn/bin" ]; then
    export PATH="$HOME/.yarn/bin:$PATH"
fi

# Android SDK
if [ -d "$HOME/Library/Android/sdk" ]; then
    export ANDROID_HOME="$HOME/Library/Android/sdk"
    export PATH="$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools"
fi

# Locale settings
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
EOF
    echo "Bash profili guncellendi"
fi
echo ""

# 7. Profili yukle
echo "7. Yeni profil yukleniyor..."
if [ "$SHELL" = "/bin/zsh" ] || [ "$SHELL" = "/usr/bin/zsh" ]; then
    source ~/.zshrc
else
    source ~/.bash_profile
fi
echo ""

# 8. Test et
echo "8. Sistem test ediliyor..."
echo "PATH: $PATH"
echo "Locale: $LANG"
echo "Yarn version: $(yarn --version 2>/dev/null || echo 'Yarn bulunamadi')"
echo "Node version: $(node --version 2>/dev/null || echo 'Node bulunamadi')"
echo ""

echo "=== Terminal Sorunlari Cozuldu ==="
echo "Lutfen yeni terminal penceresi acin veya 'source ~/.bash_profile' (bash) veya 'source ~/.zshrc' (zsh) calistirin"
