#!/usr/bin/env node

/**
 * Database Migration Tool for Fishivo
 * 
 * Usage:
 *   node database/migrate.js up          # Run all pending migrations
 *   node database/migrate.js down        # Rollback last migration
 *   node database/migrate.js status      # Show migration status
 *   node database/migrate.js create <name> # Create new migration file
 */

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: path.join(__dirname, '../../.env') });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration in .env file');
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Migration directory
const MIGRATIONS_DIR = path.join(__dirname, 'migrations');

// Ensure migrations table exists
async function ensureMigrationsTable() {
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        filename TEXT NOT NULL UNIQUE,
        executed_at TIMESTAMPTZ DEFAULT NOW()
      );
    `
  });

  if (error) {
    console.error('❌ Failed to create migrations table:', error.message);
    process.exit(1);
  }
}

// Get executed migrations
async function getExecutedMigrations() {
  const { data, error } = await supabase
    .from('migrations')
    .select('filename')
    .order('filename');

  if (error) {
    console.error('❌ Failed to get executed migrations:', error.message);
    process.exit(1);
  }

  return data.map(row => row.filename);
}

// Get available migration files
function getAvailableMigrations() {
  if (!fs.existsSync(MIGRATIONS_DIR)) {
    fs.mkdirSync(MIGRATIONS_DIR, { recursive: true });
    return [];
  }

  return fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.sql'))
    .sort();
}

// Execute SQL file
async function executeSqlFile(filename) {
  const filePath = path.join(MIGRATIONS_DIR, filename);
  const sql = fs.readFileSync(filePath, 'utf8');

  console.log(`📄 Executing ${filename}...`);

  // Split SQL into individual statements
  const statements = sql
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

  for (const statement of statements) {
    if (statement.trim()) {
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      if (error) {
        console.error(`❌ Error in ${filename}:`, error.message);
        throw error;
      }
    }
  }

  // Record migration as executed
  const { error } = await supabase
    .from('migrations')
    .insert({ filename });

  if (error) {
    console.error(`❌ Failed to record migration ${filename}:`, error.message);
    throw error;
  }

  console.log(`✅ ${filename} executed successfully`);
}

// Run pending migrations
async function runMigrations() {
  await ensureMigrationsTable();

  const executed = await getExecutedMigrations();
  const available = getAvailableMigrations();
  const pending = available.filter(file => !executed.includes(file));

  if (pending.length === 0) {
    console.log('✅ No pending migrations');
    return;
  }

  console.log(`🚀 Running ${pending.length} pending migration(s):`);
  pending.forEach(file => console.log(`   - ${file}`));
  console.log('');

  for (const filename of pending) {
    try {
      await executeSqlFile(filename);
    } catch (error) {
      console.error(`❌ Migration failed: ${filename}`);
      process.exit(1);
    }
  }

  console.log('');
  console.log('🎉 All migrations completed successfully!');
}

// Show migration status
async function showStatus() {
  await ensureMigrationsTable();

  const executed = await getExecutedMigrations();
  const available = getAvailableMigrations();

  console.log('📊 Migration Status:');
  console.log('');

  if (available.length === 0) {
    console.log('   No migration files found');
    return;
  }

  available.forEach(file => {
    const status = executed.includes(file) ? '✅ Executed' : '⏳ Pending';
    console.log(`   ${status} - ${file}`);
  });

  console.log('');
  console.log(`Total: ${available.length} migrations, ${executed.length} executed, ${available.length - executed.length} pending`);
}

// Rollback last migration
async function rollbackMigration() {
  await ensureMigrationsTable();

  const { data, error } = await supabase
    .from('migrations')
    .select('filename')
    .order('executed_at', { ascending: false })
    .limit(1);

  if (error) {
    console.error('❌ Failed to get last migration:', error.message);
    process.exit(1);
  }

  if (!data || data.length === 0) {
    console.log('❌ No migrations to rollback');
    return;
  }

  const lastMigration = data[0].filename;
  console.log(`⚠️  Rolling back migration: ${lastMigration}`);
  console.log('⚠️  Note: This will only remove the migration record.');
  console.log('⚠️  Manual cleanup of database changes may be required.');

  // Remove migration record
  const { error: deleteError } = await supabase
    .from('migrations')
    .delete()
    .eq('filename', lastMigration);

  if (deleteError) {
    console.error('❌ Failed to rollback migration:', deleteError.message);
    process.exit(1);
  }

  console.log(`✅ Migration ${lastMigration} rolled back`);
}

// Create new migration file
function createMigration(name) {
  if (!name) {
    console.error('❌ Migration name is required');
    console.error('Usage: node database/migrate.js create <name>');
    process.exit(1);
  }

  const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
  const counter = String(getAvailableMigrations().length + 1).padStart(3, '0');
  const filename = `${counter}_${name.toLowerCase().replace(/\s+/g, '_')}.sql`;
  const filePath = path.join(MIGRATIONS_DIR, filename);

  const template = `-- =====================================================
-- MIGRATION ${counter}: ${name.toUpperCase()}
-- Created: ${new Date().toISOString().slice(0, 10)}
-- Description: ${name}
-- =====================================================

-- Add your SQL statements here

-- Example:
-- CREATE TABLE IF NOT EXISTS example_table (
--   id BIGSERIAL PRIMARY KEY,
--   name TEXT NOT NULL,
--   created_at TIMESTAMPTZ DEFAULT NOW()
-- );

-- CREATE INDEX IF NOT EXISTS idx_example_table_name ON example_table(name);
`;

  if (!fs.existsSync(MIGRATIONS_DIR)) {
    fs.mkdirSync(MIGRATIONS_DIR, { recursive: true });
  }

  fs.writeFileSync(filePath, template);
  console.log(`✅ Created migration file: ${filename}`);
  console.log(`📝 Edit the file at: ${filePath}`);
}

// Main function
async function main() {
  const command = process.argv[2];
  const arg = process.argv[3];

  switch (command) {
    case 'up':
      await runMigrations();
      break;
    case 'down':
      await rollbackMigration();
      break;
    case 'status':
      await showStatus();
      break;
    case 'create':
      createMigration(arg);
      break;
    default:
      console.log('🗄️  Fishivo Database Migration Tool');
      console.log('');
      console.log('Usage:');
      console.log('  node database/migrate.js up              # Run all pending migrations');
      console.log('  node database/migrate.js down            # Rollback last migration');
      console.log('  node database/migrate.js status          # Show migration status');
      console.log('  node database/migrate.js create <name>   # Create new migration file');
      console.log('');
      console.log('Examples:');
      console.log('  node database/migrate.js up');
      console.log('  node database/migrate.js create "add user preferences"');
      console.log('  node database/migrate.js status');
      break;
  }
}

// Run the tool
main().catch(error => {
  console.error('❌ Migration tool error:', error.message);
  process.exit(1);
});
