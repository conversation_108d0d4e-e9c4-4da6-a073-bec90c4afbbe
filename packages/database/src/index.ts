// Database schemas and utilities for Fishivo
import { createClient } from '@supabase/supabase-js';

// Database table schemas
export interface DatabaseUser {
  id: string;
  email: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  bio?: string;
  location?: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseCatch {
  id: string;
  user_id: string;
  species: string;
  weight?: number;
  length?: number;
  latitude: number;
  longitude: number;
  location_name?: string;
  image_url?: string;
  description?: string;
  weather_conditions?: string;
  bait_used?: string;
  technique?: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseFollow {
  id: string;
  follower_id: string;
  following_id: string;
  created_at: string;
}

export interface DatabaseLike {
  id: string;
  user_id: string;
  catch_id: string;
  created_at: string;
}

export interface DatabaseComment {
  id: string;
  user_id: string;
  catch_id: string;
  content: string;
  created_at: string;
  updated_at: string;
}

// Database types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: DatabaseUser;
        Insert: Omit<DatabaseUser, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<DatabaseUser, 'id' | 'created_at'>>;
      };
      catches: {
        Row: DatabaseCatch;
        Insert: Omit<DatabaseCatch, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<DatabaseCatch, 'id' | 'created_at'>>;
      };
      follows: {
        Row: DatabaseFollow;
        Insert: Omit<DatabaseFollow, 'id' | 'created_at'>;
        Update: never;
      };
      likes: {
        Row: DatabaseLike;
        Insert: Omit<DatabaseLike, 'id' | 'created_at'>;
        Update: never;
      };
      comments: {
        Row: DatabaseComment;
        Insert: Omit<DatabaseComment, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<DatabaseComment, 'id' | 'created_at'>>;
      };
    };
  };
}

// Database client factory
export const createDatabaseClient = (supabaseUrl: string, supabaseKey: string) => {
  return createClient<Database>(supabaseUrl, supabaseKey);
};

// SQL Migration scripts
export const migrations = {
  createUsersTable: `
    CREATE TABLE IF NOT EXISTS users (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      email VARCHAR(255) UNIQUE NOT NULL,
      username VARCHAR(50) UNIQUE NOT NULL,
      full_name VARCHAR(255) NOT NULL,
      avatar_url TEXT,
      bio TEXT,
      location VARCHAR(255),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `,
  createCatchesTable: `
    CREATE TABLE IF NOT EXISTS catches (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      species VARCHAR(100) NOT NULL,
      weight DECIMAL(10,2),
      length DECIMAL(10,2),
      latitude DECIMAL(10,8) NOT NULL,
      longitude DECIMAL(11,8) NOT NULL,
      location_name VARCHAR(255),
      image_url TEXT,
      description TEXT,
      weather_conditions TEXT,
      bait_used VARCHAR(255),
      technique VARCHAR(255),
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `,
  createFollowsTable: `
    CREATE TABLE IF NOT EXISTS follows (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      follower_id UUID REFERENCES users(id) ON DELETE CASCADE,
      following_id UUID REFERENCES users(id) ON DELETE CASCADE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(follower_id, following_id)
    );
  `,
  createLikesTable: `
    CREATE TABLE IF NOT EXISTS likes (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      catch_id UUID REFERENCES catches(id) ON DELETE CASCADE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      UNIQUE(user_id, catch_id)
    );
  `,
  createCommentsTable: `
    CREATE TABLE IF NOT EXISTS comments (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID REFERENCES users(id) ON DELETE CASCADE,
      catch_id UUID REFERENCES catches(id) ON DELETE CASCADE,
      content TEXT NOT NULL,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `
};