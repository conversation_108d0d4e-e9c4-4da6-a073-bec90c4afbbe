# 🗄️ Fishivo Database Migration System

Bu klasör Fishivo projesinin database migration sistemini içerir. Proje bü<PERSON>üdükçe database değişikliklerini güvenli ve organize bir şekilde yönetmek için tasarlanmıştır.

## 📁 Klasör Yapısı

```
database/
├── migrations/           # Migration dosyaları
│   ├── 001_initial_schema.sql
│   ├── 002_equipment_system.sql
│   ├── 003_admin_system.sql
│   ├── 004_messaging_badges.sql
│   ├── 005_rls_policies.sql
│   └── 006_default_data.sql
├── migrate.js           # Migration yönetim aracı
└── README.md           # Bu dosya
```

## 🚀 Hızlı Başlangıç

### 1. <PERSON><PERSON>

```bash
# Migration aracını çalıştırılabilir yap
chmod +x database/migrate.js

# Tüm migration'ları çalıştır
node database/migrate.js up
```

### 2. Migration Durumunu Kontrol Et

```bash
node database/migrate.js status
```

## 📋 Migration Dosyaları

### **001_initial_schema.sql** - Temel Yapı
- ✅ Users (kullanıcı sistemi)
- ✅ Posts (içerik sistemi)
- ✅ Fish Species (balık türleri)
- ✅ Spots (balıkçılık noktaları)
- ✅ Social System (likes, comments, follows)
- ✅ Notifications (bildirim sistemi)
- ✅ Basic Indexes

### **002_equipment_system.sql** - Ekipman Sistemi
- ✅ Equipment Catalog (ekipman kataloğu)
- ✅ User Equipment (kullanıcı ekipmanları)
- ✅ Equipment Categories & Brands
- ✅ Equipment Reviews & Ratings
- ✅ Equipment Wishlist
- ✅ Triggers & Functions

### **003_admin_system.sql** - Admin & Moderasyon
- ✅ Admin Logs (admin işlem kayıtları)
- ✅ Reports System (şikayet sistemi)
- ✅ System Settings (sistem ayarları)
- ✅ User Moderation (kullanıcı moderasyonu)
- ✅ Content Moderation (içerik moderasyonu)
- ✅ Feature Flags (özellik bayrakları)
- ✅ API Rate Limiting

### **004_messaging_badges.sql** - Mesajlaşma & Rozetler
- ✅ Messaging System (mesajlaşma sistemi)
- ✅ Message Threads (mesaj konuları)
- ✅ Badge System (rozet sistemi)
- ✅ Badge Progress Tracking
- ✅ Leaderboards (liderlik tabloları)
- ✅ User Achievement Stats

### **005_rls_policies.sql** - Güvenlik Politikaları
- ✅ Row Level Security (RLS) aktifleştirme
- ✅ User Policies (kullanıcı politikaları)
- ✅ Content Policies (içerik politikaları)
- ✅ Social Policies (sosyal politikalar)
- ✅ Admin Policies (admin politikaları)

### **006_default_data.sql** - Başlangıç Verileri
- ✅ System Settings (sistem ayarları)
- ✅ Equipment Categories (ekipman kategorileri)
- ✅ Equipment Brands (ekipman markaları)
- ✅ Badge Definitions (rozet tanımları)
- ✅ Leaderboard Definitions
- ✅ Feature Flags
- ✅ Sample Fish Species

## 🛠️ Migration Aracı Kullanımı

### Temel Komutlar

```bash
# Tüm bekleyen migration'ları çalıştır
node database/migrate.js up

# Migration durumunu göster
node database/migrate.js status

# Son migration'ı geri al
node database/migrate.js down

# Yeni migration dosyası oluştur
node database/migrate.js create "add new feature"
```

### Örnek Kullanım

```bash
# Yeni bir özellik için migration oluştur
node database/migrate.js create "add weather data"

# Oluşturulan dosyayı düzenle
# database/migrations/007_add_weather_data.sql

# Migration'ı çalıştır
node database/migrate.js up
```

## 📊 Database Şeması Özeti

### **Toplam Tablo Sayısı: 30+**

#### **Temel Tablolar (Öncelik 1)**
- `users` - Kullanıcı profilleri
- `posts` - İçerik paylaşımları
- `fish_species` - Balık türleri
- `spots` - Balıkçılık noktaları
- `likes`, `comments`, `follows` - Sosyal özellikler
- `notifications` - Bildirimler

#### **Ekipman Sistemi**
- `equipment` - Ekipman kataloğu
- `user_equipment` - Kullanıcı ekipmanları
- `equipment_categories` - Kategoriler
- `equipment_brands` - Markalar
- `equipment_reviews` - Değerlendirmeler
- `equipment_wishlist` - İstek listesi

#### **Admin & Moderasyon**
- `admin_logs` - Admin işlem kayıtları
- `reports` - Şikayet sistemi
- `system_settings` - Sistem ayarları
- `user_warnings` - Kullanıcı uyarıları
- `blocked_users` - Engelleme sistemi
- `content_moderation_queue` - Moderasyon kuyruğu

#### **Sosyal & Başarı Sistemi**
- `messages` - Mesajlaşma
- `message_threads` - Mesaj konuları
- `badge_definitions` - Rozet tanımları
- `user_badges` - Kullanıcı rozetleri
- `leaderboards` - Liderlik tabloları
- `user_achievement_stats` - Başarı istatistikleri

## 🔒 Güvenlik Özellikleri

### **Row Level Security (RLS)**
- ✅ Tüm tablolarda RLS aktif
- ✅ Kullanıcı bazlı erişim kontrolü
- ✅ Admin/moderatör yetkilendirmesi
- ✅ Engelleme sistemi entegrasyonu

### **Veri Koruması**
- ✅ Yasaklı kullanıcılar gizlenir
- ✅ Silinen içerik filtrelenir
- ✅ Özel mesajlar korunur
- ✅ Admin işlemleri loglanır

## 📈 Performans Optimizasyonları

### **Indexler**
- ✅ Primary key indexleri
- ✅ Foreign key indexleri
- ✅ Arama optimizasyonu indexleri
- ✅ Sıralama indexleri
- ✅ Composite indexler

### **Triggers & Functions**
- ✅ Otomatik sayaç güncellemeleri
- ✅ Timestamp güncellemeleri
- ✅ İstatistik hesaplamaları
- ✅ Veri tutarlılığı kontrolleri

## 🔄 Gelecek Migration'lar

### **Planlanan Özellikler**
- Weather integration (hava durumu)
- Tide data (gelgit verileri)
- Advanced analytics (gelişmiş analitik)
- Group messaging (grup mesajlaşması)
- Tournament system (turnuva sistemi)

### **Yeni Migration Oluşturma**

```bash
# Yeni özellik için migration oluştur
node database/migrate.js create "add tournament system"

# Dosya otomatik oluşturulur:
# database/migrations/007_add_tournament_system.sql
```

## 🚨 Önemli Notlar

### **Production'da Dikkat Edilecekler**
1. **Backup**: Migration öncesi mutlaka backup alın
2. **Test**: Staging ortamında test edin
3. **Downtime**: Büyük migration'lar için bakım modu
4. **Rollback**: Geri alma planı hazırlayın

### **Migration Yazma Kuralları**
1. **Idempotent**: Birden fazla çalıştırılabilir olmalı
2. **Backward Compatible**: Mümkünse geriye uyumlu
3. **Documented**: Açıklama ve yorumlar ekleyin
4. **Tested**: Test ortamında doğrulayın

## 🆘 Sorun Giderme

### **Migration Hatası**
```bash
# Migration durumunu kontrol et
node database/migrate.js status

# Hatalı migration'ı manuel olarak düzelt
# Supabase SQL Editor'da düzeltmeleri yap

# Migration kaydını sil (dikkatli!)
DELETE FROM migrations WHERE filename = 'problematic_migration.sql';
```

### **RLS Politika Hatası**
```sql
-- Politika çakışması durumunda
DROP POLICY IF EXISTS "policy_name" ON table_name;
CREATE POLICY "new_policy_name" ON table_name ...;
```

## 📞 Destek

Migration sistemi ile ilgili sorunlar için:
1. Migration durumunu kontrol edin: `node database/migrate.js status`
2. Supabase logs'ları inceleyin
3. Database schema'yı manuel kontrol edin
4. Gerekirse rollback yapın: `node database/migrate.js down`

---

**🎣 Happy Fishing with Fishivo! 🎣**
