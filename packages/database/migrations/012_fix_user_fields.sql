-- =====================================================
-- MIGRATION 012: FIX USER FIELDS
-- Created: 2025-06-17
-- Description: Fix user fields to match code expectations
-- =====================================================

-- Check if is_premium exists and drop if needed
DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'is_premium'
  ) THEN
    ALTER TABLE users DROP COLUMN is_premium;
    RAISE NOTICE 'Dropped is_premium column';
  END IF;
END $$;

-- Check if total_catches exists and rename if needed
DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'total_catches'
  ) THEN
    ALTER TABLE users RENAME COLUMN total_catches TO catches_count;
    RAISE NOTICE 'Renamed total_catches to catches_count';
  END IF;
END $$;

-- Check if total_spots exists and rename if needed
DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'total_spots'
  ) THEN
    ALTER TABLE users RENAME COLUMN total_spots TO spots_count;
    RAISE NOTICE 'Renamed total_spots to spots_count';
  END IF;
END $$;

-- Check if reputation_score exists and drop if needed
DO $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'reputation_score'
  ) THEN
    ALTER TABLE users DROP COLUMN reputation_score;
    RAISE NOTICE 'Dropped reputation_score column';
  END IF;
END $$;

-- Ensure provider exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'provider'
  ) THEN
    ALTER TABLE users ADD COLUMN provider TEXT DEFAULT 'email';
    RAISE NOTICE 'Added provider column';
  END IF;
END $$;

-- Ensure is_pro exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'is_pro'
  ) THEN
    ALTER TABLE users ADD COLUMN is_pro BOOLEAN DEFAULT FALSE;
    RAISE NOTICE 'Added is_pro column';
  END IF;
END $$;

-- Ensure catches_count exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'catches_count'
  ) THEN
    ALTER TABLE users ADD COLUMN catches_count INTEGER DEFAULT 0;
    RAISE NOTICE 'Added catches_count column';
  END IF;
END $$;

-- Ensure spots_count exists
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'spots_count'
  ) THEN
    ALTER TABLE users ADD COLUMN spots_count INTEGER DEFAULT 0;
    RAISE NOTICE 'Added spots_count column';
  END IF;
END $$;

-- Refresh schema cache
DO $$
BEGIN
  -- Force schema refresh
  ALTER TABLE users ADD COLUMN _tmp INTEGER;
  ALTER TABLE users DROP COLUMN _tmp;
  RAISE NOTICE 'Schema cache refreshed';
END $$; 