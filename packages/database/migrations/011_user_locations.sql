-- Migration: User Locations for Weather
-- Description: Create user_locations table for saving weather locations

-- Create user_locations table
CREATE TABLE IF NOT EXISTS user_locations (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  type VA<PERSON><PERSON><PERSON>(50) NOT NULL DEFAULT 'saved', -- 'current', 'saved', 'favorite'
  latitude DECIMAL(10, 8) NOT NULL,
  longitude DECIMAL(11, 8) NOT NULL,
  address TEXT,
  city VARCHAR(255),
  country VARCHAR(255),
  is_favorite BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_user_locations_user_id ON user_locations(user_id);
CREATE INDEX IF NOT EXISTS idx_user_locations_type ON user_locations(type);
CREATE INDEX IF NOT EXISTS idx_user_locations_coordinates ON user_locations(latitude, longitude);

-- Add RLS policies
ALTER TABLE user_locations ENABLE ROW LEVEL SECURITY;

-- Users can only see their own locations
CREATE POLICY user_locations_select_policy ON user_locations
  FOR SELECT USING (auth.uid() = user_id);

-- Users can only insert their own locations
CREATE POLICY user_locations_insert_policy ON user_locations
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can only update their own locations
CREATE POLICY user_locations_update_policy ON user_locations
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can only delete their own locations
CREATE POLICY user_locations_delete_policy ON user_locations
  FOR DELETE USING (auth.uid() = user_id);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_user_locations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER user_locations_updated_at_trigger
  BEFORE UPDATE ON user_locations
  FOR EACH ROW
  EXECUTE FUNCTION update_user_locations_updated_at(); 