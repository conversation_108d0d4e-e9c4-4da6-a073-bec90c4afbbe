-- =====================================================
-- MIGRATION 006: DEFAULT DATA
-- Created: 2024-01-20
-- Description: Initial data for system settings, equipment categories, etc.
-- =====================================================

-- =====================================================
-- SYSTEM SETTINGS
-- =====================================================

INSERT INTO system_settings (key, value, description, category) VALUES
-- General settings
('registration_enabled', 'true', 'Ye<PERSON> kull<PERSON>ı<PERSON>ı kayıtlarına izin ver', 'general'),
('app_name', '"Fishivo"', 'Uygulama adı', 'general'),
('app_version', '"1.0.0"', 'Uygulama versiyonu', 'general'),
('maintenance_mode', 'false', 'Bakım modu aktif/pasif', 'general'),

-- Moderation settings
('post_moderation', '"auto"', 'Post moderasyon modu: auto, manual', 'moderation'),
('spot_approval_required', 'true', 'Yeni spotlar için admin onayı gerekli', 'moderation'),
('auto_flag_profanity', 'true', 'Küfür içeren içerikleri otomatik işaretle', 'moderation'),
('review_moderation', '"auto"', 'Değerlendirme moderasyon modu', 'moderation'),

-- User limits
('daily_post_limit', '10', 'Kullanıcı başına günlük post limiti', 'limits'),
('max_file_size', '5242880', 'Maksimum dosya boyutu (bytes) - 5MB', 'limits'),
('daily_spot_limit', '3', 'Kullanıcı başına günlük spot ekleme limiti', 'limits'),
('daily_message_limit', '50', 'Kullanıcı başına günlük mesaj limiti', 'limits'),
('max_equipment_per_user', '100', 'Kullanıcı başına maksimum ekipman sayısı', 'limits'),
('max_images_per_post', '5', 'Post başına maksimum resim sayısı', 'limits'),

-- Pro membership pricing
('pro_monthly_price', '29.99', 'Aylık Pro üyelik fiyatı (TRY)', 'pricing'),
('pro_yearly_price', '299.99', 'Yıllık Pro üyelik fiyatı (TRY)', 'pricing'),
('pro_features', '["unlimited_posts", "advanced_analytics", "priority_support"]', 'Pro üyelik özellikleri', 'pricing'),

-- Security settings
('password_min_length', '8', 'Minimum şifre uzunluğu', 'security'),
('session_timeout', '86400', 'Oturum zaman aşımı (saniye)', 'security'),
('max_login_attempts', '5', 'Maksimum giriş denemesi', 'security'),
('account_lockout_duration', '1800', 'Hesap kilitleme süresi (saniye)', 'security'),

-- Feature flags
('enable_messaging', 'true', 'Mesajlaşma özelliği aktif', 'features'),
('enable_equipment_reviews', 'true', 'Ekipman değerlendirmeleri aktif', 'features'),

('enable_weather_integration', 'false', 'Hava durumu entegrasyonu aktif', 'features'),
('enable_tide_data', 'false', 'Gelgit verileri aktif', 'features')

ON CONFLICT (key) DO NOTHING;

-- =====================================================
-- EQUIPMENT CATEGORIES
-- =====================================================

INSERT INTO equipment_categories (id, name, icon, sort_order) VALUES
('reel', 'Makine', 'settings', 1),
('rod', 'Olta Kamışı', 'activity', 2),
('lure', 'Yapay Yem', 'fish', 3),
('bait', 'Canlı Yem', 'circle', 4),
('line', 'Misina & İp', 'activity', 5),
('hook', 'İğne & Terminal', 'anchor', 6),
('accessory', 'Aksesuarlar', 'package', 7),
('clothing', 'Giyim & Güvenlik', 'user', 8)
ON CONFLICT (id) DO NOTHING;

-- Subcategories for reels
INSERT INTO equipment_categories (id, name, icon, parent_id, sort_order) VALUES
('spinning_reel', 'Spinning Makine', 'settings', 'reel', 1),
('casting_reel', 'Casting Makine', 'settings', 'reel', 2),
('surf_reel', 'Surf Makine', 'settings', 'reel', 3),
('trolling_reel', 'Trolling Makine', 'settings', 'reel', 4)
ON CONFLICT (id) DO NOTHING;

-- Subcategories for rods
INSERT INTO equipment_categories (id, name, icon, parent_id, sort_order) VALUES
('spinning_rod', 'Spinning Kamış', 'activity', 'rod', 1),
('casting_rod', 'Casting Kamış', 'activity', 'rod', 2),
('surf_rod', 'Surf Kamış', 'activity', 'rod', 3),
('trolling_rod', 'Trolling Kamış', 'activity', 'rod', 4),
('fly_rod', 'Fly Kamış', 'activity', 'rod', 5)
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- EQUIPMENT BRANDS
-- =====================================================

INSERT INTO equipment_brands (name, country, description) VALUES
('Shimano', 'Japonya', 'Dünya çapında tanınan balıkçılık ekipmanları üreticisi'),
('Daiwa', 'Japonya', 'Yenilikçi balıkçılık teknolojileri lideri'),
('Abu Garcia', 'İsveç', 'Geleneksel İskandinav balıkçılık markası'),
('Penn', 'ABD', 'Dayanıklı deniz balıkçılığı ekipmanları'),
('Okuma', 'Tayvan', 'Kaliteli ve uygun fiyatlı balıkçılık ekipmanları'),
('Rapala', 'Finlandiya', 'Dünyaca ünlü yapay yem üreticisi'),
('Berkley', 'ABD', 'Misina ve yapay yem uzmanı'),
('Gamakatsu', 'Japonya', 'Premium balık iğnesi üreticisi'),
('Owner', 'Japonya', 'Yüksek kaliteli terminal takım üreticisi'),
('Mustad', 'Norveç', 'Geleneksel balık iğnesi markası')
ON CONFLICT (name) DO NOTHING;



-- =====================================================
-- FEATURE FLAGS
-- =====================================================

INSERT INTO feature_flags (id, name, description, is_enabled) VALUES
('messaging_system', 'Mesajlaşma Sistemi', 'Kullanıcılar arası mesajlaşma özelliği', true),
('equipment_reviews', 'Ekipman Değerlendirmeleri', 'Ekipman değerlendirme ve puanlama sistemi', true),


('spot_approval', 'Spot Onay Sistemi', 'Yeni spotlar için admin onayı', true),
('weather_integration', 'Hava Durumu Entegrasyonu', 'Hava durumu verileri entegrasyonu', false),
('tide_data', 'Gelgit Verileri', 'Gelgit bilgileri entegrasyonu', false),
('pro_membership', 'Pro Üyelik', 'Premium üyelik sistemi', true),
('push_notifications', 'Push Bildirimleri', 'Mobil push bildirimleri', true),
('social_login', 'Sosyal Medya Girişi', 'Google/Facebook ile giriş', true)
ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- SAMPLE FISH SPECIES
-- =====================================================

INSERT INTO fish_species (name, scientific_name, category, min_size, max_size, avg_weight, season, habitat, bait, difficulty, description, min_legal_size) VALUES
('Levrek', 'Dicentrarchus labrax', 'Deniz Balığı', 25, 80, 2.5, '["İlkbahar", "Yaz", "Sonbahar"]', '["Kıyı", "Kayalık"]', '["Canlı yem", "Suni yem"]', 'Orta', 'Akdeniz ve Karadeniz''de yaygın bulunan popüler av balığı', 25),
('Çupra', 'Sparus aurata', 'Deniz Balığı', 20, 50, 1.2, '["İlkbahar", "Yaz"]', '["Kıyı", "Kayalık"]', '["Karides", "Midye"]', 'Kolay', 'Lezzetli eti ile bilinen değerli balık türü', 20),
('İstavrit', 'Trachurus trachurus', 'Deniz Balığı', 15, 35, 0.3, '["Yaz", "Sonbahar"]', '["Açık deniz", "Kıyı"]', '["Küçük balık", "Suni yem"]', 'Kolay', 'Sürü halinde yaşayan hızlı balık türü', 15),
('Sazan', 'Cyprinus carpio', 'Tatlı Su Balığı', 30, 100, 5.0, '["İlkbahar", "Yaz", "Sonbahar"]', '["Göl", "Nehir"]', '["Mısır", "Ekmek"]', 'Zor', 'Tatlı sularda yaşayan iri balık türü', 30),
('Alabalık', 'Salmo trutta', 'Tatlı Su Balığı', 20, 60, 1.5, '["İlkbahar", "Sonbahar"]', '["Nehir", "Göl"]', '["Solucan", "Suni yem"]', 'Orta', 'Soğuk suları seven değerli balık türü', 22),
('Kefal', 'Mugil cephalus', 'Deniz Balığı', 20, 60, 1.8, '["Yaz", "Sonbahar"]', '["Kıyı", "Lagün"]', '["Ekmek", "Mısır"]', 'Kolay', 'Kıyı sularında yaygın bulunan balık', 20),
('Barbun', 'Mullus barbatus', 'Deniz Balığı', 10, 25, 0.2, '["Yaz", "Sonbahar"]', '["Kumsal", "Çamur"]', '["Karides", "Solucan"]', 'Kolay', 'Küçük ama lezzetli deniz balığı', 11),
('Mercan', 'Pagrus pagrus', 'Deniz Balığı', 25, 70, 2.0, '["İlkbahar", "Yaz"]', '["Kayalık", "Derin deniz"]', '["Balık eti", "Karides"]', 'Zor', 'Derin sularda yaşayan değerli balık', 25)
ON CONFLICT DO NOTHING;

-- =====================================================
-- SAMPLE SPOTS DATA
-- =====================================================

-- Insert sample fishing spots
INSERT INTO spots (
  name, description, location, spot_type, access_type, difficulty,
  depth_min, depth_max, bottom_type, facilities, fish_species,
  image_url, status, user_id, approved_by, approved_at
) VALUES
(
  'Galata Köprüsü Altı',
  'İstanbul''un en popüler balık tutma noktalarından biri. Özellikle lüfer ve palamut için ideal.',
  '{"latitude": 41.0186, "longitude": 28.9744, "address": "Galata Köprüsü, Karaköy", "city": "İstanbul", "country": "Türkiye"}',
  'fishing',
  'public',
  'Kolay',
  5,
  15,
  '["Kayalık", "Çamur"]',
  '["Park", "WC", "Kafe", "Ulaşım"]',
  '[1, 3, 6]', -- Levrek, İstavrit, Kefal
  'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
  'approved',
  '550e8400-e29b-41d4-a716-446655440000',
  '550e8400-e29b-41d4-a716-446655440000',
  NOW()
),
(
  'Kadıköy İskelesi',
  'Marmara Denizi''nde güzel bir balık tutma noktası. Sabah erken saatlerde çok verimli.',
  '{"latitude": 40.9833, "longitude": 29.0167, "address": "Kadıköy İskelesi", "city": "İstanbul", "country": "Türkiye"}',
  'fishing',
  'public',
  'Kolay',
  3,
  12,
  '["Kum", "Çakıl"]',
  '["Park", "WC", "Kafe", "Ulaşım", "Otopark"]',
  '[1, 2, 3, 7]', -- Levrek, Çupra, İstavrit, Barbun
  'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=400&h=300&fit=crop',
  'approved',
  '550e8400-e29b-41d4-a716-446655440001',
  '550e8400-e29b-41d4-a716-446655440000',
  NOW()
),
(
  'Boğaziçi Köprüsü',
  'Boğaz''ın en güzel manzaralı balık tutma noktası. Çupra ve levrek için mükemmel.',
  '{"latitude": 41.0391, "longitude": 29.0350, "address": "Boğaziçi Köprüsü", "city": "İstanbul", "country": "Türkiye"}',
  'fishing',
  'public',
  'Orta',
  8,
  25,
  '["Kayalık", "Derin"]',
  '["Park", "WC", "Manzara"]',
  '[1, 2, 8]', -- Levrek, Çupra, Mercan
  'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400&h=300&fit=crop',
  'approved',
  '550e8400-e29b-41d4-a716-446655440002',
  '550e8400-e29b-41d4-a716-446655440000',
  NOW()
),
(
  'Sarıyer Sahili',
  'Karadeniz''e açılan güzel bir nokta. Lüfer ve palamut mevsiminde çok verimli.',
  '{"latitude": 41.1667, "longitude": 29.0500, "address": "Sarıyer Sahili", "city": "İstanbul", "country": "Türkiye"}',
  'fishing',
  'public',
  'Orta',
  10,
  30,
  '["Kayalık", "Kum"]',
  '["Park", "WC", "Restoran"]',
  '[1, 3, 6]', -- Levrek, İstavrit, Kefal
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
  'approved',
  '550e8400-e29b-41d4-a716-446655440003',
  '550e8400-e29b-41d4-a716-446655440000',
  NOW()
),
(
  'Büyükada İskelesi',
  'Adalar''ın en popüler balık tutma noktası. Sakin ve huzurlu bir ortam.',
  '{"latitude": 40.8667, "longitude": 29.1167, "address": "Büyükada İskelesi", "city": "İstanbul", "country": "Türkiye"}',
  'fishing',
  'public',
  'Kolay',
  5,
  18,
  '["Kum", "Çakıl"]',
  '["Park", "WC", "Kafe", "Ulaşım", "Otopark"]',
  '[2, 3, 7]', -- Çupra, İstavrit, Barbun
  'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
  'approved',
  '550e8400-e29b-41d4-a716-446655440000',
  '550e8400-e29b-41d4-a716-446655440000',
  NOW()
),
(
  'Premium Gizli Spot',
  'Sadece premium üyeler için özel bir balık tutma noktası.',
  '{"latitude": 41.0500, "longitude": 29.0200, "address": "Gizli Konum", "city": "İstanbul", "country": "Türkiye"}',
  'fishing',
  'private',
  'Zor',
  15,
  40,
  '["Kayalık", "Derin"]',
  '["Özel Alan", "Premium Hizmet"]',
  '[1, 2, 8]', -- Levrek, Çupra, Mercan
  'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
  'approved',
  '550e8400-e29b-41d4-a716-446655440000', -- Premium user
  '550e8400-e29b-41d4-a716-446655440000',
  NOW()
);

-- Update spot statistics
UPDATE spots SET 
  rating = 4.5,
  rating_count = 12,
  catches_count = 25
WHERE name = 'Galata Köprüsü Altı';

UPDATE spots SET 
  rating = 4.2,
  rating_count = 8,
  catches_count = 18
WHERE name = 'Kadıköy İskelesi';

UPDATE spots SET 
  rating = 4.8,
  rating_count = 15,
  catches_count = 32
WHERE name = 'Boğaziçi Köprüsü';

UPDATE spots SET 
  rating = 4.0,
  rating_count = 6,
  catches_count = 14
WHERE name = 'Sarıyer Sahili';

UPDATE spots SET 
  rating = 4.3,
  rating_count = 10,
  catches_count = 22
WHERE name = 'Büyükada İskelesi';

UPDATE spots SET 
  rating = 4.9,
  rating_count = 5,
  catches_count = 8
WHERE name = 'Premium Gizli Spot';
