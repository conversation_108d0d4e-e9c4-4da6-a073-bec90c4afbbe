-- =====================================================
-- MIGRATION 017: ADD USER SOCIAL FIELDS
-- Created: 2024-06-24
-- Description: Adding social media fields to users table
-- =====================================================

-- Add Instagram URL field if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'instagram_url'
  ) THEN
    ALTER TABLE users ADD COLUMN instagram_url TEXT;
    RAISE NOTICE 'Added instagram_url column';
  END IF;
END $$;

-- Add Facebook URL field if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'facebook_url'
  ) THEN
    ALTER TABLE users ADD COLUMN facebook_url TEXT;
    RAISE NOTICE 'Added facebook_url column';
  END IF;
END $$;

-- Add YouTube URL field if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'youtube_url'
  ) THEN
    ALTER TABLE users ADD COLUMN youtube_url TEXT;
    RAISE NOTICE 'Added youtube_url column';
  END IF;
END $$;

-- Add Twitter/X URL field if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'twitter_url'
  ) THEN
    ALTER TABLE users ADD COLUMN twitter_url TEXT;
    RAISE NOTICE 'Added twitter_url column';
  END IF;
END $$;

-- Update existing users with empty values if NULL
UPDATE users
SET 
  instagram_url = '',
  facebook_url = '',
  youtube_url = '',
  twitter_url = ''
WHERE 
  instagram_url IS NULL OR
  facebook_url IS NULL OR
  youtube_url IS NULL OR
  twitter_url IS NULL;

-- Add comments to columns
COMMENT ON COLUMN users.instagram_url IS 'Instagram URL or username for user profile';
COMMENT ON COLUMN users.facebook_url IS 'Facebook URL or username for user profile';
COMMENT ON COLUMN users.youtube_url IS 'YouTube URL or channel name for user profile';
COMMENT ON COLUMN users.twitter_url IS 'Twitter/X URL or handle for user profile';

-- Refresh schema cache
DO $$
BEGIN
  -- Force schema refresh
  ALTER TABLE users ADD COLUMN _tmp INTEGER;
  ALTER TABLE users DROP COLUMN _tmp;
  RAISE NOTICE 'Schema cache refreshed';
END $$; 