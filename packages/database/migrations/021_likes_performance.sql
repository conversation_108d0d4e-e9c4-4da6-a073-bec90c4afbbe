-- Performance optimizations for likes system
-- Migration name: 021_likes_performance

-- Add indexes for faster queries on likes table
CREATE INDEX IF NOT EXISTS idx_likes_post_id_created_at ON likes(post_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_likes_user_id_created_at ON likes(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_likes_recent ON likes(created_at DESC);

-- Add timestamp columns to likes table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'likes' AND column_name = 'updated_at') THEN
    ALTER TABLE likes ADD COLUMN updated_at TIMESTAMPTZ DEFAULT NOW();
  END IF;
END$$;

-- Create functions for faster like operations

-- Function to check if user has liked a post
CREATE OR REPLACE FUNCTION user_liked_post(user_uuid UUID, post_id_param BIGINT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM likes
    WHERE user_id = user_uuid AND post_id = post_id_param
    LIMIT 1
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all posts liked by a user (with pagination)
CREATE OR REPLACE FUNCTION get_user_liked_posts(user_uuid UUID, limit_param INT DEFAULT 20, offset_param INT DEFAULT 0)
RETURNS TABLE (
  post_id BIGINT,
  liked_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT l.post_id, l.created_at AS liked_at
  FROM likes l
  WHERE l.user_id = user_uuid
  ORDER BY l.created_at DESC
  LIMIT limit_param
  OFFSET offset_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get recent likers of a post (with pagination)
CREATE OR REPLACE FUNCTION get_post_likers(post_id_param BIGINT, limit_param INT DEFAULT 20, offset_param INT DEFAULT 0)
RETURNS TABLE (
  user_id UUID,
  username TEXT,
  full_name TEXT,
  avatar_url TEXT,
  liked_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.id AS user_id,
    u.username,
    u.full_name,
    u.avatar_url,
    l.created_at AS liked_at
  FROM likes l
  JOIN users u ON l.user_id = u.id
  WHERE l.post_id = post_id_param
  ORDER BY l.created_at DESC
  LIMIT limit_param
  OFFSET offset_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Optimized materialized view for user's liked posts (refreshed periodically)
CREATE MATERIALIZED VIEW IF NOT EXISTS mv_user_likes_summary AS
SELECT 
  user_id,
  COUNT(*) AS total_likes,
  MAX(created_at) AS last_liked_at,
  array_agg(post_id ORDER BY created_at DESC LIMIT 50) AS recent_liked_posts
FROM likes
GROUP BY user_id
WITH DATA;

-- Index on the materialized view
CREATE INDEX IF NOT EXISTS idx_mv_user_likes_summary_user_id ON mv_user_likes_summary(user_id);

-- Function to refresh the materialized view (can be called by a schedule)
CREATE OR REPLACE FUNCTION refresh_likes_materialized_view()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_user_likes_summary;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Denormalized likes count in posts table to avoid counting on every request
DO $$
BEGIN
  -- Update posts table to ensure likes_count exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'posts' AND column_name = 'likes_count') THEN
    ALTER TABLE posts ADD COLUMN likes_count INTEGER DEFAULT 0;
  END IF;
  
  -- Initialize counts if not already set
  UPDATE posts p
  SET likes_count = (SELECT COUNT(*) FROM likes WHERE post_id = p.id)
  WHERE p.likes_count = 0 OR p.likes_count IS NULL;
END$$;

-- Custom like/unlike trigger function with optimizations and transaction support
CREATE OR REPLACE FUNCTION process_like_event()
RETURNS TRIGGER AS $$
DECLARE
  current_count INTEGER;
BEGIN
  -- Like added
  IF TG_OP = 'INSERT' THEN
    -- Increment post like count
    UPDATE posts 
    SET likes_count = likes_count + 1,
        updated_at = NOW()
    WHERE id = NEW.post_id;
    
    -- Notify system about the new like
    PERFORM pg_notify('post_liked', json_build_object(
      'post_id', NEW.post_id,
      'user_id', NEW.user_id,
      'created_at', NEW.created_at
    )::text);
    
    RETURN NEW;
  -- Like removed
  ELSIF TG_OP = 'DELETE' THEN
    -- Get current count to ensure we don't go below zero
    SELECT likes_count INTO current_count FROM posts WHERE id = OLD.post_id;
    
    -- Decrement post like count (never below 0)
    UPDATE posts 
    SET likes_count = GREATEST(0, likes_count - 1),
        updated_at = NOW()
    WHERE id = OLD.post_id;
    
    -- Notify system about the removed like
    PERFORM pg_notify('post_unliked', json_build_object(
      'post_id', OLD.post_id,
      'user_id', OLD.user_id
    )::text);
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if exists to avoid conflicts
DROP TRIGGER IF EXISTS trigger_process_like ON likes;

-- Create optimized trigger for likes table
CREATE TRIGGER trigger_process_like
AFTER INSERT OR DELETE ON likes
FOR EACH ROW
EXECUTE FUNCTION process_like_event();

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION user_liked_post(UUID, BIGINT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_liked_posts(UUID, INT, INT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_post_likers(BIGINT, INT, INT) TO authenticated;

-- Comment on objects to document
COMMENT ON FUNCTION user_liked_post IS 'Efficiently check if a user has liked a specific post';
COMMENT ON FUNCTION get_user_liked_posts IS 'Get paginated list of posts liked by a specific user';
COMMENT ON FUNCTION get_post_likers IS 'Get paginated list of users who liked a specific post';
COMMENT ON MATERIALIZED VIEW mv_user_likes_summary IS 'Materialized summary of user likes for quick access'; 