-- =====================================================
-- MIGRATION 001: INITIAL SCHEMA
-- Created: 2024-01-20
-- Description: Core database structure for Fishivo
-- =====================================================

-- =====================================================
-- 1. CORE USER SYSTEM
-- =====================================================

-- Users table (extends auth.users)
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT NOT NULL UNIQUE,
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  location TEXT,
  phone TEXT,
  website TEXT,
  
  -- Social stats
  followers_count INTEGER DEFAULT 0,
  following_count INTEGER DEFAULT 0,
  catches_count INTEGER DEFAULT 0,
  spots_count INTEGER DEFAULT 0,
  
  -- User preferences
  privacy_settings JSONB DEFAULT '{"profile": "public", "catches": "public", "location": "friends"}',
  notification_settings JSONB DEFAULT '{"likes": true, "comments": true, "follows": true, "messages": true}',
  
  -- Pro membership
  is_pro BOOLEAN DEFAULT FALSE,
  pro_until TIMESTAMPTZ,
  pro_features JSONB DEFAULT '[]',
  
  -- Admin & moderation
  role TEXT DEFAULT 'user' CHECK (role IN ('user', 'moderator', 'admin', 'super_admin')),
  permissions JSONB DEFAULT '[]',
  banned_until TIMESTAMPTZ,
  ban_reason TEXT,
  
  -- User equipment (JSON array)
  equipments JSONB DEFAULT '[]',
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  last_active_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 2. CONTENT SYSTEM (POSTS/CATCHES)
-- =====================================================

-- Posts table (catches/shares)
CREATE TABLE IF NOT EXISTS posts (
  id BIGSERIAL PRIMARY KEY,
  content TEXT NOT NULL,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Media
  image_url TEXT,
  images JSONB DEFAULT '[]', -- Multiple images support
  
  -- Location data
  location JSONB, -- {latitude, longitude, address, city, country}
  
  -- Catch details
  catch_details JSONB, -- {species_id, species_name, weight, length, bait_used, weather_conditions, water_temperature, time_of_day, technique, equipment_used}
  
  -- Social stats
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  shares_count INTEGER DEFAULT 0,
  
  -- Moderation
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'hidden', 'deleted', 'pending_review')),
  moderation_notes TEXT,
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 3. FISH SPECIES SYSTEM
-- =====================================================

-- Fish species table
CREATE TABLE IF NOT EXISTS fish_species (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  scientific_name TEXT,
  category TEXT, -- 'Deniz Balığı', 'Tatlı Su Balığı', 'Anadrom'
  
  -- Physical characteristics
  min_size INTEGER, -- cm
  max_size INTEGER, -- cm
  avg_weight DECIMAL(8,3), -- kg
  max_weight DECIMAL(8,3), -- kg
  
  -- Habitat and behavior
  season JSONB, -- ['İlkbahar', 'Yaz', 'Sonbahar', 'Kış']
  habitat JSONB, -- ['Kıyı', 'Kayalık', 'Kumsal', 'Derin deniz']
  depth_range JSONB, -- {min: 0, max: 100}
  water_type JSONB, -- ['Tuzlu', 'Tatlı', 'Acı']
  
  -- Fishing info
  bait JSONB, -- ['Canlı yem', 'Suni yem', 'Karides']
  techniques JSONB, -- ['Jigging', 'Trolling', 'Bottom fishing']
  difficulty TEXT CHECK (difficulty IN ('Kolay', 'Orta', 'Zor')),
  best_time JSONB, -- ['Sabah', 'Öğle', 'Akşam', 'Gece']
  
  -- Media and content
  image_url TEXT,
  images JSONB DEFAULT '[]',
  description TEXT,
  tips TEXT,
  
  -- Regulations
  min_legal_size INTEGER, -- cm
  daily_limit INTEGER,
  seasonal_restrictions JSONB,
  
  -- Admin
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'draft', 'archived')),
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 4. SPOTS SYSTEM
-- =====================================================

-- Fishing spots table
CREATE TABLE IF NOT EXISTS spots (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  
  -- Location
  location JSONB NOT NULL, -- {latitude, longitude, address, city, country}
  
  -- Spot details
  spot_type TEXT NOT NULL CHECK (spot_type IN ('fishing', 'marina', 'bait_shop', 'restaurant', 'accommodation')),
  access_type TEXT DEFAULT 'public' CHECK (access_type IN ('public', 'private', 'paid')),
  difficulty TEXT CHECK (difficulty IN ('Kolay', 'Orta', 'Zor')),
  
  -- Physical characteristics
  depth_min INTEGER, -- meters
  depth_max INTEGER, -- meters
  area_size DECIMAL(10,2), -- km²
  bottom_type JSONB, -- ['Kum', 'Çakıl', 'Kayalık', 'Çamur']
  
  -- Facilities and features
  facilities JSONB DEFAULT '[]', -- ['Park', 'WC', 'Kafe', 'Tekne rampası']
  fish_species JSONB DEFAULT '[]', -- Available fish species
  
  -- Regulations
  regulations JSONB DEFAULT '{}', -- {min_size_limits, seasonal_restrictions, daily_limit, license_required}
  
  -- Media
  image_url TEXT,
  images JSONB DEFAULT '[]',
  
  -- Social and ratings
  rating DECIMAL(2,1) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  catches_count INTEGER DEFAULT 0,
  
  -- Admin and moderation
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'archived')),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  approved_by UUID REFERENCES users(id) ON DELETE SET NULL,
  approved_at TIMESTAMPTZ,
  rejection_reason TEXT,
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 5. SOCIAL SYSTEM
-- =====================================================

-- Likes table
CREATE TABLE IF NOT EXISTS likes (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  post_id BIGINT REFERENCES posts(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (user_id, post_id)
);

-- Comments table
CREATE TABLE IF NOT EXISTS comments (
  id BIGSERIAL PRIMARY KEY,
  content TEXT NOT NULL,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  post_id BIGINT REFERENCES posts(id) ON DELETE CASCADE,
  parent_id BIGINT REFERENCES comments(id) ON DELETE CASCADE, -- For replies
  
  -- Moderation
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'hidden', 'deleted')),
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Follows table
CREATE TABLE IF NOT EXISTS follows (
  id BIGSERIAL PRIMARY KEY,
  follower_id UUID REFERENCES users(id) ON DELETE CASCADE,
  following_id UUID REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (follower_id, following_id),
  CHECK (follower_id != following_id)
);

-- =====================================================
-- 6. NOTIFICATION SYSTEM
-- =====================================================

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('like', 'comment', 'follow', 'message', 'mention', 'system')),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  
  -- Related data
  data JSONB, -- {post_id, user_id, etc.}
  action_url TEXT,
  
  -- Status
  read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- =====================================================
-- 7. BASIC INDEXES
-- =====================================================

-- User indexes
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at DESC);

-- Post indexes
CREATE INDEX IF NOT EXISTS idx_posts_user_id ON posts(user_id);
CREATE INDEX IF NOT EXISTS idx_posts_created_at ON posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);

-- Species indexes
CREATE INDEX IF NOT EXISTS idx_fish_species_name ON fish_species(name);
CREATE INDEX IF NOT EXISTS idx_fish_species_category ON fish_species(category);
CREATE INDEX IF NOT EXISTS idx_fish_species_status ON fish_species(status);

-- Spot indexes
CREATE INDEX IF NOT EXISTS idx_spots_user_id ON spots(user_id);
CREATE INDEX IF NOT EXISTS idx_spots_status ON spots(status);
CREATE INDEX IF NOT EXISTS idx_spots_spot_type ON spots(spot_type);

-- Social indexes
CREATE INDEX IF NOT EXISTS idx_likes_post_id ON likes(post_id);
CREATE INDEX IF NOT EXISTS idx_likes_user_id ON likes(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_post_id ON comments(post_id);
CREATE INDEX IF NOT EXISTS idx_follows_follower_id ON follows(follower_id);
CREATE INDEX IF NOT EXISTS idx_follows_following_id ON follows(following_id);

-- Notification indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
