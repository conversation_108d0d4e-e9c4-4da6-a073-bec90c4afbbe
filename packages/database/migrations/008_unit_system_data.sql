-- =====================================================
-- MIGRATION 008: UNIT SYSTEM DEFAULT DATA
-- Created: 2024-01-20
-- Description: Default data for unit system (categories, units, regional defaults)
-- =====================================================

-- =====================================================
-- 1. UNIT CATEGORIES
-- =====================================================

INSERT INTO unit_categories (id, name, name_en, description, icon, base_unit, sort_order) VALUES
('weight', 'Ağırlık', 'Weight', 'Balık ağırlığı ve ekipman ağırlığı ölçü birimleri', 'scale', 'kg', 1),
('length', 'Uzunluk', 'Length', 'Balık boyu ve ekipman uzunluğu ölçü birimleri', 'ruler', 'cm', 2),
('distance', 'Mesafe', 'Distance', 'Konum ve seyahat mesafesi ölçü birimleri', 'map-pin', 'km', 3),
('temperature', 'Sıcaklık', 'Temperature', 'Su sıcaklığı ve hava sıcaklığı ölçü birimleri', 'thermometer', 'celsius', 4),
('depth', 'Derinlik', 'Depth', 'Su derinliği ölçü birimleri', 'arrow-down', 'meters', 5),
('speed', 'Hız', 'Speed', 'Rüzgar hızı ve tekne hızı ölçü birimleri', 'zap', 'kmh', 6),
('pressure', 'Basınç', 'Pressure', 'Hava basıncı ölçü birimleri', 'gauge', 'hpa', 7)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  description = EXCLUDED.description,
  icon = EXCLUDED.icon,
  base_unit = EXCLUDED.base_unit,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 2. WEIGHT UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('kg', 'weight', 'Kilogram', 'Kilogram', 'kg', true, 1.0, 2, 0.001, 1000, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 1),
('g', 'weight', 'Gram', 'Gram', 'g', false, 0.001, 0, 1, 1000000, '["TR", "EU", "METRIC", "GLOBAL"]', 60, '[]', 2),
('lbs', 'weight', 'Pound', 'Pound', 'lbs', false, 2.20462, 2, 0.002, 2204, '["US", "UK", "IMPERIAL"]', 75, '["US", "UK", "CA"]', 3),
('oz', 'weight', 'Ons', 'Ounce', 'oz', false, 35.274, 1, 0.035, 35274, '["US", "UK", "IMPERIAL"]', 45, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 3. LENGTH UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('cm', 'length', 'Santimetre', 'Centimeter', 'cm', true, 1.0, 1, 0.1, 1000, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 1),
('m', 'length', 'Metre', 'Meter', 'm', false, 100.0, 2, 0.001, 10, '["TR", "EU", "METRIC", "GLOBAL"]', 70, '[]', 2),
('inch', 'length', 'İnç', 'Inch', 'in', false, 0.393701, 1, 0.04, 394, '["US", "UK", "IMPERIAL"]', 75, '["US", "UK", "CA"]', 3),
('ft', 'length', 'Feet', 'Feet', 'ft', false, 3.28084, 1, 0.3, 36000, '["US", "UK", "IMPERIAL"]', 90, '["US", "UK", "CA"]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 4. DISTANCE UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('km', 'distance', 'Kilometre', 'Kilometer', 'km', true, 1.0, 2, 0.001, 50000, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 1),
('m_dist', 'distance', 'Metre', 'Meter', 'm', false, 0.001, 0, 1, 50000000, '["TR", "EU", "METRIC", "GLOBAL"]', 60, '[]', 2),
('miles', 'distance', 'Mil', 'Miles', 'mi', false, 0.621371, 2, 0.001, 31069, '["US", "UK", "IMPERIAL"]', 75, '["US", "UK", "CA"]', 3),
('nm', 'distance', 'Deniz Mili', 'Nautical Mile', 'nm', false, 0.539957, 2, 0.001, 26998, '["MARINE", "AVIATION"]', 50, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 5. TEMPERATURE UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, conversion_formula, reverse_formula, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('celsius', 'temperature', 'Santigrat', 'Celsius', '°C', true, NULL, NULL, NULL, 1, -50, 60, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP", "UK"]', 1),
('fahrenheit', 'temperature', 'Fahrenheit', 'Fahrenheit', '°F', false, NULL, '(C * 9/5) + 32', '(F - 32) * 5/9', 1, -58, 140, '["US", "IMPERIAL"]', 70, '["US"]', 2)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  conversion_formula = EXCLUDED.conversion_formula,
  reverse_formula = EXCLUDED.reverse_formula,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 6. DEPTH UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, use_case, sort_order) VALUES
('meters', 'depth', 'Metre', 'Meters', 'm', true, 1.0, 1, 0.1, 11000, '["TR", "EU", "METRIC", "GLOBAL"]', 95, '["TR", "EU"]', 'general', 1),
('feet', 'depth', 'Feet', 'Feet', 'ft', false, 3.28084, 1, 0.3, 36000, '["US", "UK", "IMPERIAL"]', 90, '["US", "UK", "CA"]', 'general', 2),
('fathoms', 'depth', 'Kulaç', 'Fathoms', 'fath', false, 0.546806649, 1, 0.05, 6000, '["MARINE"]', 85, '[]', 'marine', 3)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  use_case = EXCLUDED.use_case,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 7. SPEED UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('kmh', 'speed', 'Kilometre/Saat', 'Kilometer per Hour', 'km/h', true, 1.0, 1, 0, 500, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP"]', 1),
('mph', 'speed', 'Mil/Saat', 'Miles per Hour', 'mph', false, 1.60934, 1, 0, 311, '["US", "UK", "IMPERIAL"]', 75, '["US", "UK", "CA"]', 2),
('knots', 'speed', 'Knot', 'Knots', 'kn', false, 1.852, 1, 0, 270, '["MARINE", "AVIATION"]', 85, '[]', 3),
('ms', 'speed', 'Metre/Saniye', 'Meter/Second', 'm/s', false, 0.277777778, 1, 0, 139, '["SCIENTIFIC"]', 60, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 8. PRESSURE UNITS
-- =====================================================

INSERT INTO unit_definitions (id, category_id, name, name_en, symbol, is_base_unit, conversion_factor, precision_digits, min_value, max_value, regions, popularity, default_for_regions, sort_order) VALUES
('hpa', 'pressure', 'Hektopaskal', 'Hectopascal', 'hPa', true, 1.0, 0, 800, 1100, '["TR", "EU", "METRIC", "GLOBAL"]', 85, '["TR", "DE", "FR", "IT", "ES", "JP", "UK"]', 1),
('mbar', 'pressure', 'Milibar', 'Millibar', 'mbar', false, 1.0, 0, 800, 1100, '["EU", "METRIC"]', 70, '[]', 2),
('inhg', 'pressure', 'İnç Civa', 'Inches of Mercury', 'inHg', false, 33.8639, 2, 23.62, 32.48, '["US", "IMPERIAL"]', 65, '["US"]', 3),
('mmhg', 'pressure', 'Milimetre Civa', 'Millimeters of Mercury', 'mmHg', false, 1.33322, 0, 600, 825, '["MEDICAL", "SCIENTIFIC"]', 40, '[]', 4)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  name_en = EXCLUDED.name_en,
  symbol = EXCLUDED.symbol,
  is_base_unit = EXCLUDED.is_base_unit,
  conversion_factor = EXCLUDED.conversion_factor,
  precision_digits = EXCLUDED.precision_digits,
  min_value = EXCLUDED.min_value,
  max_value = EXCLUDED.max_value,
  regions = EXCLUDED.regions,
  popularity = EXCLUDED.popularity,
  default_for_regions = EXCLUDED.default_for_regions,
  sort_order = EXCLUDED.sort_order,
  updated_at = NOW();

-- =====================================================
-- 9. REGIONAL DEFAULTS
-- =====================================================

INSERT INTO regional_unit_defaults (region_code, region_name, weight_unit, length_unit, distance_unit, temperature_unit, depth_unit, speed_unit, pressure_unit, currency, date_format, number_format) VALUES
('TR', 'Türkiye', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'TRY', 'DD/MM/YYYY', '{"decimal": ",", "thousands": "."}'),
('US', 'United States', 'lbs', 'inch', 'miles', 'fahrenheit', 'feet', 'mph', 'inhg', 'USD', 'MM/DD/YYYY', '{"decimal": ".", "thousands": ","}'),
('UK', 'United Kingdom', 'lbs', 'inch', 'miles', 'celsius', 'feet', 'mph', 'hpa', 'GBP', 'DD/MM/YYYY', '{"decimal": ".", "thousands": ","}'),
('EU', 'European Union', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'EUR', 'DD/MM/YYYY', '{"decimal": ",", "thousands": "."}'),
('CA', 'Canada', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'CAD', 'DD/MM/YYYY', '{"decimal": ".", "thousands": ","}'),
('AU', 'Australia', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'AUD', 'DD/MM/YYYY', '{"decimal": ".", "thousands": ","}'),
('JP', 'Japan', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'JPY', 'YYYY/MM/DD', '{"decimal": ".", "thousands": ","}'),
('GLOBAL', 'Global Default', 'kg', 'cm', 'km', 'celsius', 'meters', 'kmh', 'hpa', 'USD', 'DD/MM/YYYY', '{"decimal": ".", "thousands": ","}')
ON CONFLICT (region_code) DO UPDATE SET
  region_name = EXCLUDED.region_name,
  weight_unit = EXCLUDED.weight_unit,
  length_unit = EXCLUDED.length_unit,
  distance_unit = EXCLUDED.distance_unit,
  temperature_unit = EXCLUDED.temperature_unit,
  depth_unit = EXCLUDED.depth_unit,
  speed_unit = EXCLUDED.speed_unit,
  pressure_unit = EXCLUDED.pressure_unit,
  currency = EXCLUDED.currency,
  date_format = EXCLUDED.date_format,
  number_format = EXCLUDED.number_format;

-- =====================================================
-- 10. CONVERSION RULES
-- =====================================================

-- Auto-switch rules for better UX
INSERT INTO unit_conversion_rules (category_id, from_unit, to_unit, threshold_value, rule_type, context, priority) VALUES
-- Weight conversions
('weight', 'g', 'kg', 1000, 'auto_switch', 'display', 1),
('weight', 'oz', 'lbs', 16, 'auto_switch', 'display', 1),
('weight', 'kg', 'g', 0.1, 'suggestion', 'input', 2),
('weight', 'lbs', 'oz', 0.25, 'suggestion', 'input', 2),

-- Length conversions
('length', 'cm', 'm', 100, 'auto_switch', 'display', 1),
('length', 'inch', 'ft', 12, 'auto_switch', 'display', 1),
('length', 'm', 'cm', 1, 'suggestion', 'input', 2),
('length', 'ft', 'inch', 1, 'suggestion', 'input', 2),

-- Distance conversions
('distance', 'm_dist', 'km', 1000, 'auto_switch', 'display', 1),
('distance', 'km', 'm_dist', 1, 'suggestion', 'input', 2)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 11. VALIDATION RULES
-- =====================================================

-- Context-specific validation rules
INSERT INTO unit_validation_rules (unit_id, min_value, max_value, precision_digits, context, error_message, warning_message) VALUES
-- Catch weight validation
('kg', 0.01, 500, 2, 'catch_weight', 'Balık ağırlığı 0.01kg - 500kg arasında olmalıdır', 'Çok büyük bir balık! Emin misiniz?'),
('lbs', 0.02, 1100, 2, 'catch_weight', 'Fish weight must be between 0.02lbs - 1100lbs', 'Very large fish! Are you sure?'),
('g', 10, 500000, 0, 'catch_weight', 'Balık ağırlığı 10g - 500kg arasında olmalıdır', 'Çok büyük bir balık! Emin misiniz?'),

-- Fish length validation
('cm', 1, 500, 1, 'fish_length', 'Balık boyu 1cm - 500cm arasında olmalıdır', 'Çok büyük bir balık! Emin misiniz?'),
('inch', 0.4, 197, 1, 'fish_length', 'Fish length must be between 0.4in - 197in', 'Very large fish! Are you sure?'),
('m', 0.01, 5, 2, 'fish_length', 'Balık boyu 1cm - 5m arasında olmalıdır', 'Çok büyük bir balık! Emin misiniz?'),

-- Water temperature validation
('celsius', -2, 45, 1, 'water_temperature', 'Su sıcaklığı -2°C - 45°C arasında olmalıdır', 'Olağandışı su sıcaklığı'),
('fahrenheit', 28, 113, 1, 'water_temperature', 'Water temperature must be between 28°F - 113°F', 'Unusual water temperature'),

-- Depth validation
('meters', 0.1, 11000, 1, 'fishing_depth', 'Derinlik 0.1m - 11000m arasında olmalıdır', 'Çok derin! Emin misiniz?'),
('feet', 0.3, 36000, 1, 'fishing_depth', 'Depth must be between 0.3ft - 36000ft', 'Very deep! Are you sure?'),
('fathoms', 0.05, 6000, 1, 'fishing_depth', 'Derinlik 0.05fm - 6000fm arasında olmalıdır', 'Çok derin! Emin misiniz?')
ON CONFLICT DO NOTHING;

-- =====================================================
-- 12. PERFORMANCE OPTIMIZATIONS
-- =====================================================

-- Pre-calculate common conversions for cache
INSERT INTO unit_conversion_cache (from_unit, to_unit, from_value, to_value, hit_count) VALUES
-- Common weight conversions
('kg', 'lbs', 1, 2.20462, 100),
('kg', 'lbs', 2, 4.40924, 100),
('kg', 'lbs', 5, 11.0231, 100),
('lbs', 'kg', 1, 0.453592, 100),
('lbs', 'kg', 5, 2.26796, 100),
('lbs', 'kg', 10, 4.53592, 100),

-- Common length conversions
('cm', 'inch', 10, 3.93701, 100),
('cm', 'inch', 20, 7.87402, 100),
('cm', 'inch', 50, 19.685, 100),
('inch', 'cm', 1, 2.54, 100),
('inch', 'cm', 12, 30.48, 100),
('inch', 'cm', 24, 60.96, 100),

-- Common temperature conversions
('celsius', 'fahrenheit', 0, 32, 100),
('celsius', 'fahrenheit', 10, 50, 100),
('celsius', 'fahrenheit', 20, 68, 100),
('fahrenheit', 'celsius', 32, 0, 100),
('fahrenheit', 'celsius', 68, 20, 100),
('fahrenheit', 'celsius', 86, 30, 100)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 13. SYSTEM SETTINGS
-- =====================================================

-- Insert unit system settings into system_settings table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'system_settings') THEN
    INSERT INTO system_settings (key, value, description, category) VALUES
    ('unit_system.cache_enabled', 'true', 'Enable unit conversion caching', 'units'),
    ('unit_system.cache_max_size', '10000', 'Maximum number of cached conversions', 'units'),
    ('unit_system.cache_ttl_hours', '24', 'Cache time-to-live in hours', 'units'),
    ('unit_system.auto_detect_region', 'true', 'Auto-detect user region for unit defaults', 'units'),
    ('unit_system.analytics_enabled', 'true', 'Enable unit usage analytics', 'units'),
    ('unit_system.validation_strict', 'false', 'Enable strict validation for unit values', 'units')
    ON CONFLICT (key) DO UPDATE SET
      value = EXCLUDED.value,
      description = EXCLUDED.description,
      updated_at = NOW();
  END IF;
END $$; 