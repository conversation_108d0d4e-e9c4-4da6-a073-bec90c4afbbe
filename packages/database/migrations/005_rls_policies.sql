-- =====================================================
-- MIGRATION 005: ROW LEVEL SECURITY POLICIES
-- Created: 2024-01-20
-- Description: Security policies for all tables
-- =====================================================

-- =====================================================
-- ENABLE RLS ON ALL TABLES
-- =====================================================

ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE fish_species ENABLE ROW LEVEL SECURITY;
ALTER TABLE spots ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment_brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment_review_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment_wishlist ENABLE ROW LEVEL SECURITY;
ALTER TABLE likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

ALTER TABLE admin_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_warnings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_suspensions ENABLE ROW LEVEL SECURITY;
ALTER TABLE blocked_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_moderation_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE maintenance_schedules ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- USER POLICIES
-- =====================================================

-- Users can view public profiles and their own profile
CREATE POLICY "Users viewable by everyone" ON users FOR SELECT USING (
  CASE 
    WHEN auth.uid() = id THEN true -- Own profile
    WHEN banned_until IS NOT NULL AND banned_until > NOW() THEN false -- Banned users not visible
    ELSE true -- Public profiles
  END
);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Admins can manage all users
CREATE POLICY "Admins can manage users" ON users FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- =====================================================
-- CONTENT POLICIES (POSTS, COMMENTS, LIKES)
-- =====================================================

-- Posts viewable by everyone (except hidden/deleted)
CREATE POLICY "Posts viewable by everyone" ON posts FOR SELECT USING (
  status = 'active' AND 
  user_id NOT IN (
    SELECT blocked_id FROM blocked_users WHERE blocker_id = auth.uid()
  )
);

-- Users can create posts
CREATE POLICY "Users can create posts" ON posts FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update own posts
CREATE POLICY "Users can update own posts" ON posts FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete own posts
CREATE POLICY "Users can delete own posts" ON posts FOR DELETE USING (auth.uid() = user_id);

-- Admins can manage all posts
CREATE POLICY "Admins can manage all posts" ON posts FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- Comments policies (similar to posts)
CREATE POLICY "Comments viewable by everyone" ON comments FOR SELECT USING (
  status = 'active' AND
  user_id NOT IN (
    SELECT blocked_id FROM blocked_users WHERE blocker_id = auth.uid()
  )
);

CREATE POLICY "Users can create comments" ON comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own comments" ON comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own comments" ON comments FOR DELETE USING (auth.uid() = user_id);

-- Likes policies
CREATE POLICY "Likes viewable by everyone" ON likes FOR SELECT USING (true);
CREATE POLICY "Users can manage own likes" ON likes FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- FISH SPECIES & SPOTS POLICIES
-- =====================================================

-- Fish species viewable by everyone (active only)
CREATE POLICY "Fish species viewable by everyone" ON fish_species FOR SELECT USING (status = 'active');

-- Admins can manage fish species
CREATE POLICY "Admins can manage fish species" ON fish_species FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- Approved spots viewable by everyone
CREATE POLICY "Approved spots viewable by everyone" ON spots FOR SELECT USING (status = 'approved');

-- Users can create spots
CREATE POLICY "Users can create spots" ON spots FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update own spots (if not approved yet)
CREATE POLICY "Users can update own spots" ON spots FOR UPDATE USING (
  auth.uid() = user_id AND status IN ('pending', 'rejected')
);

-- Admins can manage all spots
CREATE POLICY "Admins can manage all spots" ON spots FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- =====================================================
-- EQUIPMENT POLICIES
-- =====================================================

-- Equipment viewable by everyone (active only)
CREATE POLICY "Equipment viewable by everyone" ON equipment FOR SELECT USING (status = 'active');

-- Equipment categories and brands viewable by everyone
CREATE POLICY "Equipment categories viewable by everyone" ON equipment_categories FOR SELECT USING (is_active = true);
CREATE POLICY "Equipment brands viewable by everyone" ON equipment_brands FOR SELECT USING (is_active = true);

-- Admins can manage equipment
CREATE POLICY "Admins can manage equipment" ON equipment FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- User equipment policies
CREATE POLICY "Users can view own equipment" ON user_equipment FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own equipment" ON user_equipment FOR ALL USING (auth.uid() = user_id);

-- Equipment reviews policies
CREATE POLICY "Equipment reviews viewable by everyone" ON equipment_reviews FOR SELECT USING (status = 'active');
CREATE POLICY "Users can create equipment reviews" ON equipment_reviews FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own equipment reviews" ON equipment_reviews FOR UPDATE USING (auth.uid() = user_id);

-- Equipment review votes policies
CREATE POLICY "Users can manage own review votes" ON equipment_review_votes FOR ALL USING (auth.uid() = user_id);

-- Equipment wishlist policies
CREATE POLICY "Users can view own wishlist" ON equipment_wishlist FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own wishlist" ON equipment_wishlist FOR ALL USING (auth.uid() = user_id);

-- =====================================================
-- SOCIAL POLICIES (FOLLOWS, MESSAGES)
-- =====================================================

-- Follows viewable by everyone
CREATE POLICY "Follows viewable by everyone" ON follows FOR SELECT USING (true);
CREATE POLICY "Users can manage own follows" ON follows FOR ALL USING (auth.uid() = follower_id);

-- Messages policies
CREATE POLICY "Users can view own messages" ON messages FOR SELECT USING (
  auth.uid() = sender_id OR auth.uid() = receiver_id
);
CREATE POLICY "Users can send messages" ON messages FOR INSERT WITH CHECK (
  auth.uid() = sender_id AND
  receiver_id NOT IN (
    SELECT blocked_id FROM blocked_users WHERE blocker_id = auth.uid()
  ) AND
  sender_id NOT IN (
    SELECT blocked_id FROM blocked_users WHERE blocker_id = receiver_id
  )
);
CREATE POLICY "Users can update own messages" ON messages FOR UPDATE USING (
  auth.uid() = sender_id OR auth.uid() = receiver_id
);

-- Message threads policies
CREATE POLICY "Users can view own message threads" ON message_threads FOR SELECT USING (
  auth.uid() = participant_1 OR auth.uid() = participant_2
);
CREATE POLICY "Users can update own message threads" ON message_threads FOR UPDATE USING (
  auth.uid() = participant_1 OR auth.uid() = participant_2
);

-- =====================================================
-- NOTIFICATION POLICIES
-- =====================================================

-- Users can view own notifications
CREATE POLICY "Users can view own notifications" ON notifications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can update own notifications" ON notifications FOR UPDATE USING (auth.uid() = user_id);





-- =====================================================
-- ADMIN & MODERATION POLICIES
-- =====================================================

-- Admin logs viewable by admins only
CREATE POLICY "Admin logs viewable by admins only" ON admin_logs FOR SELECT USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- Reports policies
CREATE POLICY "Anyone can create reports" ON reports FOR INSERT WITH CHECK (auth.uid() = reporter_id);
CREATE POLICY "Users can view own reports" ON reports FOR SELECT USING (auth.uid() = reporter_id);
CREATE POLICY "Admins can manage reports" ON reports FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- System settings viewable by super admins only
CREATE POLICY "Super admins can manage system settings" ON system_settings FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role = 'super_admin')
);

-- User moderation policies (admin only)
CREATE POLICY "Admins can manage user warnings" ON user_warnings FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

CREATE POLICY "Admins can manage user suspensions" ON user_suspensions FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- Blocked users policies
CREATE POLICY "Users can view own blocked users" ON blocked_users FOR SELECT USING (auth.uid() = blocker_id);
CREATE POLICY "Users can manage own blocked users" ON blocked_users FOR ALL USING (auth.uid() = blocker_id);

-- Content moderation queue (admin only)
CREATE POLICY "Admins can manage content moderation queue" ON content_moderation_queue FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- Feature flags (admin only)
CREATE POLICY "Admins can manage feature flags" ON feature_flags FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- API rate limits (admin only)
CREATE POLICY "Admins can view API rate limits" ON api_rate_limits FOR SELECT USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);

-- Maintenance schedules (admin only)
CREATE POLICY "Admins can manage maintenance schedules" ON maintenance_schedules FOR ALL USING (
  EXISTS (SELECT 1 FROM users WHERE users.id = auth.uid() AND users.role IN ('admin', 'super_admin'))
);
