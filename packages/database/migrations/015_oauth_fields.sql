-- =====================================================
-- MIGRATION 015: ADD OAUTH FIELDS
-- Created: 2024-07-05
-- Description: Add OAuth-specific fields to users table
-- =====================================================

-- Add Google ID field if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'google_id'
  ) THEN
    ALTER TABLE users ADD COLUMN google_id TEXT;
    RAISE NOTICE 'Added google_id column';
  END IF;
END $$;

-- Add Facebook ID field if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'facebook_id'
  ) THEN
    ALTER TABLE users ADD COLUMN facebook_id TEXT;
    RAISE NOTICE 'Added facebook_id column';
  END IF;
END $$;

-- Add Apple ID field if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'apple_id'
  ) THEN
    ALTER TABLE users ADD COLUMN apple_id TEXT;
    RAISE NOTICE 'Added apple_id column';
  END IF;
END $$;

-- Ensure provider field exists and has default value
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'users'
    AND column_name = 'provider'
  ) THEN
    ALTER TABLE users ADD COLUMN provider TEXT DEFAULT 'email';
    RAISE NOTICE 'Added provider column';
  ELSE
    -- Update provider column default if it exists
    ALTER TABLE users ALTER COLUMN provider SET DEFAULT 'email';
    RAISE NOTICE 'Updated provider column default value';
  END IF;
END $$;

-- Create index on OAuth ID fields for faster lookups
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE tablename = 'users'
    AND indexname = 'idx_users_google_id'
  ) THEN
    CREATE INDEX idx_users_google_id ON users(google_id);
    RAISE NOTICE 'Created index on google_id';
  END IF;
  
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE tablename = 'users'
    AND indexname = 'idx_users_facebook_id'
  ) THEN
    CREATE INDEX idx_users_facebook_id ON users(facebook_id);
    RAISE NOTICE 'Created index on facebook_id';
  END IF;
  
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE tablename = 'users'
    AND indexname = 'idx_users_apple_id'
  ) THEN
    CREATE INDEX idx_users_apple_id ON users(apple_id);
    RAISE NOTICE 'Created index on apple_id';
  END IF;
END $$;

-- Create index on provider field
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1
    FROM pg_indexes
    WHERE tablename = 'users'
    AND indexname = 'idx_users_provider'
  ) THEN
    CREATE INDEX idx_users_provider ON users(provider);
    RAISE NOTICE 'Created index on provider';
  END IF;
END $$;

-- Update existing users with null provider
UPDATE users
SET provider = 'email'
WHERE provider IS NULL;

-- Refresh schema cache
DO $$
BEGIN
  -- Force schema refresh
  ALTER TABLE users ADD COLUMN _tmp INTEGER;
  ALTER TABLE users DROP COLUMN _tmp;
  RAISE NOTICE 'Schema cache refreshed';
END $$; 