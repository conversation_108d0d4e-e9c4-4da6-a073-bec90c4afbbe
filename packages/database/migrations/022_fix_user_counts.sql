-- Fix user counts migration
-- Migration name: 022_fix_user_counts

-- Ensure user count fields exist
DO $$
BEGIN
  -- Add followers_count if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'followers_count') THEN
    ALTER TABLE users ADD COLUMN followers_count INTEGER DEFAULT 0;
  END IF;
  
  -- Add following_count if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'following_count') THEN
    ALTER TABLE users ADD COLUMN following_count INTEGER DEFAULT 0;
  END IF;
  
  -- Add catches_count if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'catches_count') THEN
    ALTER TABLE users ADD COLUMN catches_count INTEGER DEFAULT 0;
  END IF;
END$$;

-- Recalculate all user statistics
UPDATE users SET 
  catches_count = (SELECT COUNT(*) FROM posts WHERE user_id = users.id),
  followers_count = (SELECT COUNT(*) FROM follows WHERE following_id = users.id),
  following_count = (SELECT COUNT(*) FROM follows WHERE follower_id = users.id);

-- Ensure triggers exist and are working
DO $$
BEGIN
  -- Check if the trigger function exists
  IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_user_statistics') THEN
    -- Create the function if it doesn't exist
    CREATE OR REPLACE FUNCTION update_user_statistics()
    RETURNS TRIGGER AS $$
    BEGIN
      IF TG_TABLE_NAME = 'posts' THEN
        -- Update catches_count
        IF TG_OP = 'INSERT' THEN
          UPDATE users 
          SET catches_count = catches_count + 1,
              updated_at = NOW()
          WHERE id = NEW.user_id;
        ELSIF TG_OP = 'DELETE' THEN
          UPDATE users 
          SET catches_count = GREATEST(catches_count - 1, 0),
              updated_at = NOW()
          WHERE id = OLD.user_id;
        END IF;
      ELSIF TG_TABLE_NAME = 'follows' THEN
        -- Update followers/following counts
        IF TG_OP = 'INSERT' THEN
          -- Increase follower count
          UPDATE users 
          SET followers_count = followers_count + 1,
              updated_at = NOW()
          WHERE id = NEW.following_id;
          -- Increase following count
          UPDATE users 
          SET following_count = following_count + 1,
              updated_at = NOW()
          WHERE id = NEW.follower_id;
        ELSIF TG_OP = 'DELETE' THEN
          -- Decrease follower count
          UPDATE users 
          SET followers_count = GREATEST(followers_count - 1, 0),
              updated_at = NOW()
          WHERE id = OLD.following_id;
          -- Decrease following count
          UPDATE users 
          SET following_count = GREATEST(following_count - 1, 0),
              updated_at = NOW()
          WHERE id = OLD.follower_id;
        END IF;
      END IF;
      
      RETURN COALESCE(NEW, OLD);
    END;
    $$ LANGUAGE plpgsql;
  END IF;
END$$;

-- Drop and recreate triggers to ensure they're working
DROP TRIGGER IF EXISTS trigger_update_user_catches ON posts;
CREATE TRIGGER trigger_update_user_catches
  AFTER INSERT OR DELETE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION update_user_statistics();

DROP TRIGGER IF EXISTS trigger_update_user_follows ON follows;
CREATE TRIGGER trigger_update_user_follows
  AFTER INSERT OR DELETE ON follows
  FOR EACH ROW
  EXECUTE FUNCTION update_user_statistics();

-- Log the current state
DO $$
DECLARE
  total_users INTEGER;
  users_with_followers INTEGER;
  users_with_following INTEGER;
  users_with_catches INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_users FROM users;
  SELECT COUNT(*) INTO users_with_followers FROM users WHERE followers_count > 0;
  SELECT COUNT(*) INTO users_with_following FROM users WHERE following_count > 0;
  SELECT COUNT(*) INTO users_with_catches FROM users WHERE catches_count > 0;
  
  RAISE NOTICE 'Migration completed: % total users, % with followers, % following, % with catches', 
    total_users, users_with_followers, users_with_following, users_with_catches;
END$$; 