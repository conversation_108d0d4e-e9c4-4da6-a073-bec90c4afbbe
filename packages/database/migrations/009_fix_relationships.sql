-- =====================================================
-- MIGRATION 009: FIX RELATIONSHIPS
-- Created: 2024-01-21
-- Description: Fix missing relationships and improve data flow
-- =====================================================

-- =====================================================
-- 1. POSTS ↔ SPOTS RELATIONSHIP
-- =====================================================

-- Add spot_id to posts table for direct post-spot relationship
ALTER TABLE posts ADD COLUMN IF NOT EXISTS spot_id BIGINT REFERENCES spots(id) ON DELETE SET NULL;

-- Create index for spot_id
CREATE INDEX IF NOT EXISTS idx_posts_spot_id ON posts(spot_id);

-- Update spots table to track catch counts properly
-- This will be updated via triggers

-- =====================================================
-- 2. SPOT REVIEWS SYSTEM
-- =====================================================

-- Create spot_reviews table for spot-specific reviews
CREATE TABLE IF NOT EXISTS spot_reviews (
  id BIGSERIAL PRIMARY KEY,
  spot_id BIGINT NOT NULL REFERENCES spots(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  title TEXT,
  content TEXT,
  
  -- Review categories
  accessibility_rating INTEGER CHECK (accessibility_rating >= 1 AND accessibility_rating <= 5),
  facilities_rating INTEGER CHECK (facilities_rating >= 1 AND facilities_rating <= 5),
  fish_diversity_rating INTEGER CHECK (fish_diversity_rating >= 1 AND fish_diversity_rating <= 5),
  
  -- Helpful votes
  helpful_count INTEGER DEFAULT 0,
  
  -- Moderation
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'hidden', 'deleted', 'pending_review')),
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Prevent duplicate reviews from same user for same spot
  UNIQUE(spot_id, user_id)
);

-- Create indexes for spot_reviews
CREATE INDEX IF NOT EXISTS idx_spot_reviews_spot_id ON spot_reviews(spot_id);
CREATE INDEX IF NOT EXISTS idx_spot_reviews_user_id ON spot_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_spot_reviews_rating ON spot_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_spot_reviews_created_at ON spot_reviews(created_at DESC);

-- =====================================================
-- 3. SPOT FAVORITES SYSTEM
-- =====================================================

-- Create spot_favorites table
CREATE TABLE IF NOT EXISTS spot_favorites (
  id BIGSERIAL PRIMARY KEY,
  spot_id BIGINT NOT NULL REFERENCES spots(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  -- Prevent duplicate favorites
  UNIQUE(spot_id, user_id)
);

-- Create indexes for spot_favorites
CREATE INDEX IF NOT EXISTS idx_spot_favorites_spot_id ON spot_favorites(spot_id);
CREATE INDEX IF NOT EXISTS idx_spot_favorites_user_id ON spot_favorites(user_id);

-- =====================================================
-- 4. EQUIPMENT REVIEWS SYSTEM
-- =====================================================

-- Create equipment_reviews table
CREATE TABLE IF NOT EXISTS equipment_reviews (
  id BIGSERIAL PRIMARY KEY,
  equipment_name TEXT NOT NULL,
  brand TEXT,
  model TEXT,
  category TEXT,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Rating details
  overall_rating INTEGER NOT NULL CHECK (overall_rating >= 1 AND overall_rating <= 5),
  durability_rating INTEGER CHECK (durability_rating >= 1 AND durability_rating <= 5),
  value_rating INTEGER CHECK (value_rating >= 1 AND value_rating <= 5),
  performance_rating INTEGER CHECK (performance_rating >= 1 AND performance_rating <= 5),
  
  -- Review content
  title TEXT,
  content TEXT,
  pros JSONB DEFAULT '[]',
  cons JSONB DEFAULT '[]',
  
  -- Usage context
  usage_duration TEXT, -- '1 ay', '6 ay', '1 yıl', etc.
  fishing_style JSONB DEFAULT '[]', -- ['Shore fishing', 'Boat fishing', etc.]
  target_species JSONB DEFAULT '[]',
  
  -- Helpful votes
  helpful_count INTEGER DEFAULT 0,
  
  -- Images
  images JSONB DEFAULT '[]',
  
  -- Moderation
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'hidden', 'deleted', 'pending_review')),
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for equipment_reviews
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_user_id ON equipment_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_equipment_name ON equipment_reviews(equipment_name);
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_brand ON equipment_reviews(brand);
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_category ON equipment_reviews(category);
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_overall_rating ON equipment_reviews(overall_rating);

-- =====================================================
-- 5. TRIP PLANNING SYSTEM
-- =====================================================

-- Create fishing_trips table
CREATE TABLE IF NOT EXISTS fishing_trips (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  
  -- Trip details
  start_date TIMESTAMPTZ NOT NULL,
  end_date TIMESTAMPTZ,
  spot_id BIGINT REFERENCES spots(id) ON DELETE SET NULL,
  
  -- Trip settings
  is_public BOOLEAN DEFAULT true,
  max_participants INTEGER,
  current_participants INTEGER DEFAULT 1,
  
  -- Planned equipment and targets
  planned_equipment JSONB DEFAULT '[]',
  target_species JSONB DEFAULT '[]',
  
  -- Trip status
  status TEXT DEFAULT 'planned' CHECK (status IN ('planned', 'active', 'completed', 'cancelled')),
  
  -- Weather and conditions
  weather_conditions JSONB,
  
  -- Results
  success_rating INTEGER CHECK (success_rating >= 1 AND success_rating <= 5),
  total_catches INTEGER DEFAULT 0,
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for fishing_trips
CREATE INDEX IF NOT EXISTS idx_fishing_trips_user_id ON fishing_trips(user_id);
CREATE INDEX IF NOT EXISTS idx_fishing_trips_spot_id ON fishing_trips(spot_id);
CREATE INDEX IF NOT EXISTS idx_fishing_trips_start_date ON fishing_trips(start_date);
CREATE INDEX IF NOT EXISTS idx_fishing_trips_status ON fishing_trips(status);

-- =====================================================
-- 6. TRIP PARTICIPANTS
-- =====================================================

-- Create trip_participants table
CREATE TABLE IF NOT EXISTS trip_participants (
  id BIGSERIAL PRIMARY KEY,
  trip_id BIGINT NOT NULL REFERENCES fishing_trips(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Participation details
  status TEXT DEFAULT 'invited' CHECK (status IN ('invited', 'accepted', 'declined', 'removed')),
  role TEXT DEFAULT 'participant' CHECK (role IN ('organizer', 'participant')),
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Prevent duplicate participation
  UNIQUE(trip_id, user_id)
);

-- Create indexes for trip_participants
CREATE INDEX IF NOT EXISTS idx_trip_participants_trip_id ON trip_participants(trip_id);
CREATE INDEX IF NOT EXISTS idx_trip_participants_user_id ON trip_participants(user_id);

-- =====================================================
-- 7. POSTS ↔ TRIPS RELATIONSHIP
-- =====================================================

-- Add trip_id to posts table
ALTER TABLE posts ADD COLUMN IF NOT EXISTS trip_id BIGINT REFERENCES fishing_trips(id) ON DELETE SET NULL;

-- Create index for trip_id
CREATE INDEX IF NOT EXISTS idx_posts_trip_id ON posts(trip_id);

-- =====================================================
-- 8. SPOT STATISTICS TRIGGERS
-- =====================================================

-- Function to update spot statistics
CREATE OR REPLACE FUNCTION update_spot_statistics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update catches_count when post is added/removed
  IF TG_OP = 'INSERT' AND NEW.spot_id IS NOT NULL THEN
    UPDATE spots 
    SET catches_count = catches_count + 1,
        updated_at = NOW()
    WHERE id = NEW.spot_id;
  ELSIF TG_OP = 'DELETE' AND OLD.spot_id IS NOT NULL THEN
    UPDATE spots 
    SET catches_count = GREATEST(catches_count - 1, 0),
        updated_at = NOW()
    WHERE id = OLD.spot_id;
  ELSIF TG_OP = 'UPDATE' THEN
    -- Handle spot_id changes
    IF OLD.spot_id IS DISTINCT FROM NEW.spot_id THEN
      -- Decrease from old spot
      IF OLD.spot_id IS NOT NULL THEN
        UPDATE spots 
        SET catches_count = GREATEST(catches_count - 1, 0),
            updated_at = NOW()
        WHERE id = OLD.spot_id;
      END IF;
      -- Increase to new spot
      IF NEW.spot_id IS NOT NULL THEN
        UPDATE spots 
        SET catches_count = catches_count + 1,
            updated_at = NOW()
        WHERE id = NEW.spot_id;
      END IF;
    END IF;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for spot statistics
DROP TRIGGER IF EXISTS trigger_update_spot_statistics ON posts;
CREATE TRIGGER trigger_update_spot_statistics
  AFTER INSERT OR UPDATE OR DELETE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION update_spot_statistics();

-- =====================================================
-- 9. SPOT RATING TRIGGERS
-- =====================================================

-- Function to update spot ratings
CREATE OR REPLACE FUNCTION update_spot_rating()
RETURNS TRIGGER AS $$
BEGIN
  -- Recalculate spot rating and rating_count
  UPDATE spots 
  SET 
    rating = COALESCE((
      SELECT ROUND(AVG(rating::numeric), 1)
      FROM spot_reviews 
      WHERE spot_id = COALESCE(NEW.spot_id, OLD.spot_id)
      AND status = 'active'
    ), 0),
    rating_count = COALESCE((
      SELECT COUNT(*)
      FROM spot_reviews 
      WHERE spot_id = COALESCE(NEW.spot_id, OLD.spot_id)
      AND status = 'active'
    ), 0),
    updated_at = NOW()
  WHERE id = COALESCE(NEW.spot_id, OLD.spot_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for spot rating updates
DROP TRIGGER IF EXISTS trigger_update_spot_rating ON spot_reviews;
CREATE TRIGGER trigger_update_spot_rating
  AFTER INSERT OR UPDATE OR DELETE ON spot_reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_spot_rating();

-- =====================================================
-- 10. USER STATISTICS TRIGGERS
-- =====================================================

-- Function to update user statistics
CREATE OR REPLACE FUNCTION update_user_statistics()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_TABLE_NAME = 'posts' THEN
    -- Update catches_count
    IF TG_OP = 'INSERT' THEN
      UPDATE users 
      SET catches_count = catches_count + 1,
          updated_at = NOW()
      WHERE id = NEW.user_id;
    ELSIF TG_OP = 'DELETE' THEN
      UPDATE users 
      SET catches_count = GREATEST(catches_count - 1, 0),
          updated_at = NOW()
      WHERE id = OLD.user_id;
    END IF;
  ELSIF TG_TABLE_NAME = 'spots' THEN
    -- Update spots_count
    IF TG_OP = 'INSERT' THEN
      UPDATE users 
      SET spots_count = spots_count + 1,
          updated_at = NOW()
      WHERE id = NEW.user_id;
    ELSIF TG_OP = 'DELETE' THEN
      UPDATE users 
      SET spots_count = GREATEST(spots_count - 1, 0),
          updated_at = NOW()
      WHERE id = OLD.user_id;
    END IF;
  ELSIF TG_TABLE_NAME = 'follows' THEN
    -- Update followers/following counts
    IF TG_OP = 'INSERT' THEN
      -- Increase follower count
      UPDATE users 
      SET followers_count = followers_count + 1,
          updated_at = NOW()
      WHERE id = NEW.following_id;
      -- Increase following count
      UPDATE users 
      SET following_count = following_count + 1,
          updated_at = NOW()
      WHERE id = NEW.follower_id;
    ELSIF TG_OP = 'DELETE' THEN
      -- Decrease follower count
      UPDATE users 
      SET followers_count = GREATEST(followers_count - 1, 0),
          updated_at = NOW()
      WHERE id = OLD.following_id;
      -- Decrease following count
      UPDATE users 
      SET following_count = GREATEST(following_count - 1, 0),
          updated_at = NOW()
      WHERE id = OLD.follower_id;
    END IF;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for user statistics
DROP TRIGGER IF EXISTS trigger_update_user_catches ON posts;
CREATE TRIGGER trigger_update_user_catches
  AFTER INSERT OR DELETE ON posts
  FOR EACH ROW
  EXECUTE FUNCTION update_user_statistics();

DROP TRIGGER IF EXISTS trigger_update_user_spots ON spots;
CREATE TRIGGER trigger_update_user_spots
  AFTER INSERT OR DELETE ON spots
  FOR EACH ROW
  EXECUTE FUNCTION update_user_statistics();

DROP TRIGGER IF EXISTS trigger_update_user_follows ON follows;
CREATE TRIGGER trigger_update_user_follows
  AFTER INSERT OR DELETE ON follows
  FOR EACH ROW
  EXECUTE FUNCTION update_user_statistics();

-- =====================================================
-- 11. POST STATISTICS TRIGGERS
-- =====================================================

-- Function to update post statistics
CREATE OR REPLACE FUNCTION update_post_statistics()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_TABLE_NAME = 'likes' THEN
    -- Update likes_count
    UPDATE posts 
    SET likes_count = (
      SELECT COUNT(*) FROM likes WHERE post_id = COALESCE(NEW.post_id, OLD.post_id)
    ),
    updated_at = NOW()
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  ELSIF TG_TABLE_NAME = 'comments' THEN
    -- Update comments_count
    UPDATE posts 
    SET comments_count = (
      SELECT COUNT(*) FROM comments WHERE post_id = COALESCE(NEW.post_id, OLD.post_id) AND status = 'active'
    ),
    updated_at = NOW()
    WHERE id = COALESCE(NEW.post_id, OLD.post_id);
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for post statistics
DROP TRIGGER IF EXISTS trigger_update_post_likes ON likes;
CREATE TRIGGER trigger_update_post_likes
  AFTER INSERT OR DELETE ON likes
  FOR EACH ROW
  EXECUTE FUNCTION update_post_statistics();

DROP TRIGGER IF EXISTS trigger_update_post_comments ON comments;
CREATE TRIGGER trigger_update_post_comments
  AFTER INSERT OR UPDATE OR DELETE ON comments
  FOR EACH ROW
  EXECUTE FUNCTION update_post_statistics();

-- =====================================================
-- 12. INITIAL DATA FIXES
-- =====================================================

-- Recalculate all existing statistics
UPDATE users SET 
  catches_count = (SELECT COUNT(*) FROM posts WHERE user_id = users.id),
  spots_count = (SELECT COUNT(*) FROM spots WHERE user_id = users.id),
  followers_count = (SELECT COUNT(*) FROM follows WHERE following_id = users.id),
  following_count = (SELECT COUNT(*) FROM follows WHERE follower_id = users.id);

UPDATE posts SET 
  likes_count = (SELECT COUNT(*) FROM likes WHERE post_id = posts.id),
  comments_count = (SELECT COUNT(*) FROM comments WHERE post_id = posts.id AND status = 'active');

UPDATE spots SET 
  catches_count = (SELECT COUNT(*) FROM posts WHERE spot_id = spots.id); 