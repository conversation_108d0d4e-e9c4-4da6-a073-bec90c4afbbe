-- =====================================================
-- MIGRATION 013: BYPASS AUTH CONSTRAINT
-- Created: 2025-06-17
-- Description: Create function to bypass auth constraint for testing
-- =====================================================

-- Create a function to bypass the foreign key constraint for testing
CREATE OR REPLACE FUNCTION admin_create_user_bypass_auth(user_data jsonb)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  test_user_id uuid;
BEGIN
  -- Extract the user ID from the input data
  test_user_id := (user_data->>'id')::uuid;
  
  -- First create a record in auth.users to satisfy the foreign key constraint
  INSERT INTO auth.users (
    id, 
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    last_sign_in_at
  ) VALUES (
    test_user_id,
    user_data->>'email',
    '$2a$10$XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX', -- dummy password hash
    NOW(),
    NOW(),
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO NOTHING;
  
  -- Then insert into the public users table
  INSERT INTO public.users (
    id, 
    email, 
    username, 
    full_name, 
    avatar_url, 
    provider, 
    is_pro, 
    catches_count, 
    spots_count, 
    created_at, 
    updated_at
  ) VALUES (
    test_user_id,
    user_data->>'email',
    user_data->>'username',
    user_data->>'full_name',
    user_data->>'avatar_url',
    user_data->>'provider',
    (user_data->>'is_pro')::boolean,
    (user_data->>'catches_count')::integer,
    (user_data->>'spots_count')::integer,
    (user_data->>'created_at')::timestamptz,
    (user_data->>'updated_at')::timestamptz
  )
  ON CONFLICT (id) DO UPDATE SET
    email = user_data->>'email',
    username = user_data->>'username',
    full_name = user_data->>'full_name',
    avatar_url = user_data->>'avatar_url',
    provider = user_data->>'provider',
    is_pro = (user_data->>'is_pro')::boolean,
    catches_count = (user_data->>'catches_count')::integer,
    spots_count = (user_data->>'spots_count')::integer,
    updated_at = (user_data->>'updated_at')::timestamptz;
  
  -- Return the created user data
  RETURN user_data;
END;
$$; 