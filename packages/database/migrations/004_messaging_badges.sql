-- =====================================================
-- MIGRATION 004: MESSAGING SYSTEM
-- Date: 2024-01-15
-- Description: Messaging system for user communication
-- =====================================================

-- =====================================================
-- MESSAGING SYSTEM
-- =====================================================

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
  id BIGSERIAL PRIMARY KEY,
  sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
  receiver_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Message content
  content TEXT NOT NULL,
  message_type TEXT DEFAULT 'text', -- 'text', 'image', 'location', 'catch_share'
  
  -- Metadata
  metadata JSONB DEFAULT '{}', -- For additional data like image URLs, location coords, etc.
  
  -- Status
  read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  CHECK (sender_id != receiver_id),
  CHECK (char_length(content) <= 1000)
);

-- Message threads (conversations between two users)
CREATE TABLE IF NOT EXISTS message_threads (
  id BIGSERIAL PRIMARY KEY,
  participant_1 UUID REFERENCES users(id) ON DELETE CASCADE,
  participant_2 UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Thread metadata
  last_message_id BIGINT REFERENCES messages(id),
  last_activity TIMESTAMPTZ DEFAULT NOW(),
  
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Ensure participant_1 < participant_2 for consistency
  UNIQUE(participant_1, participant_2),
  CHECK (participant_1 != participant_2)
);

-- =====================================================
-- INDEXES
-- =====================================================

-- Message indexes
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_receiver_id ON messages(receiver_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_read ON messages(read);
CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(message_type);

-- Message thread indexes
CREATE INDEX IF NOT EXISTS idx_message_threads_participant_1 ON message_threads(participant_1);
CREATE INDEX IF NOT EXISTS idx_message_threads_participant_2 ON message_threads(participant_2);
CREATE INDEX IF NOT EXISTS idx_message_threads_last_activity ON message_threads(last_activity DESC);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Update message thread on new message
CREATE OR REPLACE FUNCTION update_message_thread()
RETURNS TRIGGER AS $$
BEGIN
    -- Update or create message thread
    INSERT INTO message_threads (participant_1, participant_2, last_message_id, last_activity)
    VALUES (
        LEAST(NEW.sender_id, NEW.receiver_id),
        GREATEST(NEW.sender_id, NEW.receiver_id),
        NEW.id,
        NEW.created_at
    )
    ON CONFLICT (participant_1, participant_2)
    DO UPDATE SET
        last_message_id = NEW.id,
        last_activity = NEW.created_at,
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_message_thread_trigger
    AFTER INSERT ON messages
    FOR EACH ROW EXECUTE FUNCTION update_message_thread();

-- =====================================================
-- ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE message_threads ENABLE ROW LEVEL SECURITY;

-- Messages policies
CREATE POLICY "Users can view their own messages" ON messages
    FOR SELECT USING (sender_id = auth.uid() OR receiver_id = auth.uid());

CREATE POLICY "Users can send messages" ON messages
    FOR INSERT WITH CHECK (sender_id = auth.uid());

CREATE POLICY "Users can update their sent messages" ON messages
    FOR UPDATE USING (sender_id = auth.uid());

-- Message threads policies
CREATE POLICY "Users can view their threads" ON message_threads
    FOR SELECT USING (participant_1 = auth.uid() OR participant_2 = auth.uid());

CREATE POLICY "System can manage threads" ON message_threads
    FOR ALL USING (true);
