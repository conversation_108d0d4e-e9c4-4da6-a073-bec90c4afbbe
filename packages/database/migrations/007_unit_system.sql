-- =====================================================
-- MIGRATION 007: UNIT SYSTEM
-- Created: 2024-01-20
-- Description: Database-driven unit conversion and user preferences system
-- =====================================================

-- =====================================================
-- 1. UNIT DEFINITIONS SYSTEM
-- =====================================================

-- Unit categories (weight, length, temperature, etc.)
CREATE TABLE IF NOT EXISTS unit_categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  name_en TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  base_unit TEXT NOT NULL, -- The base unit for this category
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Unit definitions (kg, lbs, cm, inch, etc.)
CREATE TABLE IF NOT EXISTS unit_definitions (
  id TEXT PRIMARY KEY,
  category_id TEXT REFERENCES unit_categories(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  name_en TEXT NOT NULL,
  symbol TEXT NOT NULL,
  
  -- Conversion properties
  is_base_unit BOOLEAN DEFAULT FALSE,
  conversion_factor DECIMAL(20,10), -- Multiplier to convert to base unit
  conversion_formula TEXT, -- For complex conversions like temperature
  reverse_formula TEXT, -- For reverse conversions
  
  -- Display properties
  precision_digits INTEGER DEFAULT 2,
  min_value DECIMAL(20,10),
  max_value DECIMAL(20,10),
  
  -- Regional usage
  regions JSONB DEFAULT '[]', -- ['TR', 'US', 'EU', 'UK']
  popularity INTEGER DEFAULT 0, -- 0-100 popularity score
  default_for_regions JSONB DEFAULT '[]', -- Regions where this is default
  
  -- Metadata
  use_case TEXT, -- 'general', 'marine', 'scientific', 'traditional'
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  UNIQUE(category_id, symbol),
  CHECK (conversion_factor > 0 OR conversion_formula IS NOT NULL)
);

-- =====================================================
-- 2. USER PREFERENCES SYSTEM
-- =====================================================

-- User unit preferences
CREATE TABLE IF NOT EXISTS user_unit_preferences (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  
  -- Unit preferences by category
  weight_unit TEXT REFERENCES unit_definitions(id),
  length_unit TEXT REFERENCES unit_definitions(id),
  distance_unit TEXT REFERENCES unit_definitions(id),
  temperature_unit TEXT REFERENCES unit_definitions(id),
  depth_unit TEXT REFERENCES unit_definitions(id),
  speed_unit TEXT REFERENCES unit_definitions(id),
  pressure_unit TEXT REFERENCES unit_definitions(id),
  
  -- Regional context
  region TEXT DEFAULT 'TR', -- User's region/country
  auto_detect_region BOOLEAN DEFAULT TRUE,
  
  -- Preferences metadata
  last_updated_at TIMESTAMPTZ DEFAULT NOW(),
  updated_by_user BOOLEAN DEFAULT FALSE, -- FALSE if auto-set by region
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- =====================================================
-- 3. CONVERSION RULES & VALIDATION
-- =====================================================

-- Conversion rules for automatic unit switching (1000g -> 1kg)
CREATE TABLE IF NOT EXISTS unit_conversion_rules (
  id BIGSERIAL PRIMARY KEY,
  category_id TEXT REFERENCES unit_categories(id) ON DELETE CASCADE,
  from_unit TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  to_unit TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  
  -- Rule conditions
  threshold_value DECIMAL(20,10) NOT NULL, -- When to trigger conversion
  rule_type TEXT NOT NULL CHECK (rule_type IN ('auto_switch', 'suggestion', 'validation')),
  
  -- Context
  context TEXT, -- 'input', 'display', 'storage'
  priority INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Validation rules for unit values
CREATE TABLE IF NOT EXISTS unit_validation_rules (
  id BIGSERIAL PRIMARY KEY,
  unit_id TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  
  -- Validation constraints
  min_value DECIMAL(20,10),
  max_value DECIMAL(20,10),
  precision_digits INTEGER DEFAULT 2,
  
  -- Context-specific rules
  context TEXT, -- 'catch_weight', 'fish_length', 'water_temperature', etc.
  error_message TEXT,
  warning_message TEXT,
  
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 4. REGIONAL DEFAULTS SYSTEM
-- =====================================================

-- Regional unit defaults
CREATE TABLE IF NOT EXISTS regional_unit_defaults (
  id BIGSERIAL PRIMARY KEY,
  region_code TEXT NOT NULL, -- 'TR', 'US', 'UK', 'EU', etc.
  region_name TEXT NOT NULL,
  
  -- Default units by category
  weight_unit TEXT REFERENCES unit_definitions(id),
  length_unit TEXT REFERENCES unit_definitions(id),
  distance_unit TEXT REFERENCES unit_definitions(id),
  temperature_unit TEXT REFERENCES unit_definitions(id),
  depth_unit TEXT REFERENCES unit_definitions(id),
  speed_unit TEXT REFERENCES unit_definitions(id),
  pressure_unit TEXT REFERENCES unit_definitions(id),
  
  -- Regional metadata
  currency TEXT DEFAULT 'USD',
  date_format TEXT DEFAULT 'DD/MM/YYYY',
  number_format JSONB DEFAULT '{"decimal": ".", "thousands": ","}',
  
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(region_code)
);

-- =====================================================
-- 5. CONVERSION CACHE & PERFORMANCE
-- =====================================================

-- Conversion cache for frequently used conversions
CREATE TABLE IF NOT EXISTS unit_conversion_cache (
  id BIGSERIAL PRIMARY KEY,
  from_unit TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  to_unit TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  from_value DECIMAL(20,10),
  to_value DECIMAL(20,10),
  
  -- Cache metadata
  hit_count INTEGER DEFAULT 1,
  last_accessed TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(from_unit, to_unit, from_value)
);

-- =====================================================
-- 6. UNIT SYSTEM ANALYTICS
-- =====================================================

-- Track unit usage for analytics
CREATE TABLE IF NOT EXISTS unit_usage_analytics (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  unit_id TEXT REFERENCES unit_definitions(id) ON DELETE CASCADE,
  category_id TEXT REFERENCES unit_categories(id) ON DELETE CASCADE,
  
  -- Usage context
  action_type TEXT NOT NULL, -- 'input', 'display', 'conversion'
  context TEXT, -- 'catch_entry', 'weather_display', 'profile_view'
  
  -- Session info
  session_id TEXT,
  ip_address INET,
  user_agent TEXT,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 7. INDEXES FOR PERFORMANCE
-- =====================================================

-- Unit categories indexes
CREATE INDEX IF NOT EXISTS idx_unit_categories_active ON unit_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_unit_categories_sort ON unit_categories(sort_order);

-- Unit definitions indexes
CREATE INDEX IF NOT EXISTS idx_unit_definitions_category ON unit_definitions(category_id);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_base_unit ON unit_definitions(is_base_unit);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_active ON unit_definitions(is_active);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_regions ON unit_definitions USING GIN(regions);
CREATE INDEX IF NOT EXISTS idx_unit_definitions_popularity ON unit_definitions(popularity DESC);

-- User preferences indexes
CREATE INDEX IF NOT EXISTS idx_user_unit_preferences_user_id ON user_unit_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_unit_preferences_region ON user_unit_preferences(region);
CREATE INDEX IF NOT EXISTS idx_user_unit_preferences_updated ON user_unit_preferences(updated_at DESC);

-- Conversion rules indexes
CREATE INDEX IF NOT EXISTS idx_unit_conversion_rules_category ON unit_conversion_rules(category_id);
CREATE INDEX IF NOT EXISTS idx_unit_conversion_rules_from_to ON unit_conversion_rules(from_unit, to_unit);
CREATE INDEX IF NOT EXISTS idx_unit_conversion_rules_active ON unit_conversion_rules(is_active);

-- Validation rules indexes
CREATE INDEX IF NOT EXISTS idx_unit_validation_rules_unit ON unit_validation_rules(unit_id);
CREATE INDEX IF NOT EXISTS idx_unit_validation_rules_context ON unit_validation_rules(context);

-- Regional defaults indexes
CREATE INDEX IF NOT EXISTS idx_regional_unit_defaults_region ON regional_unit_defaults(region_code);
CREATE INDEX IF NOT EXISTS idx_regional_unit_defaults_active ON regional_unit_defaults(is_active);

-- Conversion cache indexes
CREATE INDEX IF NOT EXISTS idx_unit_conversion_cache_from_to ON unit_conversion_cache(from_unit, to_unit);
CREATE INDEX IF NOT EXISTS idx_unit_conversion_cache_accessed ON unit_conversion_cache(last_accessed DESC);
CREATE INDEX IF NOT EXISTS idx_unit_conversion_cache_hits ON unit_conversion_cache(hit_count DESC);

-- Analytics indexes
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_user ON unit_usage_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_unit ON unit_usage_analytics(unit_id);
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_category ON unit_usage_analytics(category_id);
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_created ON unit_usage_analytics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_unit_usage_analytics_context ON unit_usage_analytics(context);

-- =====================================================
-- 8. TRIGGERS & FUNCTIONS
-- =====================================================

-- Function to update user preferences timestamp
CREATE OR REPLACE FUNCTION update_user_unit_preferences_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  NEW.last_updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for user preferences updates
CREATE TRIGGER trigger_update_user_unit_preferences_timestamp
  BEFORE UPDATE ON user_unit_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_user_unit_preferences_timestamp();

-- Function to update conversion cache hit count
CREATE OR REPLACE FUNCTION update_conversion_cache_hit()
RETURNS TRIGGER AS $$
BEGIN
  NEW.hit_count = OLD.hit_count + 1;
  NEW.last_accessed = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to clean old cache entries
CREATE OR REPLACE FUNCTION clean_conversion_cache()
RETURNS void AS $$
BEGIN
  -- Keep only top 10000 most accessed entries
  DELETE FROM unit_conversion_cache
  WHERE id NOT IN (
    SELECT id FROM unit_conversion_cache
    ORDER BY hit_count DESC, last_accessed DESC
    LIMIT 10000
  );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 9. RLS POLICIES
-- =====================================================

-- Enable RLS on all unit system tables
ALTER TABLE unit_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_definitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_unit_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_conversion_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_validation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE regional_unit_defaults ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_conversion_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE unit_usage_analytics ENABLE ROW LEVEL SECURITY;

-- Unit categories and definitions are public (read-only)
CREATE POLICY "Unit categories are viewable by everyone" ON unit_categories
  FOR SELECT USING (is_active = true);

CREATE POLICY "Unit definitions are viewable by everyone" ON unit_definitions
  FOR SELECT USING (is_active = true);

-- User preferences are private to each user
CREATE POLICY "Users can view their own unit preferences" ON user_unit_preferences
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own unit preferences" ON user_unit_preferences
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own unit preferences" ON user_unit_preferences
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Conversion rules and validation rules are public (read-only)
CREATE POLICY "Conversion rules are viewable by everyone" ON unit_conversion_rules
  FOR SELECT USING (is_active = true);

CREATE POLICY "Validation rules are viewable by everyone" ON unit_validation_rules
  FOR SELECT USING (is_active = true);

-- Regional defaults are public (read-only)
CREATE POLICY "Regional defaults are viewable by everyone" ON regional_unit_defaults
  FOR SELECT USING (is_active = true);

-- Conversion cache is public (read-only for users, write for system)
CREATE POLICY "Conversion cache is viewable by everyone" ON unit_conversion_cache
  FOR SELECT USING (true);

-- Analytics are private (only system can write)
CREATE POLICY "Users can view their own usage analytics" ON unit_usage_analytics
  FOR SELECT USING (auth.uid() = user_id);

-- =====================================================
-- 10. COMMENTS & DOCUMENTATION
-- =====================================================

COMMENT ON TABLE unit_categories IS 'Defines unit categories like weight, length, temperature';
COMMENT ON TABLE unit_definitions IS 'Defines individual units with conversion factors and properties';
COMMENT ON TABLE user_unit_preferences IS 'Stores each user''s preferred units by category';
COMMENT ON TABLE unit_conversion_rules IS 'Rules for automatic unit conversions and suggestions';
COMMENT ON TABLE unit_validation_rules IS 'Validation constraints for unit values in different contexts';
COMMENT ON TABLE regional_unit_defaults IS 'Default unit preferences by region/country';
COMMENT ON TABLE unit_conversion_cache IS 'Cache for frequently used unit conversions';
COMMENT ON TABLE unit_usage_analytics IS 'Analytics data for unit system usage patterns';

COMMENT ON COLUMN unit_definitions.conversion_factor IS 'Multiplier to convert from this unit to base unit';
COMMENT ON COLUMN unit_definitions.conversion_formula IS 'Formula for complex conversions (e.g., temperature)';
COMMENT ON COLUMN unit_definitions.precision_digits IS 'Number of decimal places to display';
COMMENT ON COLUMN user_unit_preferences.auto_detect_region IS 'Whether to auto-update units based on detected region';
COMMENT ON COLUMN unit_conversion_rules.threshold_value IS 'Value threshold that triggers the conversion rule'; 