-- =====================================================
-- MIGRATION 016: ADD USER TITLE FIELD
-- Created: 2024-06-19
-- Description: Adding title column to users table for professional titles
-- =====================================================

-- Add title field to users table
ALTER TABLE users
ADD COLUMN title TEXT;

-- Update existing users with empty title if NULL
UPDATE users
SET title = ''
WHERE title IS NULL;

-- Add comment to column
COMMENT ON COLUMN users.title IS 'Professional title or badge of user displayed in profile';

-- Log migration completion
DO $$
BEGIN
    RAISE NOTICE 'Migration 016: Added title column to users table';
END $$; 