-- Add RPC functions for incrementing and decrementing likes count
-- These functions are called from the backend when a post is liked/unliked
-- They provide a more efficient way to update the likes_count without having to
-- do a full count query every time

-- Function to increment likes count
CREATE OR REPLACE FUNCTION increment_likes_count(post_id INT)
RETURNS void AS $$
BEGIN
  UPDATE posts
  SET likes_count = likes_count + 1,
      updated_at = NOW()
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement likes count
CREATE OR REPLACE FUNCTION decrement_likes_count(post_id INT)
RETURNS void AS $$
BEGIN
  UPDATE posts
  SET likes_count = GREATEST(likes_count - 1, 0), -- Ensure we don't go below 0
      updated_at = NOW()
  WHERE id = post_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get the current likes count for a post
CREATE OR REPLACE FUNCTION get_likes_count(post_id INT)
RETURNS INT AS $$
DECLARE
  like_count INT;
BEGIN
  SELECT COUNT(*) INTO like_count
  FROM likes
  WHERE post_id = $1;
  
  RETURN like_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION increment_likes_count(INT) TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_likes_count(INT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_likes_count(INT) TO authenticated;

-- Add comment explaining the purpose of these functions
COMMENT ON FUNCTION increment_likes_count IS 'Increments the likes_count for a post by 1';
COMMENT ON FUNCTION decrement_likes_count IS 'Decrements the likes_count for a post by 1 (minimum 0)';
COMMENT ON FUNCTION get_likes_count IS 'Returns the current number of likes for a post by counting rows in likes table'; 