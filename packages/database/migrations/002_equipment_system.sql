-- =====================================================
-- MIGRATION 002: EQUIPMENT SYSTEM
-- Created: 2024-01-20
-- Description: Equipment catalog and user equipment management
-- =====================================================

-- =====================================================
-- EQUIPMENT CATALOG
-- =====================================================

-- Equipment catalog table
CREATE TABLE IF NOT EXISTS equipment (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  category TEXT NOT NULL, -- 'Makine', 'Olta', 'Yem', 'İğne', 'Suni Yem', 'Aksesuar'
  subcategory TEXT,
  
  -- Brand and model
  brand TEXT,
  model TEXT,
  sku TEXT,
  
  -- Pricing
  price_range JSONB, -- {min: number, max: number, currency: 'TRY'}
  
  -- Technical specifications
  specifications JSONB DEFAULT '{}',
  
  -- Compatibility
  suitable_for JSONB DEFAULT '[]', -- Fish species
  fishing_type JSONB DEFAULT '[]', -- ['Kıyı', 'Tekne', 'Göl', 'Nehir']
  difficulty_level TEXT CHECK (difficulty_level IN ('Başlangıç', 'Orta', 'İleri', 'Profesyonel')),
  
  -- Media and content
  image_url TEXT,
  images JSONB DEFAULT '[]',
  description TEXT,
  features JSONB DEFAULT '[]', -- Pros
  drawbacks JSONB DEFAULT '[]', -- Cons
  
  -- User ratings and reviews
  user_rating DECIMAL(2,1) DEFAULT 0,
  rating_count INTEGER DEFAULT 0,
  review_count INTEGER DEFAULT 0,
  
  -- Admin
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'draft', 'archived', 'discontinued')),
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User equipment (what users own)
CREATE TABLE IF NOT EXISTS user_equipment (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  equipment_id BIGINT REFERENCES equipment(id) ON DELETE CASCADE,
  
  -- User-specific details
  purchase_date DATE,
  purchase_price DECIMAL(10,2),
  condition TEXT CHECK (condition IN ('Yeni', 'İyi', 'Orta', 'Kötü')),
  notes TEXT,
  
  -- Usage stats
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, equipment_id)
);

-- =====================================================
-- EQUIPMENT CATEGORIES & BRANDS
-- =====================================================

-- Equipment categories
CREATE TABLE IF NOT EXISTS equipment_categories (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  icon TEXT NOT NULL,
  parent_id TEXT REFERENCES equipment_categories(id),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Equipment brands
CREATE TABLE IF NOT EXISTS equipment_brands (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  logo_url TEXT,
  website TEXT,
  country TEXT,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- EQUIPMENT REVIEWS & RATINGS
-- =====================================================

-- Equipment reviews
CREATE TABLE IF NOT EXISTS equipment_reviews (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  equipment_id BIGINT REFERENCES equipment(id) ON DELETE CASCADE,
  
  -- Review content
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  pros JSONB DEFAULT '[]',
  cons JSONB DEFAULT '[]',
  
  -- Usage context
  usage_duration TEXT, -- 'Birkaç gün', '1 ay', '6 ay', '1 yıl+'
  fishing_type TEXT, -- 'Kıyı', 'Tekne', 'Göl'
  experience_level TEXT, -- 'Başlangıç', 'Orta', 'İleri'
  
  -- Moderation
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'hidden', 'pending')),
  
  -- Helpfulness
  helpful_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, equipment_id)
);

-- Review helpfulness votes
CREATE TABLE IF NOT EXISTS equipment_review_votes (
  id BIGSERIAL PRIMARY KEY,
  review_id BIGINT REFERENCES equipment_reviews(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  is_helpful BOOLEAN NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(review_id, user_id)
);

-- =====================================================
-- EQUIPMENT WISHLIST
-- =====================================================

-- Equipment wishlist
CREATE TABLE IF NOT EXISTS equipment_wishlist (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  equipment_id BIGINT REFERENCES equipment(id) ON DELETE CASCADE,
  priority INTEGER DEFAULT 1 CHECK (priority >= 1 AND priority <= 5),
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, equipment_id)
);

-- =====================================================
-- INDEXES
-- =====================================================

-- Equipment indexes
CREATE INDEX IF NOT EXISTS idx_equipment_category ON equipment(category);
CREATE INDEX IF NOT EXISTS idx_equipment_brand ON equipment(brand);
CREATE INDEX IF NOT EXISTS idx_equipment_status ON equipment(status);
CREATE INDEX IF NOT EXISTS idx_equipment_rating ON equipment(user_rating DESC);
CREATE INDEX IF NOT EXISTS idx_equipment_created_at ON equipment(created_at DESC);

-- User equipment indexes
CREATE INDEX IF NOT EXISTS idx_user_equipment_user_id ON user_equipment(user_id);
CREATE INDEX IF NOT EXISTS idx_user_equipment_equipment_id ON user_equipment(equipment_id);

-- Equipment categories indexes
CREATE INDEX IF NOT EXISTS idx_equipment_categories_parent_id ON equipment_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_equipment_categories_sort_order ON equipment_categories(sort_order);

-- Equipment reviews indexes
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_equipment_id ON equipment_reviews(equipment_id);
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_user_id ON equipment_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_rating ON equipment_reviews(rating DESC);
CREATE INDEX IF NOT EXISTS idx_equipment_reviews_created_at ON equipment_reviews(created_at DESC);

-- Equipment wishlist indexes
CREATE INDEX IF NOT EXISTS idx_equipment_wishlist_user_id ON equipment_wishlist(user_id);
CREATE INDEX IF NOT EXISTS idx_equipment_wishlist_priority ON equipment_wishlist(priority DESC);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Update equipment rating when review is added/updated/deleted
CREATE OR REPLACE FUNCTION update_equipment_rating()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE equipment 
        SET 
            user_rating = (
                SELECT ROUND(AVG(rating)::numeric, 1) 
                FROM equipment_reviews 
                WHERE equipment_id = NEW.equipment_id AND status = 'active'
            ),
            rating_count = (
                SELECT COUNT(*) 
                FROM equipment_reviews 
                WHERE equipment_id = NEW.equipment_id AND status = 'active'
            ),
            review_count = (
                SELECT COUNT(*) 
                FROM equipment_reviews 
                WHERE equipment_id = NEW.equipment_id AND status = 'active'
            )
        WHERE id = NEW.equipment_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE equipment 
        SET 
            user_rating = (
                SELECT COALESCE(ROUND(AVG(rating)::numeric, 1), 0) 
                FROM equipment_reviews 
                WHERE equipment_id = OLD.equipment_id AND status = 'active'
            ),
            rating_count = (
                SELECT COUNT(*) 
                FROM equipment_reviews 
                WHERE equipment_id = OLD.equipment_id AND status = 'active'
            ),
            review_count = (
                SELECT COUNT(*) 
                FROM equipment_reviews 
                WHERE equipment_id = OLD.equipment_id AND status = 'active'
            )
        WHERE id = OLD.equipment_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_equipment_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON equipment_reviews
    FOR EACH ROW EXECUTE FUNCTION update_equipment_rating();

-- Update review helpfulness count
CREATE OR REPLACE FUNCTION update_review_helpfulness()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE equipment_reviews 
        SET helpful_count = (
            SELECT COUNT(*) 
            FROM equipment_review_votes 
            WHERE review_id = NEW.review_id AND is_helpful = true
        )
        WHERE id = NEW.review_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE equipment_reviews 
        SET helpful_count = (
            SELECT COUNT(*) 
            FROM equipment_review_votes 
            WHERE review_id = OLD.review_id AND is_helpful = true
        )
        WHERE id = OLD.review_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_review_helpfulness_trigger
    AFTER INSERT OR UPDATE OR DELETE ON equipment_review_votes
    FOR EACH ROW EXECUTE FUNCTION update_review_helpfulness();
