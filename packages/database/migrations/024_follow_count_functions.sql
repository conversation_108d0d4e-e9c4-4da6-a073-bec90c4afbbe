-- Follow count RPC functions
-- Migration name: 024_follow_count_functions

-- Function to increment followers count
CREATE OR REPLACE FUNCTION increment_followers_count(user_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE users 
  SET followers_count = followers_count + 1,
      updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement followers count
CREATE OR REPLACE FUNCTION decrement_followers_count(user_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE users 
  SET followers_count = GREATEST(followers_count - 1, 0),
      updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to increment following count
CREATE OR REPLACE FUNCTION increment_following_count(user_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE users 
  SET following_count = following_count + 1,
      updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrement following count
CREATE OR REPLACE FUNCTION decrement_following_count(user_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE users 
  SET following_count = GREATEST(following_count - 1, 0),
      updated_at = NOW()
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION increment_followers_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_followers_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_following_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_following_count(UUID) TO authenticated; 