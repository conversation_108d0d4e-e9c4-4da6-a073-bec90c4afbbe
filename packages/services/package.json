{"name": "@fishivo/services", "version": "1.0.0", "description": "Shared API services for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx"}, "keywords": ["services", "api", "supabase"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@fishivo/shared": "1.0.0", "@fishivo/utils": "1.0.0", "@supabase/supabase-js": "^2.50.2", "axios": "1.6.7"}, "devDependencies": {"@types/node": "20.11.5", "eslint": "8.56.0", "tsup": "^8.0.0", "typescript": "5.3.3"}}