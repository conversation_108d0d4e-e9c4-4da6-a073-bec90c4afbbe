import { Alert } from 'react-native';
import { launchImageLibrary, launchCamera, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import { API_BASE_URL } from './config';

interface ImageUploadResponse {
  success: boolean;
  data?: {
    variants: {
      thumbnail: string;
      medium: string;
      large: string;
      original: string;
    };
    originalFilename: string;
    size: number;
    uploadType: string;
  };
  error?: string;
}

export interface UploadResponse {
  success: boolean;
  data?: {
    url: string;
    filename: string;
    size: number;
  };
  error?: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export class ImageUploadService {
  private baseUrl = API_BASE_URL;

  constructor() {
    // baseUrl is already set from API_CONFIG
  }

  /**
   * Pick image from camera or gallery
   */
  async pickImage(source: 'camera' | 'gallery'): Promise<ImagePickerResponse | null> {
    try {
      const options = {
        mediaType: 'photo' as MediaType,
        includeBase64: false,
        maxHeight: 2000,
        maxWidth: 2000,
        quality: 0.8 as any,
      };

      return new Promise((resolve) => {
        const callback = (response: ImagePickerResponse) => {
          if (response.didCancel || response.errorMessage) {
            resolve(null);
          } else {
            resolve(response);
          }
        };

        if (source === 'camera') {
          launchCamera(options, callback);
        } else {
          launchImageLibrary(options, callback);
        }
      });
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Hata', 'Resim seçilirken bir hata oluştu.');
      return null;
    }
  }

  /**
   * Upload image to backend (Cloudflare Images)
   */
  async uploadImage(
    imageUri: string,
    metadata: {
      type: 'catch' | 'spot' | 'profile' | 'equipment';
      fishSpecies?: string;
      spotName?: string;
    },
    authToken: string
  ): Promise<ImageUploadResponse> {
    try {
      // Create form data
      const formData = new FormData();
      
      // Add image file
      const imageFile = {
        uri: imageUri,
        type: 'image/jpeg',
        name: `${metadata.type}_${Date.now()}.jpg`,
      } as any;

      formData.append('image', imageFile);
      formData.append('type', metadata.type);
      
      if (metadata.fishSpecies) {
        formData.append('fishSpecies', metadata.fishSpecies);
      }
      
      if (metadata.spotName) {
        formData.append('spotName', metadata.spotName);
      }

      // Upload to backend
      const response = await fetch(`${this.baseUrl}/api/upload/image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'multipart/form-data',
        },
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Upload failed');
      }

      return {
        success: true,
        data: result.data
      };

    } catch (error) {
      console.error('Error uploading image:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Upload multiple images in sequence
   */
  async uploadMultipleImages(
    imageUris: string[],
    metadata: {
      type: 'catch' | 'spot' | 'profile' | 'equipment';
      fishSpecies?: string;
      spotName?: string;
    },
    authToken: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<{
    success: boolean;
    uploadedImages: string[];
    failedImages: string[];
  }> {
    const uploadedImages: string[] = [];
    const failedImages: string[] = [];
    let totalUploaded = 0;

    for (let i = 0; i < imageUris.length; i++) {
      const uri = imageUris[i];
      
      const result = await this.uploadImage(uri, metadata, authToken);
      
      if (result.success && result.data) {
        uploadedImages.push(result.data.variants.medium); // Use medium size for display
      } else {
        failedImages.push(uri);
      }

      // Progress callback
      if (onProgress) {
        const progress: UploadProgress = {
          loaded: i + 1,
          total: imageUris.length,
          percentage: Math.round(((i + 1) / imageUris.length) * 100),
        };
        onProgress(progress);
      }
    }

    return {
      success: failedImages.length === 0,
      uploadedImages,
      failedImages
    };
  }

  /**
   * Show image picker options
   */
  showImagePicker(onSelect: (result: ImagePickerResponse | null) => void) {
    Alert.alert(
      'Fotoğraf Seç',
      'Nereden fotoğraf eklemek istiyorsunuz?',
      [
        { 
          text: 'Kamera', 
          onPress: async () => {
            const result = await this.pickImage('camera');
            onSelect(result);
          }
        },
        { 
          text: 'Galeri', 
          onPress: async () => {
            const result = await this.pickImage('gallery');
            onSelect(result);
          }
        },
        { text: 'İptal', style: 'cancel' }
      ]
    );
  }
}

export default new ImageUploadService(); 