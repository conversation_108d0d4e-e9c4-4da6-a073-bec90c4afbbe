import AsyncStorage from '@react-native-async-storage/async-storage';
import { API_BASE_URL } from '../config';

// API Response types
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

interface Post {
  id: number;
  user_id: string;
  content: string;
  image_url?: string;
  images?: string[];
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    country?: string;
  };
  spot_id?: number;
  trip_id?: number;
  catch_details?: {
    species_id?: number;
    species_name?: string;
    weight?: number;
    length?: number;
    bait_used?: string;
    technique?: string;
    weather_conditions?: string;
    water_temperature?: number;
    time_of_day?: 'morning' | 'afternoon' | 'evening' | 'night';
    equipment_used?: string[];
  };
  likes_count: number;
  comments_count: number;
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    username: string;
    full_name: string;
    avatar_url?: string;
    location?: string;
  };
  spot?: {
    id: number;
    name: string;
    location: {
      latitude: number;
      longitude: number;
      address?: string;
    };
  };
}

class ApiService {
  private autoDetectedIP: string | null = null;

  constructor() {
    // Auto-detect IP on service initialization
    this.initializeIPDetection();
  }

  private async initializeIPDetection() {
    // Simplified for minimal implementation
  }

  // Get the base URL from config
  private get baseURL(): string {
    return API_BASE_URL;
  }

  private async getAuthToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem('userToken');
    } catch (error) {
      console.error('Error getting auth token:', error);
      return null;
    }
  }

  // Token management methods for AuthContext
  async getToken(): Promise<string | null> {
    return this.getAuthToken();
  }

  async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('userToken', token);
    } catch (error) {
      console.error('Error setting auth token:', error);
      throw error;
    }
  }

  async removeToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem('userToken');
    } catch (error) {
      console.error('Error removing auth token:', error);
      throw error;
    }
  }

  async getCurrentUser(): Promise<ApiResponse<any>> {
    try {
      const response = await this.makeRequest<any>('/api/users/me');
      return response;
    } catch (error) {
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const token = await this.getAuthToken();
      
      const headers: { [key: string]: string } = {
        'Content-Type': 'application/json',
        ...((options.headers as { [key: string]: string }) || {}),
      };

      if (token) {
        headers.Authorization = `Bearer ${token}`;
        console.log(`🔐 API: Using token for ${endpoint}:`, token.substring(0, 20) + '...');
      } else {
        console.log(`⚠️ API: No token available for ${endpoint}`);
      }

      // Ensure endpoint starts with a slash
      const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
      
      // Build the full URL
      const url = `${this.baseURL}${normalizedEndpoint}`;
      console.log(`🌐 API Request: ${url}`);
      
      const response = await fetch(url, {
        ...options,
        headers,
      });

      console.log(`📡 API Response Status: ${response.status} for ${endpoint}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        console.log(`❌ API Error Data:`, errorData);
        throw new Error(errorData?.error || `HTTP ${response.status}`);
      }

      const responseText = await response.text();
      try {
        console.log(`📄 RAW API Response for ${endpoint}:`, responseText);
        const result = JSON.parse(responseText);
        console.log(`✅ API Response: ${endpoint}`, {
          success: result.success,
          dataType: typeof result.data,
          hasData: !!result.data
        });
        return result;
      } catch (e) {
        console.error(`❌ API JSON Parse Error [${endpoint}]:`, e);
        console.error(`📄 Faulty JSON string:`, responseText);
        throw new Error('Failed to parse JSON response from server.');
      }
    } catch (error) {
      console.error(`❌ API Error [${endpoint}]:`, error);
      throw error;
    }
  }

  // Generic POST helper
  public async post<T>(endpoint: string, body: any, extraOptions: RequestInit = {}): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(body),
      ...extraOptions,
    });
  }

  // 🎣 Catches API - Ana balık avları endpoint'i
  async getCatches(page = 1, limit = 10, filters?: {
    userId?: string;
    spotId?: number;
    tripId?: number;
  }): Promise<PaginatedResponse<Post>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters?.userId) params.append('user_id', filters.userId);
    if (filters?.spotId) params.append('spot_id', filters.spotId.toString());
    if (filters?.tripId) params.append('trip_id', filters.tripId.toString());

    const response = await this.makeRequest<PaginatedResponse<Post>>(
      `/api/posts?${params.toString()}`
    );
    
    return response.data;
  }

  // 🔄 Geriye uyumluluk için
  async getPosts(page = 1, limit = 10, filters?: {
    userId?: string;
    spotId?: number;
    tripId?: number;
  }): Promise<PaginatedResponse<Post>> {
    return this.getCatches(page, limit, filters);
  }

  async getPostById(postId: number): Promise<Post> {
    const response = await this.makeRequest<Post>(`/api/posts/${postId}`);
    return response.data;
  }

  async createPost(postData: {
    content: string;
    image_url?: string;
    images?: string[];
    location?: {
      latitude: number;
      longitude: number;
      address?: string;
      city?: string;
      country?: string;
    };
    spot_id?: number;
    trip_id?: number;
    catch_details?: {
      species_id?: number;
      species_name?: string;
      weight?: number;
      length?: number;
      bait_used?: string;
      technique?: string;
      weather_conditions?: string;
      water_temperature?: number;
      time_of_day?: 'morning' | 'afternoon' | 'evening' | 'night';
      equipment_used?: string[];
    };
  }): Promise<Post> {
    const response = await this.makeRequest<Post>('/api/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    });
    return response.data;
  }

  // Helper function for retry logic
  private async withRetry<T>(
    operation: () => Promise<T>,
    retries = 3,
    delay = 500
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (retries <= 0) throw error;
      
      console.log(`Retrying API operation. Attempts remaining: ${retries}`);
      await new Promise(resolve => setTimeout(resolve, delay));
      return this.withRetry(operation, retries - 1, delay * 1.5);
    }
  }

  /**
   * Enhanced like operations with optimistic updates and retry logic
   */
  private likeOperations = {
    /**
     * Check if the current user has liked a post
     */
    isPostLiked: async (postId: number): Promise<boolean> => {
      try {
        const response = await this.withRetry(() => 
          this.makeRequest<{ liked: boolean; postId: number; userId: string }>(`/api/posts/${postId}/like`)
        );
        return response.data?.liked || false;
      } catch (error) {
        console.error(`Error checking if post ${postId} is liked:`, error);
        return false;
      }
    },
    
    /**
     * Like a post with retry mechanism
     */
    likePost: async (postId: number): Promise<any> => {
      return this.withRetry(() => 
        this.makeRequest<any>(`/api/posts/${postId}/like`, {
          method: 'POST',
        })
      );
    },
    
    /**
     * Unlike a post with retry mechanism
     */
    unlikePost: async (postId: number): Promise<any> => {
      return this.withRetry(() => 
        this.makeRequest<any>(`/api/posts/${postId}/like`, {
          method: 'DELETE',
        })
      );
    },
    
    /**
     * Get users who liked a post with pagination
     */
    getPostLikers: async (postId: number, page = 1, limit = 20): Promise<{
      items: Array<{
        id: string;
        username: string;
        full_name?: string;
        avatar_url?: string;
        liked_at?: string;
        is_verified?: boolean;
        is_pro?: boolean;
      }>;
      total: number;
      page: number;
      limit: number;
      hasMore: boolean;
    }> => {
      try {
        const response = await this.makeRequest<any>(
          `/api/posts/${postId}/likers?page=${page}&limit=${limit}`
        );
        
        return response.data || { 
          items: [], 
          total: 0, 
          page, 
          limit, 
          hasMore: false 
        };
      } catch (error) {
        console.error(`Error fetching likers for post ${postId}:`, error);
        throw error;
      }
    },
    
    /**
     * Get posts that current user has liked
     */
    getLikedPosts: async (page = 1, limit = 20) => {
      try {
        const response = await this.makeRequest<any>(
          `/api/posts/likes/user?page=${page}&limit=${limit}`
        );
        
        return response.data || { 
          items: [], 
          total: 0, 
          page, 
          limit, 
          hasMore: false 
        };
      } catch (error) {
        console.error('Error fetching liked posts:', error);
        throw error;
      }
    },
    
    /**
     * Get like status for multiple posts at once (batch operation)
     */
    getBatchLikeStatus: async (postIds: number[]): Promise<Record<number, boolean>> => {
      if (!postIds.length) return {};
      
      try {
        const response = await this.makeRequest<any>(
          `/api/posts/likes/batch-status`,
          {
            method: 'POST',
            body: JSON.stringify({ postIds })
          }
        );
        
        return response.data?.likes || {};
      } catch (error) {
        console.error('Error fetching batch like status:', error);
        return {};
      }
    },
    
    /**
     * Get like counts for multiple posts at once (batch operation)
     */
    getBatchLikeCounts: async (postIds: number[]): Promise<Record<number, number>> => {
      if (!postIds.length) return {};
      
      try {
        const response = await this.makeRequest<any>(
          `/api/posts/likes/counts`,
          {
            method: 'POST',
            body: JSON.stringify({ postIds })
          }
        );
        
        return response.data?.counts || {};
      } catch (error) {
        console.error('Error fetching batch like counts:', error);
        return {};
      }
    }
  };

  // Like operations - using optimized methods
  async isPostLiked(postId: number): Promise<boolean> {
    return this.likeOperations.isPostLiked(postId);
  }

  async likePost(postId: number): Promise<any> {
    return this.likeOperations.likePost(postId);
  }

  async unlikePost(postId: number): Promise<any> {
    return this.likeOperations.unlikePost(postId);
  }

  async getPostLikers(postId: number, page = 1, limit = 20): Promise<{
    items: Array<{
      id: string;
      username: string;
      full_name?: string;
      avatar_url?: string;
      liked_at?: string;
      is_verified?: boolean;
      is_pro?: boolean;
    }>;
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
  }> {
    return this.likeOperations.getPostLikers(postId, page, limit);
  }

  async getLikedPosts(page = 1, limit = 20) {
    return this.likeOperations.getLikedPosts(page, limit);
  }

  async getBatchLikeStatus(postIds: number[]): Promise<Record<number, boolean>> {
    return this.likeOperations.getBatchLikeStatus(postIds);
  }

  async getBatchLikeCounts(postIds: number[]): Promise<Record<number, number>> {
    return this.likeOperations.getBatchLikeCounts(postIds);
  }

  // Spots API
  async getSpots(page = 1, limit = 10): Promise<PaginatedResponse<any>> {
    limit = Math.min(limit, 50);
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    try {
      console.log('🗺️ API: Fetching spots...');
      const response = await this.makeRequest<PaginatedResponse<any>>(`/api/spots?${params.toString()}`);
      console.log(`✅ API: Fetched ${response.data?.items?.length || 0} spots`);
      return response.data;
    } catch (error) {
      console.error('❌ API Error [getSpots]:', error);
      // Return empty paginated response instead of throwing
      return {
        items: [],
        total: 0,
        page,
        limit,
        hasMore: false
      };
    }
  }

  async searchSpots(query: string, page = 1, limit = 10): Promise<PaginatedResponse<any>> {
    const response = await this.makeRequest<PaginatedResponse<any>>(
      `/api/spots/search?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async getSpotById(spotId: number): Promise<any> {
    const response = await this.makeRequest<any>(`/api/spots/${spotId}`);
    return response.data;
  }

  // Upload API
  async uploadImage(imageUri: string): Promise<string> {
    const response = await this.makeRequest<{ url: string }>('/api/upload/image', {
      method: 'POST',
      body: JSON.stringify({ imageUri }),
    });
    return response.data.url;
  }

  // Species API
  async getSpecies(): Promise<any[]> {
    const response = await this.makeRequest<any[]>('/api/species');
    return response.data;
  }

  async getSpeciesById(id: number): Promise<any> {
    const response = await this.makeRequest<any>(`/api/species/${id}`);
    return response.data;
  }

  // Notifications API
  async getNotifications(userId: string, page = 1, limit = 20): Promise<any[]> {
    const response = await this.makeRequest<any[]>(
      `/api/notifications?userId=${userId}&page=${page}&limit=${limit}`
    );
    return response.data;
  }

  // User Equipment API
  async getUserEquipment(): Promise<any[]> {
    const response = await this.makeRequest<any[]>('/api/users/me/equipment');
    return response.data;
  }

  async addUserEquipment(equipmentData: any): Promise<any> {
    const response = await this.makeRequest<any>('/api/users/me/equipment', {
      method: 'POST',
      body: JSON.stringify(equipmentData),
    });
    return response.data;
  }

  // Profile API
  async updateProfile(profileData: any): Promise<any> {
    try {
      console.log('🔄 API updateProfile called with data:', JSON.stringify(profileData));
      
      // Username kontrolü - sadece trim edilmiş hali boşsa hata ver
      if (!profileData.username || profileData.username.trim() === '') {
        throw new Error('Kullanıcı adı zorunludur ve boş bırakılamaz.');
      }
      
      // Validate required fields or set defaults
      const safeData = {
        ...profileData,
        username: profileData.username.trim(), // Boşlukları temizle
        full_name: profileData.full_name || '',
        bio: profileData.bio || '',
        title: profileData.title || '',
        location: profileData.location || '',
      };
      
      const response = await this.makeRequest<any>('/api/users/me', {
        method: 'PUT',
        body: JSON.stringify(safeData),
      });
      
      if (!response.success) {
        console.error('❌ API Error during profile update:', response.error);
        throw new Error(response.error || 'Profile update failed');
      }
      
      console.log('✅ Profile updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('❌ API Profile Update Error:', error);
      throw error;
    }
  }

  // Get user profile statistics
  async getUserStats(): Promise<any> {
    const response = await this.makeRequest<any>('/api/users/me/stats');
    return response.data;
  }

  // Get current user full profile data
  async getUserProfile(userId: string): Promise<ApiResponse<any>> {
    // Eğer kullanıcı kendi profilini yüklüyorsa /api/users/me kullan
    const currentUser = await this.getCurrentUser();
    if (currentUser.success && currentUser.data && currentUser.data.id === userId) {
      return currentUser;
    }
    
    // Başka bir kullanıcının profilini yüklüyorsa /api/users/:id kullan
    const response = await this.makeRequest<any>(`/api/users/${userId}`);
    return response;
  }

  // Blocked Users API
  async getBlockedUsers(page = 1, limit = 20): Promise<PaginatedResponse<any>> {
    const response = await this.makeRequest<PaginatedResponse<any>>(
      `/api/users/me/blocked?page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async blockUser(userId: string): Promise<void> {
    await this.makeRequest<void>(`/api/users/block/${userId}`, {
      method: 'POST',
    });
  }

  async unblockUser(userId: string): Promise<void> {
    await this.makeRequest<void>(`/api/users/block/${userId}`, {
      method: 'DELETE',
    });
  }

  /* 🤝 Follow API */
  async followUser(userId: string): Promise<void> {
    await this.makeRequest<void>(`/api/follows`, {
      method: 'POST',
      body: JSON.stringify({ following_id: userId }),
    });
  }

  async unfollowUser(userId: string): Promise<void> {
    await this.makeRequest<void>(`/api/follows/${userId}`, {
      method: 'DELETE',
    });
  }

  async isFollowing(userId: string): Promise<boolean> {
    const response = await this.makeRequest<{ isFollowing: boolean }>(`/api/follows/check/${userId}`);
    if ((response.data as any)?.isFollowing !== undefined) {
      return (response.data as any).isFollowing;
    }
    return (response as any)?.data?.isFollowing ?? false;
  }

  // Followers/Following count for any user
  async getUserSocialCounts(userId: string): Promise<{ followers: number; following: number }> {
    try {
      const [followersRes, followingRes] = await Promise.all([
        this.makeRequest<any>(`/api/follows/${userId}/followers?page=1&limit=1`),
        this.makeRequest<any>(`/api/follows/${userId}/following?page=1&limit=1`),
      ]);

      const followers = followersRes.data?.pagination?.total ?? 0;
      const following = followingRes.data?.pagination?.total ?? 0;

      return { followers, following };
    } catch (error) {
      console.error('getUserSocialCounts error:', error);
      return { followers: 0, following: 0 };
    }
  }

  // Search API
  async searchUsers(query: string, page = 1, limit = 10): Promise<PaginatedResponse<any>> {
    const response = await this.makeRequest<PaginatedResponse<any>>(
      `/api/search/users?query=${encodeURIComponent(query)}&page=${page}&limit=${limit}`
    );
    return response.data;
  }

  async searchSpecies(query: string): Promise<any[]> {
    const response = await this.makeRequest<any[]>(
      `/api/species/search?query=${encodeURIComponent(query)}`
    );
    return response.data;
  }

  async searchEquipment(query: string): Promise<any[]> {
    const response = await this.makeRequest<any[]>(
      `/api/equipment/search?query=${encodeURIComponent(query)}`
    );
    return response.data;
  }

  // Weather API
  async getCurrentWeather(lat: number, lon: number): Promise<any> {
    const response = await this.makeRequest<any>(`/api/weather/current?lat=${lat}&lon=${lon}`);
    return response.data;
  }

  async getWeatherForecast(lat: number, lon: number, days = 5): Promise<any> {
    const response = await this.makeRequest<any>(`/api/weather/forecast?lat=${lat}&lon=${lon}&days=${days}`);
    return response.data;
  }

  async getLocationName(lat: number, lon: number): Promise<any> {
    const response = await this.makeRequest<any>(`/api/weather/reverse-geocode?lat=${lat}&lon=${lon}`);
    return response.data;
  }

  // Equipment/Gear API
  async getGearDatabase(page = 1, limit = 50, category?: string): Promise<any> {
    const params = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (category) params.append('category', category);

    const response = await this.makeRequest<any>(`/api/equipment?${params.toString()}`);
    return response.data;
  }

  async getEquipmentCategories(): Promise<any> {
    const response = await this.makeRequest<any>('/api/equipment/categories');
    return response.data;
  }

  // User catches API
  async getUserCatches(userId: string): Promise<ApiResponse<any>> {
    const response = await this.makeRequest<any>(`/api/users/${userId}/catches`);
    return response;
  }

  // Fishing Techniques API
  async getFishingTechniques(): Promise<any[]> {
    const response = await this.makeRequest<any[]>('/api/techniques');
    return response.data;
  }

  async searchFishingTechniques(query: string): Promise<any[]> {
    const response = await this.makeRequest<any[]>(`/api/techniques/search?q=${encodeURIComponent(query)}`);
    return response.data;
  }

  async getFishingTechniqueById(id: number): Promise<any> {
    const response = await this.makeRequest<any>(`/api/techniques/${id}`);
    return response.data;
  }

  // User Locations API
  async getUserLocations(): Promise<any[]> {
    try {
      console.log('🗺️ API: Fetching user locations...');
      const response = await this.makeRequest<any[]>('/api/users/me/locations');
      console.log(`✅ API: Fetched ${response.data?.length || 0} user locations`);
      return response.data || [];
    } catch (error) {
      console.error('❌ API Error [getUserLocations]:', error);
      // Return empty array instead of throwing to prevent app crashes
      return [];
    }
  }

  /* 🚫 Hesap yönetimi */
  async deactivateAccount(): Promise<void> {
    await this.makeRequest('/api/users/me/deactivate', {
      method: 'PUT'
    });
  }
}

// Export a singleton instance
export const apiService = new ApiService();
export type { Post, ApiResponse, PaginatedResponse }; 