import { createClient } from '@supabase/supabase-js';
import Config from 'react-native-config';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SUPABASE_CONFIG } from '../config';

// Debug environment variables
console.log('🔍 Environment Variables Debug:');
console.log('SUPABASE_URL:', Config.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'NOT SET');
console.log('SUPABASE_ANON_KEY:', Config.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET');
console.log('All Config:', Config);

// Supabase configuration with fallbacks
const SUPABASE_URL = Config.NEXT_PUBLIC_SUPABASE_URL || SUPABASE_CONFIG.url;
const SUPABASE_ANON_KEY = Config.NEXT_PUBLIC_SUPABASE_ANON_KEY || SUPABASE_CONFIG.anonKey;

console.log('🔧 Using Supabase URL:', SUPABASE_URL);
console.log('🔧 Using Supabase Key:', SUPABASE_ANON_KEY ? SUPABASE_ANON_KEY.substring(0, 20) + '...' : 'undefined');

if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
  console.error('❌ Missing Supabase configuration');
  console.error('SUPABASE_URL:', SUPABASE_URL);
  console.error('SUPABASE_ANON_KEY:', SUPABASE_ANON_KEY);
  throw new Error('Supabase configuration is required. Please check your environment variables.');
}

// Create custom AsyncStorage adapter for Supabase
const asyncStorageAdapter = {
  getItem: (key: string) => AsyncStorage.getItem(key),
  setItem: (key: string, value: string) => AsyncStorage.setItem(key, value),
  removeItem: (key: string) => AsyncStorage.removeItem(key)
};

// Create Supabase client with React Native optimizations
export const supabase = createClient(SUPABASE_URL!, SUPABASE_ANON_KEY!, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false, // Important for React Native
    storage: asyncStorageAdapter,
  },
  global: {
    headers: {
      'X-Client-Info': 'fishivo-mobile@1.0.0',
    },
  },
});

// Listen for auth state changes
supabase.auth.onAuthStateChange((event, session) => {
  console.log('🔐 Supabase Auth State Change:', event);
  if (event === 'SIGNED_IN' && session) {
    console.log('✅ User signed in:', session.user.email);
    // You could update your app state here
  } else if (event === 'SIGNED_OUT') {
    console.log('🚪 User signed out');
    // Clean up user data here if needed
  } else if (event === 'TOKEN_REFRESHED') {
    console.log('🔄 Token refreshed');
  }
});

// Types
export interface User {
  id: string;
  email: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  bio?: string;
  location?: string;
  is_pro: boolean;
  total_catches: number;
  total_spots: number;
  reputation_score: number;
  created_at: string;
  updated_at?: string;
}

export interface FishSpecies {
  id: number;
  name: string;
  scientific_name: string;
  category: string;
  min_size: number;
  max_size: number;
  avg_weight: number;
  season: string[];
  habitat: string[];
  bait_types: string[];
  difficulty: string;
  description: string;
  image_url?: string;
}

export interface FishingSpot {
  id: number;
  name: string;
  description: string;
  location: {
    name: string;
    city: string;
    country: string;
    coordinates: [number, number];
  };
  user_id: string;
  rating: number;
  depth_min: number;
  depth_max: number;
  facilities: string[];
  fish_species: string[];
  difficulty: string;
  is_verified: boolean;
  catch_count: number;
  image_url?: string;
  created_at: string;
}

export interface CatchPost {
  id: number;
  user_id: string;
  species_name: string;
  weight?: number;
  length?: number;
  location: {
    name: string;
    coordinates: [number, number];
  };
  image_url?: string;
  description?: string;
  equipment_used?: string[];
  weather_conditions?: any;
  created_at: string;
  likes_count: number;
  comments_count: number;
}

export interface Equipment {
  id: number;
  name: string;
  brand: string;
  category: string;
  subcategory?: string;
  model?: string;
  specifications?: any;
  price_range?: {
    min: number;
    max: number;
  };
  image_url?: string;
  description?: string;
  user_rating?: number;
  review_count?: number;
}

export interface Notification {
  id: number;
  user_id: string;
  type: 'like' | 'comment' | 'follow' | 'post' | 'system' | 'catch' | 'location';
  title: string;
  description?: string;
  data?: any;
  is_read: boolean;
  created_at: string;
}

// Database Service Class
class DatabaseService {
  
  // ================== USER OPERATIONS ==================
  
  async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      if (error || !user) return null;
      
      const { data, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (userError) {
        console.error('Error fetching user:', userError);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  async getUserById(userId: string): Promise<User | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (error) {
        console.error('Error fetching user by ID:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();
      
      if (error) {
        console.error('Error updating user:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error updating user:', error);
      return null;
    }
  }

  // ================== FISH SPECIES OPERATIONS ==================
  
  async getFishSpecies(): Promise<FishSpecies[]> {
    try {
      const { data, error } = await supabase
        .from('fish_species')
        .select('*')
        .order('name');
      
      if (error) {
        console.error('Error fetching fish species:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Error getting fish species:', error);
      return [];
    }
  }

  async getFishSpeciesById(id: number): Promise<FishSpecies | null> {
    try {
      const { data, error } = await supabase
        .from('fish_species')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        console.error('Error fetching fish species by ID:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error getting fish species by ID:', error);
      return null;
    }
  }

  // ================== FISHING SPOTS OPERATIONS ==================
  
  async getFishingSpots(limit = 20, offset = 0): Promise<FishingSpot[]> {
    try {
      const { data, error } = await supabase
        .from('spots')
        .select(`
          *,
          users:user_id (
            username,
            full_name
          )
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (error) {
        console.error('Error fetching fishing spots:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Error getting fishing spots:', error);
      return [];
    }
  }

  async getFishingSpotById(id: number): Promise<FishingSpot | null> {
    try {
      const { data, error } = await supabase
        .from('spots')
        .select(`
          *,
          users:user_id (
            username,
            full_name,
            avatar_url
          )
        `)
        .eq('id', id)
        .single();
      
      if (error) {
        console.error('Error fetching fishing spot by ID:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error getting fishing spot by ID:', error);
      return null;
    }
  }

  async createFishingSpot(spotData: Omit<FishingSpot, 'id' | 'created_at'>): Promise<FishingSpot | null> {
    try {
      const { data, error } = await supabase
        .from('spots')
        .insert({
          ...spotData,
          created_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) {
        console.error('Error creating fishing spot:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error creating fishing spot:', error);
      return null;
    }
  }

  // ================== POSTS OPERATIONS ==================
  
  async getPosts(limit = 20, offset = 0): Promise<CatchPost[]> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          users:user_id (
            username,
            full_name,
            avatar_url,
            is_pro
          ),
          fish_species:species_id (
            name,
            scientific_name
          )
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (error) {
        console.error('Error fetching posts:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Error getting posts:', error);
      return [];
    }
  }

  async getUserPosts(userId: string, limit = 20, offset = 0): Promise<CatchPost[]> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          users:user_id (
            username,
            full_name,
            avatar_url
          ),
          fish_species:species_id (
            name,
            scientific_name
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (error) {
        console.error('Error fetching user posts:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Error getting user posts:', error);
      return [];
    }
  }

  async createPost(postData: Omit<CatchPost, 'id' | 'created_at' | 'likes_count' | 'comments_count'>): Promise<CatchPost | null> {
    try {
      const { data, error } = await supabase
        .from('posts')
        .insert({
          ...postData,
          created_at: new Date().toISOString(),
          likes_count: 0,
          comments_count: 0
        })
        .select()
        .single();
      
      if (error) {
        console.error('Error creating post:', error);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error creating post:', error);
      return null;
    }
  }

  // ================== EQUIPMENT OPERATIONS ==================
  
  async getEquipment(category?: string): Promise<Equipment[]> {
    try {
      let query = supabase
        .from('equipment')
        .select('*')
        .order('name');
      
      if (category) {
        query = query.eq('category', category);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error('Error fetching equipment:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Error getting equipment:', error);
      return [];
    }
  }

  async getUserEquipment(userId: string): Promise<Equipment[]> {
    try {
      const { data, error } = await supabase
        .from('user_equipment')
        .select(`
          *,
          equipment:equipment_id (
            *
          )
        `)
        .eq('user_id', userId);
      
      if (error) {
        console.error('Error fetching user equipment:', error);
        return [];
      }
      
      return data?.map(item => item.equipment) || [];
    } catch (error) {
      console.error('Error getting user equipment:', error);
      return [];
    }
  }

  // ================== NOTIFICATIONS OPERATIONS ==================
  
  async getNotifications(userId: string, limit = 20, offset = 0): Promise<Notification[]> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (error) {
        console.error('Error fetching notifications:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Error getting notifications:', error);
      return [];
    }
  }

  async markNotificationAsRead(notificationId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);
      
      if (error) {
        console.error('Error marking notification as read:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }

  // ================== LIKES & INTERACTIONS ==================
  
  async likePost(userId: string, postId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('likes')
        .insert({
          user_id: userId,
          post_id: postId,
          created_at: new Date().toISOString()
        });
      
      if (error) {
        console.error('Error liking post:', error);
        return false;
      }
      
      // Update post likes count
      await supabase.rpc('increment_post_likes', { post_id: postId });
      
      return true;
    } catch (error) {
      console.error('Error liking post:', error);
      return false;
    }
  }

  async unlikePost(userId: string, postId: number): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('likes')
        .delete()
        .eq('user_id', userId)
        .eq('post_id', postId);
      
      if (error) {
        console.error('Error unliking post:', error);
        return false;
      }
      
      // Update post likes count
      await supabase.rpc('decrement_post_likes', { post_id: postId });
      
      return true;
    } catch (error) {
      console.error('Error unliking post:', error);
      return false;
    }
  }

  // ================== SEARCH OPERATIONS ==================
  
  async searchPosts(query: string, filters: any = {}): Promise<CatchPost[]> {
    try {
      let supabaseQuery = supabase
        .from('posts')
        .select(`
          *,
          users:user_id (
            username,
            full_name,
            avatar_url
          ),
          fish_species:species_id (
            name,
            scientific_name
          )
        `)
        .order('created_at', { ascending: false });
      
      if (query) {
        supabaseQuery = supabaseQuery.textSearch('description', query);
      }
      
      if (filters.species) {
        supabaseQuery = supabaseQuery.ilike('species_name', `%${filters.species}%`);
      }
      
      const { data, error } = await supabaseQuery.limit(50);
      
      if (error) {
        console.error('Error searching posts:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Error searching posts:', error);
      return [];
    }
  }

  async searchSpots(query: string): Promise<FishingSpot[]> {
    try {
      const { data, error } = await supabase
        .from('spots')
        .select('*')
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .order('rating', { ascending: false })
        .limit(20);
      
      if (error) {
        console.error('Error searching spots:', error);
        return [];
      }
      
      return data || [];
    } catch (error) {
      console.error('Error searching spots:', error);
      return [];
    }
  }

  // ================== UTILITY FUNCTIONS ==================
  
  async uploadImage(file: any, bucket: string, fileName: string): Promise<string | null> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(fileName, file);
      
      if (error) {
        console.error('Error uploading image:', error);
        return null;
      }
      
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(fileName);
      
      return publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      return null;
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
export default databaseService; 