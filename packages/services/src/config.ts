/**
 * Services Configuration
 * Centralized configuration for @fishivo/services package
 */

// Environment detection
const isDevelopment = process.env.NODE_ENV === 'development';

// Development IP for local testing
const DEVELOPMENT_IP = '*************'; // Update this to your local IP

// API Base URL Configuration
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 
  process.env.API_URL || 
  (isDevelopment ? `http://${DEVELOPMENT_IP}:3001` : 'https://api.fishivo.com');

// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
};

// Google OAuth Configuration
export const GOOGLE_WEB_CLIENT_ID = process.env.GOOGLE_WEB_CLIENT_ID || process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '';

// API Configuration
export const API_CONFIG = {
  baseURL: API_BASE_URL,
  timeout: parseInt(process.env.API_TIMEOUT || '10000'),
  retries: parseInt(process.env.API_RETRIES || '3'),
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '5242880'), // 5MB
};

// Upload Configuration
export const UPLOAD_CONFIG = {
  maxFileSize: API_CONFIG.maxFileSize,
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
  quality: 0.8,
  maxWidth: 1920,
  maxHeight: 1080,
};

// Cache Configuration
export const CACHE_CONFIG = {
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 100, // Maximum number of cached items
  enableCache: true,
};

// Debug logging
if (isDevelopment) {
  console.log('🔧 Services Config:', {
    apiBaseUrl: API_BASE_URL,
    supabaseUrl: SUPABASE_CONFIG.url ? 'configured' : 'missing',
    environment: process.env.NODE_ENV || 'development',
  });
}

export default {
  API_BASE_URL,
  SUPABASE_CONFIG,
  API_CONFIG,
  UPLOAD_CONFIG,
  CACHE_CONFIG,
  GOOGLE_WEB_CLIENT_ID,
};
