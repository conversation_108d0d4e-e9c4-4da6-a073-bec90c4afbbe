const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '../../.env' });

const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SERVICE_ROLE_KEY) {
  console.error('Supabase URL veya Service Role Key eksik!');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY);

async function updateUser(userId, updates) {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single();
  if (error) {
    console.error('❌ Kullanıcı güncellenemedi:', error);
    process.exit(1);
  }
  console.log('✅ <PERSON>llanıcı güncellendi:', data);
}

// ÖRNEK: userId ve yeni bilgiler
const userId = process.argv[2];
const newUsername = process.argv[3] || 'testuser_script';
const newFullName = process.argv[4] || 'Test User Script';

if (!userId) {
  console.error('Kullanıcı ID girilmelidir!');
  process.exit(1);
}

updateUser(userId, { username: newUsername, full_name: newFullName }); 