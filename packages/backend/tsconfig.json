{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": false, "skipLibCheck": true, "noImplicitAny": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "declarationMap": false, "sourceMap": false, "removeComments": true, "composite": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "references": [{"path": "../shared"}, {"path": "../config"}, {"path": "../services"}, {"path": "../utils"}]}