import { Request, Response, NextFunction } from 'express';
import { supabase } from '../services/supabase';
import { AppError } from './errorHandler';
import { User } from '../types';

// Admin role kontrolü
export const requireAdmin = async (req: Request, _res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new AppError('Authentication required', 401, 'AUTH_REQUIRED');
    }

    // Admin role kontrolü
    const user = req.user as any;
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      throw new AppError('Admin access required', 403, 'ADMIN_ACCESS_REQUIRED');
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Super admin kontrolü
export const requireSuperAdmin = async (req: Request, _res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      throw new AppError('Authentication required', 401, 'AUTH_REQUIRED');
    }

    const user = req.user as any;
    if (user.role !== 'super_admin') {
      throw new AppError('Super admin access required', 403, 'SUPER_ADMIN_ACCESS_REQUIRED');
    }

    next();
  } catch (error) {
    next(error);
  }
};

// IP whitelist kontrolü
const ADMIN_ALLOWED_IPS = process.env.ADMIN_ALLOWED_IPS?.split(',') || [];

export const ipWhitelist = (req: Request, _res: Response, next: NextFunction) => {
  if (ADMIN_ALLOWED_IPS.length === 0) {
    // IP whitelist tanımlanmamışsa geç
    return next();
  }

  const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
  
  if (!ADMIN_ALLOWED_IPS.includes(clientIP)) {
    throw new AppError('IP not authorized for admin access', 403, 'IP_NOT_AUTHORIZED');
  }

  next();
};

// Admin işlemlerini logla
export const logAdminAction = async (req: Request, _res: Response, next: NextFunction) => {
  try {
    if (req.user) {
      const user = req.user as any;
      if (user.role === 'admin' || user.role === 'super_admin') {
        // Admin log kaydı
        await supabase
          .from('admin_logs')
          .insert({
            admin_id: user.id,
            action: `${req.method} ${req.originalUrl}`,
            ip_address: req.ip,
            user_agent: req.get('User-Agent'),
            request_body: req.method !== 'GET' ? JSON.stringify(req.body) : null,
            created_at: new Date().toISOString()
          });
      }
    }
    next();
  } catch (error) {
    // Log hatası admin işlemini durdurmasın
    console.error('Admin log error:', error);
    next();
  }
};
