import { Request, Response, NextFunction } from 'express';

export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const start = Date.now();
  
  // Log request
  console.log(`📥 ${req.method} ${req.url} - ${req.ip} - ${new Date().toISOString()}`);
  
  // Log request body for POST/PUT/PATCH (excluding sensitive data)
  if (['POST', 'PUT', 'PATCH'].includes(req.method) && req.body) {
    const logBody = { ...req.body };
    
    // Remove sensitive fields
    if (logBody.password) logBody.password = '[REDACTED]';
    if (logBody.token) logBody.token = '[REDACTED]';
    if (logBody.secret) logBody.secret = '[REDACTED]';
    
    console.log(`📝 Request Body:`, JSON.stringify(logBody, null, 2));
  }

  // Override res.json to log response
  const originalJson = res.json;
  res.json = function(body: any) {
    const duration = Date.now() - start;

    // Log response
    console.log(`📤 ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);

    // Log response body for errors or in development
    if (res.statusCode >= 400 || process.env.NODE_ENV === 'development') {
      const logBody = maskSensitiveData({ ...body });
      console.log(`📋 Response Body:`, JSON.stringify(logBody, null, 2));
    }

    return originalJson.call(this, body);
  };

  // Helper function to mask sensitive data
  function maskSensitiveData(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj;

    const sensitiveFields = [
      'password', 'token', 'access_token', 'refresh_token',
      'secret', 'key', 'authorization', 'cookie', 'session',
      'jwt', 'apikey', 'api_key', 'client_secret', 'private_key'
    ];

    const masked = { ...obj };

    for (const field of sensitiveFields) {
      if (masked[field]) {
        masked[field] = '[REDACTED]';
      }
    }

    // Recursively mask nested objects
    for (const key in masked) {
      if (typeof masked[key] === 'object' && masked[key] !== null) {
        masked[key] = maskSensitiveData(masked[key]);
      }
    }

    return masked;
  }

  next();
};
