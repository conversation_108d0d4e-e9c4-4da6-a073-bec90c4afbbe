import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory } from 'rate-limiter-flexible';

// Create rate limiter instance
const rateLimiter = new RateLimiterMemory({
  points: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100'), // Number of requests
  duration: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900'), // Per 15 minutes (900 seconds)
});

// Strict rate limiter for auth endpoints
const authRateLimiter = new RateLimiterMemory({
  points: 5, // 5 requests
  duration: 900, // Per 15 minutes
});

// Upload rate limiter
const uploadRateLimiter = new RateLimiterMemory({
  points: 10, // 10 uploads
  duration: 3600, // Per hour
});

export const rateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await rateLimiter.consume(req.ip || 'unknown');
    next();
  } catch (rejRes: any) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      success: false,
      error: 'Too many requests',
      message: `Rate limit exceeded. Try again in ${secs} seconds.`,
      retryAfter: secs
    });
  }
};

export const authRateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await authRateLimiter.consume(req.ip || 'unknown');
    next();
  } catch (rejRes: any) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      success: false,
      error: 'Too many authentication attempts',
      message: `Authentication rate limit exceeded. Try again in ${secs} seconds.`,
      retryAfter: secs
    });
  }
};

export const uploadRateLimiterMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    await uploadRateLimiter.consume(req.ip || 'unknown');
    next();
  } catch (rejRes: any) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    res.set('Retry-After', String(secs));
    res.status(429).json({
      success: false,
      error: 'Too many upload attempts',
      message: `Upload rate limit exceeded. Try again in ${secs} seconds.`,
      retryAfter: secs
    });
  }
};

// Export default rate limiter
export { rateLimiterMiddleware as rateLimiter };
