import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import { ENV } from './config';
// import rateLimit from 'express-rate-limit';
import { errorHandler } from './middleware/errorHandler';
// import { logger } from './middleware/logger';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import postRoutes from './routes/posts';
import speciesRoutes from './routes/species';
import uploadRoutes from './routes/upload';
import spotsRoutes from './routes/spots';
import followsRoutes from './routes/follows';
import messagesRoutes from './routes/messages';
import searchRoutes from './routes/search';
import techniquesRoutes from './routes/techniques';
import equipmentRoutes from './routes/equipment';
import notificationsRoutes from './routes/notifications';
import reviewsRoutes from './routes/reviews';
import unitsRoutes from './routes/units';
import userPreferencesRoutes from './routes/userPreferences';
import tripsRoutes from './routes/trips';
import weatherRoutes from './routes/weather';


const app: Application = express();

// Security middleware with relaxed settings for development
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" },
  contentSecurityPolicy: false
}));

// CORS configuration - allow all origins for development
app.use(cors({
  origin: '*', // Tüm origin'lere izin ver (development için)
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Ek CORS header'ları ekle
app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
  if (req.method === 'OPTIONS') {
    res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, PATCH");
    return res.status(200).json({});
  }
  next();
});

// Compression middleware
app.use(compression() as any);

// Rate limiting - temporarily disabled
// const limiter = rateLimit({
//   windowMs: 15 * 60 * 1000, // 15 minutes
//   max: 100 // limit each IP to 100 requests per windowMs
// });
// app.use(limiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser() as any); // Cookie parser middleware

// Logging middleware
if (ENV.NODE_ENV !== 'test') {
  app.use(morgan('combined'));
}

// Custom logging middleware for debugging
app.use((req, res, next) => {
  console.log(`📥 ${req.method} ${req.path} from IP: ${req.ip || req.connection.remoteAddress}`);
  console.log(`📋 Headers:`, req.headers);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log(`📋 Body:`, req.body);
  }
  next();
});
// app.use(logger);

// Routes
app.use('/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/posts', postRoutes);
app.use('/api/species', speciesRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/spots', spotsRoutes);
app.use('/api/follows', followsRoutes);
app.use('/api/messages', messagesRoutes);
app.use('/api/search', searchRoutes);
app.use('/api/techniques', techniquesRoutes);
app.use('/api/equipment', equipmentRoutes);
app.use('/api/notifications', notificationsRoutes);
app.use('/api/reviews', reviewsRoutes);
app.use('/api/units', unitsRoutes);
app.use('/api/userPreferences', userPreferencesRoutes);
app.use('/api/trips', tripsRoutes);
app.use('/api/weather', weatherRoutes);


// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    success: true,
    message: 'Fishivo API is running!',
    timestamp: new Date().toISOString(),
    environment: ENV.NODE_ENV,
    version: '1.0.0'
  });
});

// Error handling
app.use(errorHandler);

export default app; 