// Shared TypeScript Types for Fishivo Project (Fishbrain Clone)
// Used by Backend, Web, and Mobile applications

// User Types
export interface Equipment {
  id: string;
  name: string;
  brand?: string;
  type?: string;
  description?: string;
  image_url?: string;
  created_at: string;
}

export interface User {
  id: string;
  email: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  title?: string;
  location?: string;
  provider?: string;
  google_id?: string;
  facebook_id?: string;
  role?: 'user' | 'admin' | 'super_admin';
  created_at: string;
  updated_at?: string;
  equipments?: Equipment[];
  followers_count?: number;
  following_count?: number;
  catches_count?: number;
  is_pro?: boolean;
  pro_until?: string;
  spots_count?: number;
}

export interface AuthSession {
  user: User;
  access_token?: string;
  refresh_token?: string;
  expires_at?: number;
}

// Location Types
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

// Fish and Species Types
export interface Species {
  id: number;
  name: string;
  scientific_name?: string;
  description?: string;
  image_url?: string;
  habitat?: string;
  average_size?: string;
  tips?: string;
  created_at: string;
}

export interface CatchDetails {
  species_id?: number;
  species_name?: string;
  weight?: number;
  length?: number;
  bait_used?: string;
  weather_conditions?: string;
  water_temperature?: number;
  time_of_day?: 'morning' | 'afternoon' | 'evening' | 'night';
  technique?: string;
  equipment_used?: string[];
}

// Post Types (Catches/Feed)
export interface Post {
  id: number;
  content: string;
  user_id: string;
  image_url?: string;
  location?: Location;
  catch_details?: CatchDetails;
  created_at: string;
  updated_at?: string;
  user?: User;
  likes_count?: number;
  comments_count?: number;
  is_liked?: boolean;
}

export interface CreatePostRequest {
  content: string;
  image_url?: string;
  location?: Location;
  catch_details?: CatchDetails;
  weight?: number;
  species?: string;
  caught_at?: string;
}

export interface UpdatePostRequest {
  content?: string;
  image_url?: string;
  location?: Location;
  catch_details?: CatchDetails;
}

// Spot Types
export interface Spot {
  id: number;
  name: string;
  description?: string;
  location: Location;
  spot_type: 'fishing' | 'marina' | 'bait_shop' | 'restaurant';
  user_id: string;
  image_url?: string;
  rating?: number;
  depth?: number;
  facilities?: string[];
  created_at: string;
  user?: User;
}

export interface CreateSpotRequest {
  name: string;
  description?: string;
  location: Location;
  spot_type: 'fishing' | 'marina' | 'bait_shop' | 'restaurant';
  image_url?: string;
  depth?: number;
  facilities?: string[];
}

// Social Types
export interface Like {
  id: number;
  user_id: string;
  post_id: number;
  created_at: string;
}

export interface Comment {
  id: number;
  content: string;
  user_id: string;
  post_id: number;
  created_at: string;
  updated_at?: string;
  user?: User;
}

export interface CreateCommentRequest {
  content: string;
  post_id: number;
}

export interface Follow {
  id: number;
  follower_id: string;
  following_id: string;
  created_at: string;
}

// Message Types
export interface Message {
  id: number;
  content: string;
  sender_id: string;
  receiver_id: string;
  read: boolean;
  created_at: string;
  sender?: User;
  receiver?: User;
}

export interface CreateMessageRequest {
  content: string;
  receiver_id: string;
}

// Notification Types
export interface Notification {
  id: number;
  user_id: string;
  type: 'like' | 'comment' | 'follow' | 'message' | 'mention';
  title: string;
  content: string;
  data?: any;
  read: boolean;
  created_at: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Frontend-compatible pagination (mobile app first!)
export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// Authentication Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  username?: string;
  full_name?: string;
}

export interface AuthResponse {
  success: boolean;
  data?: {
    user: User;
    token?: string;
    session?: AuthSession;
  };
  message?: string;
}

// Search Types
export interface SearchFilters {
  species?: string;
  location?: string;
  date_from?: string;
  date_to?: string;
  weight_min?: number;
  weight_max?: number;
  user_id?: string;
}

export interface SearchRequest {
  query?: string;
  type: 'posts' | 'users' | 'spots' | 'species';
  filters?: SearchFilters;
  page?: number;
  limit?: number;
}
