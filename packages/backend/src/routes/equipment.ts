import { Router } from 'express';
import { DatabaseService } from '../services/supabase';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken, optionalAuth } from '../middleware/auth';
import { ApiResponse } from '../types';

const router = Router();

// GET /api/equipment - Get all equipment (gear database)
router.get('/', asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
  const category = req.query.category as string;
  const brand = req.query.brand as string;

  const result = await DatabaseService.getEquipment(page, limit, { category, brand });

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.items,
      total: result.total,
      page: result.page,
      limit: result.limit,
      hasMore: result.hasMore
    }
  };

  res.json(response);
}));

// GET /api/equipment/search - Search equipment
router.get('/search', asyncHandler(async (req, res) => {
  const query = req.query.q as string;
  if (!query || query.trim().length < 2) {
    throw new AppError('Search query must be at least 2 characters', 400);
  }

  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 50);

  const result = await DatabaseService.searchEquipment(query, page, limit);

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.data,
      total: result.count,
      page,
      limit,
      hasMore: (page * limit) < result.count
    }
  };

  res.json(response);
}));

// GET /api/equipment/categories - Get equipment categories
router.get('/categories', asyncHandler(async (req, res) => {
  const categories = [
    { id: 'rod', name: 'Olta Kamışı', icon: 'fishing-rod' },
    { id: 'reel', name: 'Makine', icon: 'fishing-reel' },
    { id: 'line', name: 'Misina & İp', icon: 'fishing-line' },
    { id: 'lure', name: 'Yapay Yem', icon: 'fishing-lure' },
    { id: 'hook', name: 'İğne & Terminal', icon: 'fishing-hook' },
    { id: 'accessory', name: 'Aksesuarlar', icon: 'fishing-net' },
    { id: 'clothing', name: 'Giyim & Güvenlik', icon: 'fishing-vest' }
  ];

  const response: ApiResponse<any> = {
    success: true,
    data: categories
  };

  res.json(response);
}));

export default router; 