import { Router } from 'express';
import { DatabaseService } from '../services/supabase';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken } from '../middleware/auth';
import { ApiResponse } from '../types';

const router = Router();

// GET /api/notifications - Get user notifications
router.get('/', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 50);

  const notifications = await DatabaseService.getUserNotifications(req.userId!, page, limit);

  const response: ApiResponse<any> = {
    success: true,
    data: notifications
  };

  res.json(response);
}));

// PUT /api/notifications/:id/read - Mark notification as read
router.put('/:id/read', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const notificationId = parseInt(req.params.id);

  if (isNaN(notificationId)) {
    throw new AppError('Invalid notification ID', 400);
  }

  await DatabaseService.markNotificationAsRead(notificationId, req.userId!);

  const response: ApiResponse<any> = {
    success: true,
    message: 'Notification marked as read'
  };

  res.json(response);
}));

// PUT /api/notifications/read-all - Mark all notifications as read
router.put('/read-all', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  await DatabaseService.markAllNotificationsAsRead(req.userId!);

  const response: ApiResponse<any> = {
    success: true,
    message: 'All notifications marked as read'
  };

  res.json(response);
}));

export default router; 