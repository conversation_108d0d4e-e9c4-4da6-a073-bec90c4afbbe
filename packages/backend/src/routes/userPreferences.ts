import { Router } from 'express';
import { asyncHand<PERSON>, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken } from '../middleware/auth';
import { ApiResponse } from '../types';
import { UserPreferencesService } from '../services/userPreferences';
import { UserUnits } from '../types/units';
import Joi from 'joi';

const router = Router();

// Validation schema for unit preferences
const unitPreferencesSchema = Joi.object({
  weight: Joi.string().valid('kg', 'lbs', 'g', 'oz').optional(),
  length: Joi.string().valid('cm', 'inch', 'm', 'ft').optional(),
  distance: Joi.string().valid('km', 'miles', 'm', 'nm').optional(),
  temperature: Joi.string().valid('celsius', 'fahrenheit').optional(),
  depth: Joi.string().valid('meters', 'feet', 'fathoms').optional(),
  speed: Joi.string().valid('kmh', 'mph', 'knots', 'ms').optional(),
  pressure: Joi.string().valid('hpa', 'inhg', 'mbar', 'mmhg').optional(),
});

// GET /api/user-preferences - Get user's unit preferences
router.get('/', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const userUnits = await UserPreferencesService.getUserPreferences(req.userId!);

  const response: ApiResponse<UserUnits> = {
    success: true,
    data: userUnits
  };

  res.json(response);
}));

// PUT /api/user-preferences - Update user's unit preferences
router.put('/', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const { error, value } = unitPreferencesSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const updatedUnits = await UserPreferencesService.updateUserPreferences(
    req.userId!,
    value
  );

  const response: ApiResponse<UserUnits> = {
    success: true,
    message: 'Unit preferences updated successfully',
    data: updatedUnits
  };

  res.json(response);
}));

// POST /api/user-preferences/initialize - Initialize user preferences with regional defaults
router.post('/initialize', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const region = req.body.region || UserPreferencesService.detectRegionFromRequest(req);
  
  const userUnits = await UserPreferencesService.initializeUserPreferences(
    req.userId!,
    region
  );

  const response: ApiResponse<UserUnits> = {
    success: true,
    message: 'Unit preferences initialized successfully',
    data: userUnits
  };

  res.status(201).json(response);
}));

// GET /api/user-preferences/regional-defaults/:region - Get regional defaults
router.get('/regional-defaults/:region', asyncHandler(async (req, res) => {
  const { region } = req.params;
  
  const regionalDefaults = UserPreferencesService.getRegionalDefaults(region);

  const response: ApiResponse<UserUnits> = {
    success: true,
    data: regionalDefaults
  };

  res.json(response);
}));

export default router; 