import express, { Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import { DatabaseService } from '../services/supabase';
import { authenticateSupabaseToken } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/AppError';
import { ApiResponse } from '../types';

const router = express.Router();

// Validation schemas
const sendMessageSchema = Joi.object({
  receiver_id: Joi.string().uuid().required(),
  content: Joi.string().min(1).max(1000).required()
});

const getMessagesSchema = Joi.object({
  other_user_id: Joi.string().uuid().required(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(20)
});

// POST /api/messages - Send a message
router.post('/', authenticate<PERSON>upaba<PERSON>Token, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = sendMessageSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { receiver_id, content } = value;
  const sender_id = (req as any).user.id;

  if (sender_id === receiver_id) {
    throw new AppError('You cannot send a message to yourself', 400);
  }

  const messageData = {
    sender_id,
    receiver_id,
    content
  };

  const message = await DatabaseService.sendMessage(messageData);

  const response: ApiResponse = {
    success: true,
    data: message,
    message: 'Message sent successfully'
  };

  res.status(201).json(response);
}));

// GET /api/messages/:otherUserId - Get messages between current user and another user
router.get('/:otherUserId', authenticateSupabaseToken, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = getMessagesSchema.validate({
    other_user_id: req.params['otherUserId'],
    ...req.query
  });
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { other_user_id, page, limit } = value;
  const user_id = (req as any).user.id;

  const result = await DatabaseService.getMessages(user_id, other_user_id, page, limit);

  const response: ApiResponse = {
    success: true,
    data: {
      messages: result.data,
      pagination: {
        page,
        limit,
        total: result.count || 0,
        totalPages: Math.ceil((result.count || 0) / limit),
        hasNext: (page * limit) < (result.count || 0),
        hasPrev: page > 1
      }
    }
  };

  res.json(response);
}));

// PUT /api/messages/:messageId/read - Mark message as read
router.put('/:messageId/read', authenticateSupabaseToken, asyncHandler(async (req: Request, res: Response) => {
  const messageId = parseInt(req.params['messageId']);
  if (isNaN(messageId)) {
    throw new AppError('Invalid message ID', 400);
  }

  await DatabaseService.markMessageAsRead(messageId);

  const response: ApiResponse = {
    success: true,
    message: 'Message marked as read'
  };

  res.json(response);
}));

export default router; 