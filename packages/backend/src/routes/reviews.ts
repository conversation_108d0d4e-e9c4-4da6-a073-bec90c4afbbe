import express from 'express';
import Joi from 'joi';
import { DatabaseService } from '../services/supabase';
import { authenticateSupabaseToken, optionalAuth } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/AppError';
import { ApiResponse } from '../types';

const router = express.Router();

// Validation schemas
const createEquipmentReviewSchema = Joi.object({
  equipment_name: Joi.string().min(2).max(100).required(),
  brand: Joi.string().max(50).optional(),
  model: Joi.string().max(50).optional(),
  category: Joi.string().max(50).optional(),
  overall_rating: Joi.number().integer().min(1).max(5).required(),
  durability_rating: Joi.number().integer().min(1).max(5).optional(),
  value_rating: Joi.number().integer().min(1).max(5).optional(),
  performance_rating: Joi.number().integer().min(1).max(5).optional(),
  title: Joi.string().max(100).optional(),
  content: Joi.string().max(2000).optional(),
  pros: Joi.array().items(Joi.string()).optional(),
  cons: Joi.array().items(Joi.string()).optional(),
  usage_duration: Joi.string().max(50).optional(),
  fishing_style: Joi.array().items(Joi.string()).optional(),
  target_species: Joi.array().items(Joi.string()).optional(),
  images: Joi.array().items(Joi.string().uri()).max(5).optional()
});

// GET /api/reviews/equipment - Get equipment reviews
router.get('/equipment', optionalAuth, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);
  const equipment_name = req.query.equipment_name as string;
  const brand = req.query.brand as string;
  const category = req.query.category as string;

  // TODO: Implement getEquipmentReviews method in DatabaseService
  const result = { data: [], count: 0 }; // Placeholder

  const response: ApiResponse = {
    success: true,
    data: {
      reviews: result.data,
      pagination: {
        page,
        limit,
        total: result.count || 0,
        totalPages: Math.ceil((result.count || 0) / limit),
        hasNext: (page * limit) < (result.count || 0),
        hasPrev: page > 1
      }
    }
  };

  res.json(response);
}));

// POST /api/reviews/equipment - Create equipment review
router.post('/equipment', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const { error, value } = createEquipmentReviewSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const reviewData = {
    ...value,
    user_id: req.userId!
  };

  // TODO: Implement createEquipmentReview method in DatabaseService
  const review = { id: 1, ...reviewData }; // Placeholder

  const response: ApiResponse = {
    success: true,
    data: review,
    message: 'Equipment review created successfully'
  };

  res.status(201).json(response);
}));

export default router; 