import { Router } from 'express';
import { DatabaseService, supabase } from '../services/supabase';
import { asyncHand<PERSON>, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken, optionalAuth } from '../middleware/auth';
import { CreatePostRequest, UpdatePostRequest, ApiResponse, PaginatedResponse, Post } from '../types';
import { UnitConversionService } from '../services/unitConversion';
import { UserPreferencesService } from '../services/userPreferences';
import express from 'express';
import { rateLimit } from 'express-rate-limit';

import Joi from 'joi';

const router = Router();

// Cache configuration
const CACHE_TTL = 60 * 5; // 5 minutes
const LIKE_STATUS_TTL = 60 * 30; // 30 minutes

// Create rate limiters for like operations
const likeRateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // 30 likes per minute
  standardHeaders: true,
  legacyHeaders: false,
  message: { error: 'Too many like operations, please try again later' },
});

/**
 * Get cache key for different types of data
 */
const getCacheKey = (type: string, id: string | number) => `posts:${type}:${id}`;

/**
 * Check if user has liked a post
 */
const isPostLiked = async (userId: string, postId: number): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('likes')
      .select('id')
      .eq('user_id', userId)
      .eq('post_id', postId)
      .single();
    
    return !error && !!data;
  } catch (error) {
    console.error('Error checking like status:', error);
    return false;
  }
};

/**
 * Clear like-related caches
 */
const clearLikeCaches = async (userId: string, postId: number) => {
  // Redis kullanımını kaldırıyoruz
  return;
};

// Validation schemas
const createPostSchema = Joi.object({
  content: Joi.string().min(1).max(1000).required(),
  image_url: Joi.string().uri().optional(),
  images: Joi.array().items(Joi.string().uri()).max(5).optional(),
  location: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required(),
    address: Joi.string().optional(),
    city: Joi.string().optional(),
    country: Joi.string().optional()
  }).optional(),
  spot_id: Joi.number().integer().positive().optional(),
  trip_id: Joi.number().integer().positive().optional(),
  catch_details: Joi.object({
    species_id: Joi.number().integer().positive().optional(),
    species_name: Joi.string().optional(),
    weight: Joi.number().positive().optional(),
    length: Joi.number().positive().optional(),
    bait_used: Joi.string().optional(),
    technique: Joi.string().optional(),
    weather_conditions: Joi.string().optional(),
    water_temperature: Joi.number().optional(),
    time_of_day: Joi.string().valid('morning', 'afternoon', 'evening', 'night').optional(),
    equipment_used: Joi.array().items(Joi.string()).optional()
  }).optional()
});

const updatePostSchema = Joi.object({
  content: Joi.string().min(1).max(1000).optional(),
  image_url: Joi.string().uri().optional(),
  images: Joi.array().items(Joi.string().uri()).max(5).optional(),
  location: Joi.object({
    latitude: Joi.number().min(-90).max(90).required(),
    longitude: Joi.number().min(-180).max(180).required(),
    address: Joi.string().optional(),
    city: Joi.string().optional(),
    country: Joi.string().optional()
  }).optional(),
  spot_id: Joi.number().integer().positive().optional(),
  trip_id: Joi.number().integer().positive().optional(),
  catch_details: Joi.object({
    species_id: Joi.number().integer().positive().optional(),
    species_name: Joi.string().optional(),
    weight: Joi.number().positive().optional(),
    length: Joi.number().positive().optional(),
    bait_used: Joi.string().optional(),
    technique: Joi.string().optional(),
    weather_conditions: Joi.string().optional(),
    water_temperature: Joi.number().optional(),
    time_of_day: Joi.string().valid('morning', 'afternoon', 'evening', 'night').optional(),
    equipment_used: Joi.array().items(Joi.string()).optional()
  }).optional()
});

// 🧠 INSTAGRAM TARZI ALGORİTMA - FEED ENDPOİNTİ
// GET /api/posts/feed - Akıllı feed algoritması
router.get('/feed', optionalAuth, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 15, 50); // İlk açılışta 15 post
  const userId = req.userId;

  // 🎯 ALGORİTMA PARAMETRELERİ
  const algorithmWeights = {
    recency: 0.3,        // Yenilik %30
    engagement: 0.25,    // Beğeni/yorum %25  
    following: 0.2,      // Takip edilen kişiler %20
    location: 0.15,      // Yakın lokasyon %15
    fishSize: 0.1        // Büyük balık %10
  };

  let feedQuery = `
    WITH user_follows AS (
      SELECT followed_id 
      FROM follows 
      WHERE follower_id = $1 AND status = 'accepted'
    ),
    post_scores AS (
      SELECT 
        p.*,
        u.full_name,
        u.avatar_url,
        u.username,
        
        -- 📊 SKOR HESAPLAMA
        (
          -- ⏰ Yenilik skoru (24 saat içinde max puan)
          CASE 
            WHEN p.created_at > NOW() - INTERVAL '1 hour' THEN 100
            WHEN p.created_at > NOW() - INTERVAL '6 hours' THEN 80
            WHEN p.created_at > NOW() - INTERVAL '24 hours' THEN 60
            WHEN p.created_at > NOW() - INTERVAL '3 days' THEN 40
            WHEN p.created_at > NOW() - INTERVAL '7 days' THEN 20
            ELSE 10
          END * ${algorithmWeights.recency}
          
          -- 👍 Engagement skoru
          + (p.likes_count * 2 + p.comments_count * 3) * ${algorithmWeights.engagement}
          
          -- 👥 Takip skoru
          + CASE 
            WHEN uf.followed_id IS NOT NULL THEN 100 
            ELSE 0 
          END * ${algorithmWeights.following}
          
          -- 📍 Lokasyon skoru (aynı şehir/ülke)
          + CASE 
            WHEN p.location IS NOT NULL THEN 50
            ELSE 0
          END * ${algorithmWeights.location}
          
          -- 🐟 Balık boyutu skoru
          + CASE 
            WHEN (p.catch_details->>'weight')::float > 5 THEN 100
            WHEN (p.catch_details->>'weight')::float > 2 THEN 70
            WHEN (p.catch_details->>'weight')::float > 1 THEN 40
            ELSE 20
          END * ${algorithmWeights.fishSize}
          
        ) as algorithm_score,
        
        -- 🏆 Özel etiketler
        CASE 
          WHEN (p.catch_details->>'weight')::float > 5 THEN 'big_catch'
          WHEN p.likes_count > 50 THEN 'viral'
          WHEN p.created_at > NOW() - INTERVAL '1 hour' THEN 'fresh'
          ELSE null
        END as special_tag
        
      FROM posts p
      JOIN users u ON p.user_id = u.id
      LEFT JOIN user_follows uf ON p.user_id = uf.followed_id
      WHERE p.status = 'active'
      AND p.created_at > NOW() - INTERVAL '30 days' -- Son 30 gün
    )
    SELECT *
    FROM post_scores
    ORDER BY 
      -- 🎯 Ana algoritma: Skor + randomizasyon
      algorithm_score DESC,
      RANDOM() * 0.1 -- Küçük randomizasyon ekle
    LIMIT $2 OFFSET $3
  `;

  // Basit algoritma: Yeni + popüler postları karıştır
  const result = await DatabaseService.getPosts(page, limit, {});
  
  // Postları algoritma skoruna göre sırala
  const scoredPosts = result.items.map((post: any) => {
    let score = 0;
    
    // Yenilik skoru
    const hoursAgo = (Date.now() - new Date(post.created_at).getTime()) / (1000 * 60 * 60);
    if (hoursAgo < 1) score += 100;
    else if (hoursAgo < 6) score += 80;
    else if (hoursAgo < 24) score += 60;
    else score += 20;
    
    // Engagement skoru
    score += (post.likes_count * 2 + post.comments_count * 3);
    
    // Balık boyutu skoru
    const weight = post.catch_details?.weight || 0;
    if (weight > 5) score += 50;
    else if (weight > 2) score += 30;
    else if (weight > 1) score += 15;
    
    return { ...post, algorithm_score: score };
  });
  
  // Skora göre sırala
  scoredPosts.sort((a: any, b: any) => b.algorithm_score - a.algorithm_score);

  // 📊 Feed istatistikleri
  const feedStats = {
    totalPosts: scoredPosts.length,
    freshPosts: scoredPosts.filter((p: any) => {
      const hoursAgo = (Date.now() - new Date(p.created_at).getTime()) / (1000 * 60 * 60);
      return hoursAgo < 1;
    }).length,
    bigCatches: scoredPosts.filter((p: any) => (p.catch_details?.weight || 0) > 5).length,
    viralPosts: scoredPosts.filter((p: any) => p.likes_count > 10).length,
    followingPosts: 0 // TODO: Takip sistemi eklenince
  };

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: scoredPosts,
      total: scoredPosts.length,
      page,
      limit,
      hasMore: false, // Feed için always false
      algorithm: 'smart_feed',
      stats: feedStats
    }
  };

  res.json(response);
}));

// GET /api/posts - Get all posts with pagination and enhanced relationships
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);
  const userId = req.query.user_id as string;
  const spotId = req.query.spot_id as string;
  const tripId = req.query.trip_id as string;

  const result = await DatabaseService.getPosts(page, limit, {
    userId,
    spotId: spotId ? parseInt(spotId) : undefined,
    tripId: tripId ? parseInt(tripId) : undefined
  });

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.items,
      total: result.total,
      page: result.page,
      limit: result.limit,
      hasMore: result.hasMore
    }
  };

  res.json(response);
}));

// GET /api/posts/:id - Get single post with full relationships
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);

  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400, 'INVALID_POST_ID');
  }

  const post = await DatabaseService.getPostById(postId);

  if (!post) {
    throw new AppError('Post not found', 404, 'POST_NOT_FOUND');
  }

  // Get spot details if post has spot_id
  if (post.spot_id) {
    const spot = await DatabaseService.getSpotById(post.spot_id);
    post.spot = spot;
  }

  // Get trip details if post has trip_id
  if (post.trip_id) {
    const trip = await DatabaseService.getTripById(post.trip_id);
    post.trip = trip;
  }

  // Convert units to user preferences if user is authenticated
  if (req.userId && post.catch_details) {
    const userUnits = await UserPreferencesService.getUserPreferences(req.userId);
    post.catch_details = UnitConversionService.convertCatchFromBaseUnits(
      post.catch_details,
      userUnits
    );
  }

  const response: ApiResponse<Post> = {
    success: true,
    data: post
  };

  res.json(response);
}));

// POST /api/posts - Create new post with enhanced relationships
router.post('/', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const { error, value } = createPostSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  // Validate spot_id if provided
  if (value.spot_id) {
    const spot = await DatabaseService.getSpotById(value.spot_id);
    if (!spot) {
      throw new AppError('Spot not found', 404, 'SPOT_NOT_FOUND');
    }
    if (spot.status !== 'approved') {
      throw new AppError('Spot is not approved', 400, 'SPOT_NOT_APPROVED');
    }
  }

  // Validate trip_id if provided
  if (value.trip_id) {
    const trip = await DatabaseService.getTripById(value.trip_id);
    if (!trip) {
      throw new AppError('Trip not found', 404, 'TRIP_NOT_FOUND');
    }
    // Check if user is participant of the trip
    const isParticipant = await DatabaseService.isTripParticipant(value.trip_id, req.userId!);
    if (!isParticipant) {
      throw new AppError('Not authorized to post to this trip', 403, 'NOT_TRIP_PARTICIPANT');
    }
  }

  // Get user's unit preferences
  const userUnits = await UserPreferencesService.getUserPreferences(req.userId!);
  
  // Convert catch details from user units to base units for database storage
  let convertedCatchDetails = value.catch_details;
  if (value.catch_details) {
    convertedCatchDetails = UnitConversionService.convertCatchToBaseUnits(
      value.catch_details, 
      userUnits
    );
  }

  const postData: CreatePostRequest & { user_id: string } = {
    ...value,
    catch_details: convertedCatchDetails,
    user_id: req.userId!,
    created_at: new Date().toISOString()
  };

  const post = await DatabaseService.createPost(postData);

  // Convert response data back to user units for frontend
  let responsePost = post;
  if (post.catch_details) {
    responsePost = {
      ...post,
      catch_details: UnitConversionService.convertCatchFromBaseUnits(
        post.catch_details,
        userUnits
      )
    };
  }

  // Create notification for spot owner if posting to a spot
  if (value.spot_id) {
    const spot = await DatabaseService.getSpotById(value.spot_id);
    if (spot && spot.user_id !== req.userId) {
      await DatabaseService.createNotification({
        user_id: spot.user_id,
        type: 'spot_catch',
        title: 'Yeni Av Paylaşımı',
        content: `${post.users?.full_name || 'Bir kullanıcı'} spotunuzda av paylaştı!`,
        data: { post_id: post.id, spot_id: value.spot_id },
        action_url: `/posts/${post.id}`
      });
    }
  }

  const response: ApiResponse<Post> = {
    success: true,
    message: 'Post created successfully',
    data: responsePost
  };

  res.status(201).json(response);
}));

// PUT /api/posts/:id - Update post
router.put('/:id', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);

  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400, 'INVALID_POST_ID');
  }

  const { error, value } = updatePostSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  // Check if post exists and belongs to user
  const existingPost = await DatabaseService.getPostById(postId);
  if (!existingPost) {
    throw new AppError('Post not found', 404, 'POST_NOT_FOUND');
  }

  if (existingPost.user_id !== req.userId) {
    throw new AppError('Not authorized to update this post', 403, 'NOT_AUTHORIZED');
  }

  // Validate spot_id if provided
  if (value.spot_id) {
    const spot = await DatabaseService.getSpotById(value.spot_id);
    if (!spot) {
      throw new AppError('Spot not found', 404, 'SPOT_NOT_FOUND');
    }
    if (spot.status !== 'approved') {
      throw new AppError('Spot is not approved', 400, 'SPOT_NOT_APPROVED');
    }
  }

  // Get user's unit preferences and convert catch details
  let convertedCatchDetails = value.catch_details;
  if (value.catch_details) {
    const userUnits = await UserPreferencesService.getUserPreferences(req.userId!);
    convertedCatchDetails = UnitConversionService.convertCatchToBaseUnits(
      value.catch_details, 
      userUnits
    );
  }

  const updateData = {
    ...value,
    catch_details: convertedCatchDetails,
    updated_at: new Date().toISOString()
  };

  const updatedPost = await DatabaseService.updatePost(postId, updateData);

  const response: ApiResponse<Post> = {
    success: true,
    message: 'Post updated successfully',
    data: updatedPost
  };

  res.json(response);
}));

// DELETE /api/posts/:id - Delete post
router.delete('/:id', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);

  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400, 'INVALID_POST_ID');
  }

  // Check if post exists and belongs to user
  const existingPost = await DatabaseService.getPostById(postId);
  if (!existingPost) {
    throw new AppError('Post not found', 404, 'POST_NOT_FOUND');
  }

  if (existingPost.user_id !== req.userId) {
    throw new AppError('Not authorized to delete this post', 403, 'NOT_AUTHORIZED');
  }

  await DatabaseService.deletePost(postId);

  const response: ApiResponse<null> = {
    success: true,
    message: 'Post deleted successfully',
    data: null
  };

  res.json(response);
}));

// GET /api/posts/:id/like
// Check if current user has liked a post
router.get('/:id/like', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postId = Number(req.params.id);
  const userId = req.user!.id;
  
  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400);
  }
  
  const liked = await isPostLiked(userId, postId);
  
  const response: ApiResponse<{ liked: boolean; postId: number; userId: string }> = {
    success: true,
    data: {
      liked,
      postId,
      userId
    }
  };
  
  res.json(response);
}));

// POST /api/posts/:id/like
// Like a post
router.post('/:id/like', authenticateSupabaseToken, likeRateLimiter, asyncHandler(async (req, res) => {
  const postId = Number(req.params.id);
  const userId = req.user!.id;
  
  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400);
  }
  
  // Check if already liked to avoid duplicate likes
  const alreadyLiked = await isPostLiked(userId, postId);
  if (alreadyLiked) {
    return res.json({ 
      success: true, 
      already_liked: true, 
      message: 'Post already liked'
    });
  }
  
  // Insert the like
  try {
    const { error } = await supabase
      .from('likes')
      .insert({
        user_id: userId,
        post_id: postId,
        created_at: new Date().toISOString()
      });

    if (error) {
      console.error('Database error liking post:', error);
      throw new AppError('Failed to like post', 500);
    }

    // Likes count will be updated automatically by database trigger
    
    // Clear cache for this post/user
    await clearLikeCaches(userId, postId);
    
    return res.json({
      success: true,
      message: 'Post liked successfully'
    });
  } catch (error) {
    console.error('Error liking post:', error);
    throw new AppError('Failed to like post', 500);
  }
}));

// DELETE /api/posts/:id/like
// Unlike a post
router.delete('/:id/like', authenticateSupabaseToken, likeRateLimiter, asyncHandler(async (req, res) => {
  const postId = Number(req.params.id);
  const userId = req.user!.id;
  
  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400);
  }
  
  // Check if post is liked before attempting to delete
  const liked = await isPostLiked(userId, postId);
  if (!liked) {
    return res.json({ 
      success: true, 
      not_liked: true, 
      message: 'Post was not liked'
    });
  }
  
  // Delete the like
  try {
    const { error } = await supabase
      .from('likes')
      .delete()
      .eq('user_id', userId)
      .eq('post_id', postId);

    if (error) {
      console.error('Database error unliking post:', error);
      throw new AppError('Failed to unlike post', 500);
    }

    // Likes count will be updated automatically by database trigger
    
    // Clear cache
    await clearLikeCaches(userId, postId);
    
    return res.json({
      success: true,
      message: 'Post unliked successfully'
    });
  } catch (error) {
    console.error('Error unliking post:', error);
    throw new AppError('Failed to unlike post', 500);
  }
}));

// GET /api/posts/:id/likers
// Get users who liked a post (with pagination)
router.get('/:id/likers', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postId = Number(req.params.id);
  const page = Number(req.query.page) || 1;
  const limit = Math.min(Number(req.query.limit) || 20, 50); // Max 50 per page
  const offset = (page - 1) * limit;
  const currentUserId = req.user!.id;
  
  if (isNaN(postId)) {
    throw new AppError('Invalid post ID', 400);
  }
  
  try {
    console.log(`🔍 Getting likers for post ${postId}, page ${page}, limit ${limit}, currentUserId: ${currentUserId}`);
    
    // Check if current user liked the post
    const { data: userLike, error: userLikeError } = await supabase
      .from('likes')
      .select('*')
      .eq('user_id', currentUserId)
      .eq('post_id', postId)
      .maybeSingle();
      
    const isLikedByCurrentUser = !!userLike;
    console.log(`❤️ Post ${postId} is liked by current user: ${isLikedByCurrentUser}`);
    
    // Check all likes for this post (DEBUG)
    console.log(`🔍 DEBUG: Checking all likes for post ${postId}`);
    const { data: allLikes, error: allLikesError } = await supabase
      .from('likes')
      .select('*')
      .eq('post_id', postId);
      
    console.log(`📊 DEBUG: All likes for post ${postId}:`, JSON.stringify(allLikes || []));
    
    if (allLikesError) {
      console.error('❌ DEBUG: Error getting all likes:', allLikesError);
    }
    
    // Get total likes count
    const { count: totalLikes, error: countError } = await supabase
      .from('likes')
      .select('*', { count: 'exact', head: true })
      .eq('post_id', postId);
      
    if (countError) {
      console.error('Error getting likes count:', countError);
      throw new AppError('Failed to get likes count', 500);
    }
    
    console.log(`📊 Total likes for post ${postId}: ${totalLikes}`);
    
    // If no likes, return empty list
    if (totalLikes === 0) {
      console.log(`⚠️ No likes found for post ${postId}`);
      return res.json({
        items: [],
        page,
        limit,
        total: 0,
        hasMore: false,
        currentUserLiked: isLikedByCurrentUser
      });
    }
    
    // Get likers with user data using a proper JOIN
    console.log(`🔍 DEBUG: Executing Supabase query for likers with JOIN`);
    const { data: likersData, error: likersError } = await supabase
      .from('likes')
      .select(`
        id,
        user_id,
        created_at,
        users:user_id (
          id, 
          username, 
          full_name, 
          avatar_url, 
          is_pro
        )
      `)
      .eq('post_id', postId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (likersError) {
      console.error('❌ Error getting likes data:', likersError);
      throw new AppError('Failed to get likes data', 500);
    }
    
    console.log(`📊 Got ${likersData?.length || 0} likes with user data`);
    console.log('🔍 DEBUG: Raw likersData:', JSON.stringify(likersData || []));
    
    // Format user data
    const items = (likersData || []).map(like => {
      // Correct type definition for TypeScript
      interface UserData {
        id?: string;
        username?: string;
        full_name?: string | null;
        avatar_url?: string | null;
        is_pro?: boolean;
      }
      
      // Extract user data from the nested users object
      const user = like.users as UserData;
      
      if (!user) {
        console.warn(`⚠️ No user data found for like with user_id: ${like.user_id}`);
      }
      
      return {
        id: like.user_id,
        username: user?.username || 'Kullanıcı',
        full_name: user?.full_name || null,
        avatar_url: user?.avatar_url || null,
        is_pro: user?.is_pro || false,
        liked_at: like.created_at
      };
    });
    
    // Check if current user is in the list
    let currentUserInList = items.some(item => item.id === currentUserId);
    console.log(`🔍 DEBUG: Current user in list: ${currentUserInList}`);
    
    // If user liked the post but not in the list (due to pagination)
    // and we're on the first page, add the user to the list
    if (isLikedByCurrentUser && !currentUserInList && page === 1) {
      console.log(`⚠️ Current user liked the post but not in the list, adding manually`);
      
      // Get user data
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, username, full_name, avatar_url, is_pro')
        .eq('id', currentUserId)
        .single();
        
      if (!userError && userData) {
        console.log(`✅ DEBUG: Got user data for manual addition:`, JSON.stringify(userData));
        // Get like timestamp
        const likedAt = userLike?.created_at || new Date().toISOString();
        
        // Add user to the beginning of the list
        items.unshift({
          id: userData.id,
          username: userData.username || 'Kullanıcı',
          full_name: userData.full_name || null,
          avatar_url: userData.avatar_url || null,
          is_pro: userData.is_pro || false,
          liked_at: likedAt
        });
        
        currentUserInList = true;
      } else {
        console.error(`❌ DEBUG: Error getting user data for manual addition:`, userError);
      }
    }
    
    // Create response
    const responseData = {
      items,
      page,
      limit,
      total: totalLikes || 0,
      hasMore: (offset + limit) < (totalLikes || 0),
      currentUserLiked: isLikedByCurrentUser,
      currentUserInList
    };
    
    console.log(`✅ Post ${postId} likers response:`, {
      itemsCount: responseData.items.length,
      total: responseData.total,
      currentUserLiked: responseData.currentUserLiked,
      currentUserInList: responseData.currentUserInList
    });
    console.log(`🔍 DEBUG: Full response:`, JSON.stringify(responseData));
    
    const response: ApiResponse<typeof responseData> = {
      success: true,
      data: responseData
    };

    res.json(response);
  } catch (error) {
    console.error('❌ Error getting post likers:', error);
    throw new AppError('Failed to get post likers', 500);
  }
}));

// GET /api/posts/likes/user
// Get posts liked by current user
router.get('/likes/user', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const page = Number(req.query.page) || 1;
  const limit = Math.min(Number(req.query.limit) || 20, 50); // Max 50 per page
  const offset = (page - 1) * limit;
  
  // Try to get from materialized view for better performance
  try {
    // Dummy data
    const data: any[] = [];
    const count = 0;
    
    // Transform data for client consumption
    const response = {
      items: data.map(like => ({
        ...like?.posts,
        liked_at: like?.created_at
      })),
      page,
      limit,
      total: count || 0,
      hasMore: (offset + limit) < (count || 0)
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error getting user liked posts:', error);
    throw new AppError('Failed to get liked posts', 500);
  }
}));

// POST /api/posts/likes/batch-status
// Get like status for multiple posts at once (batch operation)
router.post('/likes/batch-status', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const postIds = req.body.postIds;
  
  if (!Array.isArray(postIds) || postIds.length === 0) {
    throw new AppError('Invalid or empty post IDs array', 400);
  }
  
  // Limit batch size
  const limitedPostIds = postIds.slice(0, 100);
  
  try {
    // Get all likes for the user for the given posts
    const { data, error } = await supabase
      .from('likes')
      .select('post_id')
      .eq('user_id', userId)
      .in('post_id', limitedPostIds);
    
    if (error) {
      console.error('Database error getting batch like status:', error);
      throw new AppError('Failed to get like statuses', 500);
    }
    
    // Create a map of post ID to liked status
    const likedMap: Record<number, boolean> = {};
    
    // Initialize all requested posts to false (not liked)
    limitedPostIds.forEach(id => {
      likedMap[Number(id)] = false;
    });
    
    // Set liked posts to true
    if (data) {
      data.forEach(like => {
        likedMap[like.post_id] = true;
      });
    }
    
    const response: ApiResponse<{ userId: string; likes: Record<number, boolean> }> = {
      success: true,
      data: {
        userId,
        likes: likedMap
      }
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error getting batch like status:', error);
    throw new AppError('Failed to get like statuses', 500);
  }
}));

// POST /api/posts/likes/counts
// Get like counts for multiple posts at once (batch operation)
router.post('/likes/counts', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const postIds = req.body.postIds;
  
  if (!Array.isArray(postIds) || postIds.length === 0) {
    throw new AppError('Invalid or empty post IDs array', 400);
  }
  
  // Limit batch size
  const limitedPostIds = postIds.slice(0, 100);
  
  try {
    // Get like counts for all posts
    const { data, error } = await supabase
      .from('likes')
      .select('post_id')
      .in('post_id', limitedPostIds);
    
    if (error) {
      console.error('Database error getting batch like counts:', error);
      throw new AppError('Failed to get like counts', 500);
    }
    
    // Count likes for each post
    const countMap: Record<number, number> = {};
    
    // Initialize all requested posts to 0
    limitedPostIds.forEach(id => {
      countMap[Number(id)] = 0;
    });
    
    // Count likes for each post
    if (data) {
      data.forEach(like => {
        const postId = like.post_id;
        countMap[postId] = (countMap[postId] || 0) + 1;
      });
    }
    
    const response: ApiResponse<{ counts: Record<number, number> }> = {
      success: true,
      data: {
        counts: countMap
      }
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error getting batch like counts:', error);
    throw new AppError('Failed to get like counts', 500);
  }
}));

// DEBUG: Get all likes from database (no auth required for debugging)
router.get('/debug/likes', asyncHandler(async (req, res) => {
  try {
    // Get all likes
    const { data: likesData, error: likesError } = await supabase
      .from('likes')
      .select('*')
      .order('created_at', { ascending: false });

    if (likesError) {
      console.error('Database error getting all likes:', likesError);
      throw new AppError('Failed to get likes', 500);
    }

    // Get all posts
    const { data: postsData, error: postsError } = await supabase
      .from('posts')
      .select('id, title')
      .order('id');

    if (postsError) {
      console.error('Database error getting posts:', postsError);
    }

    const response = {
      total_likes: likesData?.length || 0,
      likes: likesData || [],
      posts: postsData || []
    };

    console.log('🔍 DEBUG - All likes:', JSON.stringify(response, null, 2));
    res.json(response);
  } catch (error) {
    console.error('Error in debug likes endpoint:', error);
    throw new AppError('Failed to debug likes', 500);
  }
}));

// DEBUG: Fix like counts in posts table
router.post('/debug/fix-likes-count', asyncHandler(async (req, res) => {
  try {
    console.log('🔧 DEBUG: Fixing like counts...');
    
    // Get all posts
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('id');
    
    if (postsError) {
      console.error('Database error getting posts:', postsError);
      throw new AppError('Failed to get posts', 500);
    }
    
    let fixedCount = 0;
    
    // Update each post's like count
    for (const post of posts || []) {
      // Count likes for this post
      const { count: likeCount, error: countError } = await supabase
        .from('likes')
        .select('*', { count: 'exact', head: true })
        .eq('post_id', post.id);
      
      if (countError) {
        console.error(`Error counting likes for post ${post.id}:`, countError);
        continue;
      }
      
      // Update post's like count
      const { error: updateError } = await supabase
        .from('posts')
        .update({ likes_count: likeCount || 0 })
        .eq('id', post.id);
      
      if (updateError) {
        console.error(`Error updating like count for post ${post.id}:`, updateError);
      } else {
        fixedCount++;
        console.log(`✅ Fixed like count for post ${post.id}: ${likeCount || 0}`);
      }
    }
    
    const response = {
      success: true,
      message: `Fixed like counts for ${fixedCount} posts`,
      fixed_count: fixedCount
    };
    
    console.log('🔧 DEBUG: Like counts fixed successfully');
    res.json(response);
  } catch (error) {
    console.error('Error fixing like counts:', error);
    throw new AppError('Failed to fix like counts', 500);
  }
}));

export default router;
