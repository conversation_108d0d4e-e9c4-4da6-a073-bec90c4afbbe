import express from 'express';
import Joi from 'joi';
import { DatabaseService } from '../services/supabase';
import { authenticateSupabaseToken, optionalAuth } from '../middleware/auth';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/AppError';
import { ApiResponse } from '../types';

const router = express.Router();

// Validation schemas
const createTripSchema = Joi.object({
  title: Joi.string().min(3).max(100).required(),
  description: Joi.string().max(500).optional(),
  start_date: Joi.date().iso().required(),
  end_date: Joi.date().iso().min(Joi.ref('start_date')).optional(),
  spot_id: Joi.number().integer().positive().optional(),
  max_participants: Joi.number().integer().min(1).max(20).optional(),
  is_public: Joi.boolean().default(true),
  planned_equipment: Joi.array().items(Joi.string()).optional(),
  target_species: Joi.array().items(Joi.string()).optional()
});

// GET /api/trips - Get all trips
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);
  const status = req.query.status as string;
  const userId = req.query.user_id as string;

  // TODO: Implement getTrips method in DatabaseService
  const result = { data: [], count: 0 }; // Placeholder

  const response: ApiResponse = {
    success: true,
    data: {
      trips: result.data,
      pagination: {
        page,
        limit,
        total: result.count || 0,
        totalPages: Math.ceil((result.count || 0) / limit),
        hasNext: (page * limit) < (result.count || 0),
        hasPrev: page > 1
      }
    }
  };

  res.json(response);
}));

// GET /api/trips/:id - Get single trip
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const tripId = parseInt(req.params.id);
  if (isNaN(tripId)) {
    throw new AppError('Invalid trip ID', 400);
  }

  const trip = await DatabaseService.getTripById(tripId);
  if (!trip) {
    throw new AppError('Trip not found', 404);
  }

  const response: ApiResponse = {
    success: true,
    data: trip
  };

  res.json(response);
}));

// POST /api/trips - Create new trip
router.post('/', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const { error, value } = createTripSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const tripData = {
    ...value,
    user_id: req.userId!,
    current_participants: 1
  };

  const trip = await DatabaseService.createTrip(tripData);

  const response: ApiResponse = {
    success: true,
    data: trip,
    message: 'Trip created successfully'
  };

  res.status(201).json(response);
}));

export default router; 