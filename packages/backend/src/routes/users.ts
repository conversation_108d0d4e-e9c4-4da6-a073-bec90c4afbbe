import { Router } from 'express';
import { DatabaseService } from '../services/supabase';
import { async<PERSON><PERSON><PERSON>, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken } from '../middleware/auth';
import { ApiResponse, User } from '../types';
import Joi from 'joi';
import { supabase } from '../services/supabase';

const router = Router();

// Validation schema for user updates
const updateUserSchema = Joi.object({
  username: Joi.string().pattern(/^[a-zA-Z0-9_-]+$/).min(3).max(30).required().disallow('', null, ' '),
  full_name: Joi.string().min(2).max(100).optional().allow(null, ''),
  avatar_url: Joi.string().uri().optional().allow(null, ''),
  bio: Joi.string().max(250).optional().allow('', null),
  title: Joi.string().max(100).optional().allow('', null),
  location: Joi.string().max(100).optional().allow('', null),
  website: Joi.string().max(255).optional().allow('', null),
  instagram_url: Joi.string().max(255).optional().allow('', null),
  facebook_url: Joi.string().max(255).optional().allow('', null),
  youtube_url: Joi.string().max(255).optional().allow('', null),
  twitter_url: Joi.string().max(255).optional().allow('', null),
  equipments: Joi.array().items(
    Joi.object({
      id: Joi.string().required(),
      name: Joi.string().required(),
      brand: Joi.string().optional(),
      type: Joi.string().optional(),
      description: Joi.string().optional()
    })
  ).optional().allow(null)
});

// GET /api/users/me - Get current user profile
router.get('/me', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const response: ApiResponse<User> = {
    success: true,
    data: req.user!
  };

  res.json(response);
}));

// PUT /api/users/me - Update current user profile
router.put('/me', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  try {
    console.log('🔄 Profile update request received:', JSON.stringify(req.body));
    
    const { error, value } = updateUserSchema.validate(req.body, { 
      stripUnknown: true,
      abortEarly: false
    });
    
    if (error) {
      console.error('❌ Profile validation error:', error.message);
      throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
    }
    
    // Prepare user data with empty strings for optional fields if they're missing
    const userData = {
      ...value,
      bio: value.bio !== undefined ? value.bio : '',
      title: value.title !== undefined ? value.title : '',
      location: value.location !== undefined ? value.location : ''
    };

    console.log('✅ Validated user data:', JSON.stringify(userData));

    const updatedUser = await DatabaseService.updateUser(req.userId!, userData);

    if (!updatedUser) {
      console.error('❌ User update failed - no data returned from database');
      throw new AppError('User update failed', 500, 'DATABASE_ERROR');
    }

    console.log('✅ User profile updated successfully');

    const response: ApiResponse<User> = {
      success: true,
      data: updatedUser,
      message: 'Profile updated successfully'
    };

    res.json(response);
  } catch (err) {
    console.error('❌ Profile update error:', err);
    console.error('❌ Error details:', {
      message: err.message,
      stack: err.stack,
      code: err.code,
      details: err.details
    });
    
    // Eğer zaten AppError ise direkt fırlat
    if (err instanceof AppError) {
      throw err;
    }
    
    // DatabaseService'den gelen özel hata mesajlarını yakala
    if (err.message && err.message.includes('kullanıcı adı zaten kullanılıyor')) {
      throw new AppError(err.message, 400, 'UNIQUE_CONSTRAINT_VIOLATION');
    }
    
    // Supabase unique constraint hatası
    if (err.code === '23505') {
      if (err.message && err.message.includes('username')) {
        throw new AppError('Bu kullanıcı adı zaten kullanılıyor', 400, 'UNIQUE_CONSTRAINT_VIOLATION');
      } else if (err.message && err.message.includes('email')) {
        throw new AppError('Bu e-posta adresi zaten kullanılıyor', 400, 'UNIQUE_CONSTRAINT_VIOLATION');
      }
    }
    
    // Diğer hataları generic mesajla fırlat
    throw new AppError(err.message || 'Database operation failed', 500, 'DATABASE_ERROR');
  }
}));

// PUT /api/users/me/deactivate - Soft deactivate account
router.put('/me/deactivate', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  await DatabaseService.deactivateUser(req.userId!);
  const response: ApiResponse<any> = {
    success: true,
    message: 'Account deactivated'
  };
  res.json(response);
}));

// GET /api/users/:id - Get user profile by ID
router.get('/:id', asyncHandler(async (req, res) => {
  const userId = req.params.id;

  const user = await DatabaseService.getUserById(userId);

  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND');
  }

  // Remove sensitive information for public profile
  const publicUser = {
    id: user.id,
    username: user.username,
    full_name: user.full_name,
    avatar_url: user.avatar_url,
    created_at: user.created_at,
    bio: user.bio,
    location: user.location,
    is_pro: user.is_pro,
    followers_count: user.followers_count,
    following_count: user.following_count,
    catches_count: user.catches_count,
  };

  const response: ApiResponse<Partial<User>> = {
    success: true,
    data: publicUser
  };

  res.json(response);
}));

// GET /api/users/me/equipment - Get current user's equipment
router.get('/me/equipment', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const equipment = await DatabaseService.getUserEquipment(req.userId!);

  const response: ApiResponse<any[]> = {
    success: true,
    data: equipment
  };

  res.json(response);
}));

// POST /api/users/me/equipment - Add equipment to user
router.post('/me/equipment', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const equipmentData = {
    user_id: req.userId!,
    ...req.body
  };

  const equipment = await DatabaseService.addUserEquipment(equipmentData);

  const response: ApiResponse<any> = {
    success: true,
    data: equipment,
    message: 'Equipment added successfully'
  };

  res.status(201).json(response);
}));

// GET /api/users/me/stats - User statistics
router.get('/me/stats', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const userId = req.userId!;

  const [catchesRes, spotsRes, followersRes, followingRes] = await Promise.all([
    supabase
      .from('posts')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', userId),
    supabase
      .from('spots')
      .select('id', { count: 'exact', head: true })
      .eq('user_id', userId),
    supabase
      .from('follows')
      .select('id', { count: 'exact', head: true })
      .eq('following_id', userId),
    supabase
      .from('follows')
      .select('id', { count: 'exact', head: true })
      .eq('follower_id', userId)
  ]);

  const stats = {
    totalCatches: catchesRes.count || 0,
    totalSpots: spotsRes.count || 0,
    followers: followersRes.count || 0,
    following: followingRes.count || 0
  };

  const response: ApiResponse<any> = {
    success: true,
    data: stats
  };

  res.json(response);
}));

// GET /api/users/me/locations - Get current user's locations
router.get('/me/locations', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const locations = await DatabaseService.getUserLocations(req.userId!);

  const response: ApiResponse<any[]> = {
    success: true,
    data: locations
  };

  res.json(response);
}));

// GET /api/users/me/blocked - Get blocked users
router.get('/me/blocked', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);

  const result = await DatabaseService.getBlockedUsers(req.userId!, page, limit);

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.data,
      total: result.count || 0,
      page,
      limit,
      hasMore: (page * limit) < (result.count || 0)
    }
  };

  res.json(response);
}));

// POST /api/users/block/:userId - Block a user
router.post('/block/:userId', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const targetUserId = req.params.userId;

  if (targetUserId === req.userId) {
    throw new AppError('Cannot block yourself', 400, 'INVALID_OPERATION');
  }

  await DatabaseService.blockUser(req.userId!, targetUserId);

  const response: ApiResponse<any> = {
    success: true,
    message: 'User blocked successfully'
  };

  res.json(response);
}));

// DELETE /api/users/block/:userId - Unblock a user
router.delete('/block/:userId', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const targetUserId = req.params.userId;

  await DatabaseService.unblockUser(req.userId!, targetUserId);

  const response: ApiResponse<any> = {
    success: true,
    message: 'User unblocked successfully'
  };

  res.json(response);
}));

// GET /api/users/:id/catches - Get user's catch posts
router.get('/:id/catches', asyncHandler(async (req, res) => {
  const userId = req.params.id;
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

  const result = await DatabaseService.getPosts(page, limit, { userId });

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.items,
      total: result.total,
      page: result.page,
      limit: result.limit,
      hasMore: result.hasMore
    }
  };

  res.json(response);
}));

// GET /api/users/me/catches - Get current user's catch posts
router.get('/me/catches', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

  const result = await DatabaseService.getPosts(page, limit, { userId: req.userId! });

  const response: ApiResponse<any> = {
    success: true,
    data: {
      items: result.items,
      total: result.total,
      page: result.page,
      limit: result.limit,
      hasMore: result.hasMore
    }
  };

  res.json(response);
}));

// Permanently delete endpoint disabled for now

export default router;
