import { Router, Request, Response } from 'express';
import { supabase, DatabaseService } from '../services/supabase';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { authRateLimiterMiddleware } from '../middleware/rateLimiter';
import { LoginRequest, RegisterRequest, ApiResponse, AuthResponse } from '../types';
import Joi from 'joi';
import passport from '../config/passport';
import jwt from 'jsonwebtoken';
import { SECURITY_CONFIG } from '../config';
import { authenticateSupabaseToken } from '../middleware/auth';

const router = Router();

// Validation schemas
const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required()
});

const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  username: Joi.string().pattern(/^[a-zA-Z0-9_-]+$/).min(3).max(30).optional(),
  full_name: Joi.string().min(2).max(100).optional()
});

// POST /api/auth/register
router.post('/register', authRateLimiterMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = registerSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const { email, password, username, full_name }: RegisterRequest = value;

  // Ensure we have a username
  let finalUsername = username;
  if (!finalUsername) {
    finalUsername = await DatabaseService.generateUniqueUsername(full_name || email.split('@')[0]);
  }

  // Register user with Supabase Auth
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        username: finalUsername,
        full_name
      }
    }
  });

  if (authError) {
    throw new AppError(authError.message, 400, 'REGISTRATION_FAILED');
  }

  if (!authData.user) {
    throw new AppError('Registration failed', 400, 'REGISTRATION_FAILED');
  }

  // Create user profile in database (upsert to be safe)
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .upsert({
      id: authData.user.id,
      email: authData.user.email,
      username: finalUsername,
      full_name,
      created_at: new Date().toISOString()
    })
    .select()
    .single();

  if (profileError) {
    console.error('Profile creation error:', profileError);
    // Don't throw error here, user is already created in auth
  }

  const response: ApiResponse<AuthResponse> = {
    success: true,
    message: 'Registration successful',
    data: {
      success: true,
      data: {
        user: userProfile || {
          id: authData.user.id,
          email: authData.user.email!,
          username: finalUsername,
          full_name,
          created_at: new Date().toISOString()
        },
        token: authData.session?.access_token
      },
      message: 'Registration successful'
    }
  };

  res.status(201).json(response);
}));

// POST /api/auth/login
router.post('/login', authRateLimiterMiddleware, asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = loginSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const { email, password }: LoginRequest = value;

  // Sign in with Supabase Auth
  const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
    email,
    password
  });

  if (authError) {
    throw new AppError('Invalid credentials', 401, 'INVALID_CREDENTIALS');
  }

  if (!authData.user || !authData.session) {
    throw new AppError('Login failed', 401, 'LOGIN_FAILED');
  }

  // Get user profile
  const { data: userProfile, error: profileError } = await supabase
    .from('users')
    .select('*')
    .eq('id', authData.user.id)
    .single();

  if (profileError) {
    // Ensure username
    const autoUsername = await DatabaseService.generateUniqueUsername(authData.user.user_metadata?.full_name || authData.user.email?.split('@')[0]);

    // Create profile if it doesn't exist
    const { data: newProfile } = await supabase
      .from('users')
      .upsert({
        id: authData.user.id,
        email: authData.user.email,
        username: autoUsername,
        full_name: authData.user.user_metadata?.full_name || '',
        created_at: new Date().toISOString()
      }, { onConflict: 'id' })
      .select()
      .single();

    const response: ApiResponse<AuthResponse> = {
      success: true,
      message: 'Login successful',
      data: {
        success: true,
        data: {
          user: newProfile || {
            id: authData.user.id,
            email: authData.user.email!,
            username: autoUsername,
            full_name: authData.user.user_metadata?.full_name || '',
            created_at: new Date().toISOString()
          },
          token: authData.session.access_token
        },
        message: 'Login successful'
      }
    };

    return res.json(response);
  }

  const response: ApiResponse<AuthResponse> = {
    success: true,
    message: 'Login successful',
    data: {
      success: true,
      data: {
        user: userProfile,
        token: authData.session.access_token
      },
      message: 'Login successful'
    }
  };

  res.json(response);
}));

// POST /api/auth/logout
router.post('/logout', asyncHandler(async (req: Request, res: Response) => {
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  if (token) {
    await supabase.auth.signOut();
  }

  const response: ApiResponse = {
    success: true,
    message: 'Logout successful'
  };

  res.json(response);
}));

// POST /api/auth/refresh
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refresh_token } = req.body;

  if (!refresh_token) {
    throw new AppError('Refresh token required', 400, 'REFRESH_TOKEN_REQUIRED');
  }

  const { data, error } = await supabase.auth.refreshSession({
    refresh_token
  });

  if (error) {
    throw new AppError('Invalid refresh token', 401, 'INVALID_REFRESH_TOKEN');
  }

  const response: ApiResponse = {
    success: true,
    message: 'Token refreshed successfully',
    data: {
      access_token: data.session?.access_token,
      refresh_token: data.session?.refresh_token,
      expires_at: data.session?.expires_at
    }
  };

  res.json(response);
}));

// Helper function to generate JWT token
const generateJWT = (user: any) => {
  return jwt.sign(
    { userId: user.id, email: user.email },
    process.env['JWT_SECRET'] || 'fallback-secret',
    { expiresIn: '7d' }
  );
};

// Facebook OAuth Routes
router.get('/facebook',
  passport.authenticate('facebook', { scope: ['email'] })
);

router.get('/facebook/callback',
  passport.authenticate('facebook', { session: false }),
  asyncHandler(async (req: Request, res: Response) => {
    const user = req.user as any;

    if (!user) {
      return res.redirect(`${process.env['WEB_URL']}/login?error=oauth_failed`);
    }

    // Generate JWT token
    const token = generateJWT(user);

    // Redirect to frontend with token
    res.redirect(`${process.env['WEB_URL']}/auth/callback?token=${token}&provider=facebook`);
  })
);

// GET /auth/me - Check authentication and get current user
router.get('/me', authenticateSupabaseToken, asyncHandler(async (req: Request, res: Response) => {
  try {
    // req.user is set by authenticateSupabaseToken middleware
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Unauthorized',
        code: 'UNAUTHORIZED'
      });
    }

    // Return user data
    const response: ApiResponse = {
      success: true,
      message: 'User data retrieved successfully',
      data: user
    };

    res.json(response);
  } catch (error) {
    console.error('Error in /auth/me endpoint:', error);
    throw new AppError('Failed to get user data', 500);
  }
}));

export default router;
