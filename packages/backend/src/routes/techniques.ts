import { Router } from 'express';
import { DatabaseService } from '../services/supabase';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { ApiResponse } from '../types';

const router = Router();

// GET /api/techniques - Get all fishing techniques
router.get('/', asyncHandler(async (req, res) => {
  const techniques = await DatabaseService.getFishingTechniques();

  const response: ApiResponse<any[]> = {
    success: true,
    data: techniques
  };

  res.json(response);
}));

// GET /api/techniques/search - Search fishing techniques
router.get('/search', asyncHandler(async (req, res) => {
  const { q } = req.query;
  
  if (!q || typeof q !== 'string') {
    throw new AppError('Arama terimi gerekli', 400);
  }

  const techniques = await DatabaseService.searchFishingTechniques(q);

  const response: ApiResponse<any[]> = {
    success: true,
    data: techniques
  };

  res.json(response);
}));

// GET /api/techniques/:id - Get fishing technique by ID
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;
  
  if (!id || isNaN(Number(id))) {
    throw new AppError('Geçersiz teknik ID', 400);
  }

  const technique = await DatabaseService.getFishingTechniqueById(Number(id));

  const response: ApiResponse<any> = {
    success: true,
    data: technique
  };

  res.json(response);
}));

export default router; 