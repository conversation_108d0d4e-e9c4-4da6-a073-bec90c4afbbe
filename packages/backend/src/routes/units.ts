import { Router } from 'express';
import { asyncHand<PERSON>, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken } from '../middleware/auth';
import { ApiResponse } from '../types';
import { DatabaseUnitService } from '../services/DatabaseUnitService';
import Joi from 'joi';

const router = Router();

// Validation schemas
const conversionSchema = Joi.object({
  value: Joi.number().required(),
  fromUnit: Joi.string().required(),
  toUnit: Joi.string().required()
});

const validationSchema = Joi.object({
  value: Joi.number().required(),
  unit: Joi.string().required(),
  context: Joi.string().optional()
});

// =====================================================
// PUBLIC ENDPOINTS (No auth required)
// =====================================================

// GET /api/units/categories - Get all unit categories
router.get('/categories', asyncHandler(async (req, res) => {
  const categories = await DatabaseUnitService.getCategories();

  const response: ApiResponse<any> = {
    success: true,
    data: categories
  };

  res.json(response);
}));

// GET /api/units/category/:categoryId - Get units for a category
router.get('/category/:categoryId', asyncHandler(async (req, res) => {
  const { categoryId } = req.params;
  const units = await DatabaseUnitService.getUnitsForCategory(categoryId);

  if (units.length === 0) {
    throw new AppError('Category not found or has no units', 404, 'CATEGORY_NOT_FOUND');
  }

  const response: ApiResponse<any> = {
    success: true,
    data: units
  };

  res.json(response);
}));

// POST /api/units/convert - Convert between units
router.post('/convert', asyncHandler(async (req, res) => {
  const { error, value: validatedData } = conversionSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const { value, fromUnit, toUnit } = validatedData;
  
  try {
    const result = await DatabaseUnitService.convertUnit(value, fromUnit, toUnit);

    const response: ApiResponse<any> = {
      success: true,
      data: result
    };

    res.json(response);
  } catch (conversionError) {
    throw new AppError(conversionError.message, 400, 'CONVERSION_ERROR');
  }
}));

// POST /api/units/validate - Validate unit value
router.post('/validate', asyncHandler(async (req, res) => {
  const { error, value: validatedData } = validationSchema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const { value, unit, context } = validatedData;
  
  const result = await DatabaseUnitService.validateValue(value, unit, context);

  const response: ApiResponse<any> = {
    success: true,
    data: result
  };

  res.json(response);
}));

// GET /api/units/regional-defaults/:region - Get regional defaults
router.get('/regional-defaults/:region', asyncHandler(async (req, res) => {
  const { region } = req.params;
  
  // This would be implemented in DatabaseUnitService
  // For now, return basic response
  const response: ApiResponse<any> = {
    success: true,
    data: {
      region,
      message: 'Regional defaults endpoint - to be implemented'
    }
  };

  res.json(response);
}));

// =====================================================
// AUTHENTICATED ENDPOINTS
// =====================================================

// GET /api/units/preferences - Get user's unit preferences
router.get('/preferences', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const preferences = await DatabaseUnitService.getUserPreferences(req.userId!);

  const response: ApiResponse<any> = {
    success: true,
    data: preferences
  };

  res.json(response);
}));

// PUT /api/units/preferences - Update user's unit preferences
router.put('/preferences', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const allowedFields = [
    'weight_unit', 'length_unit', 'distance_unit', 'temperature_unit',
    'depth_unit', 'speed_unit', 'pressure_unit', 'region', 'auto_detect_region'
  ];

  const updates = {};
  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      updates[key] = req.body[key];
    }
  });

  if (Object.keys(updates).length === 0) {
    throw new AppError('No valid fields to update', 400, 'NO_VALID_FIELDS');
  }

  const updatedPreferences = await DatabaseUnitService.updateUserPreferences(
    req.userId!,
    updates
  );

  const response: ApiResponse<any> = {
    success: true,
    data: updatedPreferences
  };

  res.json(response);
}));

// POST /api/units/convert-for-user - Convert value using user's preferences
router.post('/convert-for-user', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  const schema = Joi.object({
    value: Joi.number().required(),
    category: Joi.string().required(),
    direction: Joi.string().valid('to_base', 'from_base').required()
  });

  const { error, value: validatedData } = schema.validate(req.body);
  if (error) {
    throw new AppError(error.details[0].message, 400, 'VALIDATION_ERROR');
  }

  const { value, category, direction } = validatedData;
  const preferences = await DatabaseUnitService.getUserPreferences(req.userId!);
  
  let result;
  if (direction === 'to_base') {
    // Convert from user's preferred unit to base unit (for storage)
    const userUnit = preferences[`${category}_unit`];
    if (!userUnit) {
      throw new AppError(`No preference found for category: ${category}`, 400, 'NO_PREFERENCE');
    }
    const convertedValue = await DatabaseUnitService.convertToBaseUnit(value, userUnit, category);
    result = { value: convertedValue, unit: 'base', category };
  } else {
    // Convert from base unit to user's preferred unit (for display)
    const userUnit = preferences[`${category}_unit`];
    if (!userUnit) {
      throw new AppError(`No preference found for category: ${category}`, 400, 'NO_PREFERENCE');
    }
    result = await DatabaseUnitService.convertFromBaseUnit(value, category, userUnit);
  }

  // Log usage for analytics
  if (direction === 'from_base') {
    await DatabaseUnitService.logUsage(
      req.userId!,
      category,
      'base',
      result.unit,
      result.value
    );
  }

  const response: ApiResponse<any> = {
    success: true,
    data: result
  };

  res.json(response);
}));

// =====================================================
// ADMIN ENDPOINTS
// =====================================================

// GET /api/units/stats - Get system statistics (admin only)
router.get('/stats', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  // Check if user is admin (implement your admin check logic)
  // For now, just return stats
  
  const stats = DatabaseUnitService.getStats();

  const response: ApiResponse<any> = {
    success: true,
    data: stats
  };

  res.json(response);
}));

// POST /api/units/refresh - Refresh cached data (admin only)
router.post('/refresh', authenticateSupabaseToken, asyncHandler(async (req, res) => {
  // Check if user is admin (implement your admin check logic)
  
  await DatabaseUnitService.refresh();

  const response: ApiResponse<any> = {
    success: true,
    message: 'Unit system data refreshed successfully'
  };

  res.json(response);
}));

export default router; 