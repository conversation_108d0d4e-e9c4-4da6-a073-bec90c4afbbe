import { Router } from 'express';
import { DatabaseService } from '../services/supabase';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { ApiResponse, Species } from '../types';

const router = Router();

// GET /api/species - Get all fish species
router.get('/', asyncHandler(async (req, res) => {
  const species = await DatabaseService.getSpecies();

  const response: ApiResponse<Species[]> = {
    success: true,
    data: species
  };

  res.json(response);
}));

// GET /api/species/:id - Get single species
router.get('/:id', asyncHandler(async (req, res) => {
  const speciesId = parseInt(req.params.id);

  if (isNaN(speciesId)) {
    throw new AppError('Invalid species ID', 400, 'INVALID_SPECIES_ID');
  }

  const species = await DatabaseService.getSpeciesById(speciesId);

  if (!species) {
    throw new AppError('Species not found', 404, 'SPECIES_NOT_FOUND');
  }

  const response: ApiResponse<Species> = {
    success: true,
    data: species
  };

  res.json(response);
}));

export default router;
