import { Router } from 'express';
import { DatabaseService } from '../services/supabase';
import { async<PERSON>and<PERSON>, AppError } from '../middleware/errorHandler';
import { authenticateSupabaseToken } from '../middleware/auth';
import { requireAdmin, requireSuperAdmin, ipWhitelist, logAdminAction } from '../middleware/adminAuth';
import { ApiResponse, PaginatedResponse } from '../types';

const router = Router();

// Tüm admin route'larında authentication ve admin kontrolü
router.use(authenticateSupabaseToken);
router.use(ipWhitelist);
router.use(requireAdmin);
router.use(logAdminAction);

// GET /api/admin/stats - Admin dashboard istatistikleri
router.get('/stats', asyncHandler(async (req, res) => {
  const stats = await AdminService.getDashboardStats();
  
  const response: ApiResponse = {
    success: true,
    data: stats
  };

  res.json(response);
}));

// GET /api/admin/users - Kullanıcı listesi
router.get('/users', asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
  const search = req.query.search as string;
  const role = req.query.role as string;
  const status = req.query.status as string;

  const users = await AdminService.getUsers(page, limit, { search, role, status });

  const response: ApiResponse<PaginatedResponse<any>> = {
    success: true,
    data: users
  };

  res.json(response);
}));

// PUT /api/admin/users/:id/ban - Kullanıcı yasakla
router.put('/users/:id/ban', asyncHandler(async (req, res) => {
  const userId = req.params.id;
  const { reason, duration } = req.body;

  await AdminService.banUser(userId, reason, duration);

  const response: ApiResponse = {
    success: true,
    message: 'User banned successfully'
  };

  res.json(response);
}));

// PUT /api/admin/users/:id/unban - Kullanıcı yasağını kaldır
router.put('/users/:id/unban', asyncHandler(async (req, res) => {
  const userId = req.params.id;

  await AdminService.unbanUser(userId);

  const response: ApiResponse = {
    success: true,
    message: 'User unbanned successfully'
  };

  res.json(response);
}));

// PUT /api/admin/users/:id/pro - Pro üyelik ver
router.put('/users/:id/pro', asyncHandler(async (req, res) => {
  const userId = req.params.id;
  const { duration } = req.body; // days

  await AdminService.grantProMembership(userId, duration);

  const response: ApiResponse = {
    success: true,
    message: 'Pro membership granted successfully'
  };

  res.json(response);
}));

// GET /api/admin/posts - Post listesi
router.get('/posts', asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
  const status = req.query.status as string;
  const reported = req.query.reported === 'true';

  const posts = await AdminService.getPosts(page, limit, { status, reported });

  const response: ApiResponse<PaginatedResponse<any>> = {
    success: true,
    data: posts
  };

  res.json(response);
}));

// DELETE /api/admin/posts/:id - Post sil
router.delete('/posts/:id', asyncHandler(async (req, res) => {
  const postId = parseInt(req.params.id);
  const { reason } = req.body;

  await AdminService.deletePost(postId, reason);

  const response: ApiResponse = {
    success: true,
    message: 'Post deleted successfully'
  };

  res.json(response);
}));

// GET /api/admin/reports - Şikayet listesi
router.get('/reports', asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);
  const status = req.query.status as string;

  const reports = await AdminService.getReports(page, limit, { status });

  const response: ApiResponse<PaginatedResponse<any>> = {
    success: true,
    data: reports
  };

  res.json(response);
}));

// PUT /api/admin/reports/:id/resolve - Şikayeti çöz
router.put('/reports/:id/resolve', asyncHandler(async (req, res) => {
  const reportId = parseInt(req.params.id);
  const { action, notes } = req.body;

  await AdminService.resolveReport(reportId, action, notes);

  const response: ApiResponse = {
    success: true,
    message: 'Report resolved successfully'
  };

  res.json(response);
}));

// GET /api/admin/logs - Admin işlem logları (Super admin only)
router.get('/logs', requireSuperAdmin, asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = Math.min(parseInt(req.query.limit as string) || 50, 200);

  const logs = await AdminService.getAdminLogs(page, limit);

  const response: ApiResponse<PaginatedResponse<any>> = {
    success: true,
    data: logs
  };

  res.json(response);
}));

// Admin Service (bu dosyaya eklenecek)
class AdminService {
  static async getDashboardStats() {
    // Dashboard istatistikleri
    return {
      totalUsers: 1247,
      activeUsers: 892,
      totalPosts: 3456,
      pendingReports: 12,
      proUsers: 156,
      newUsersToday: 23,
      postsToday: 67,
      reportsToday: 3
    };
  }

  static async getUsers(page: number, limit: number, filters: any) {
    // Kullanıcı listesi
    return {
      items: [],
      total: 0,
      page,
      limit,
      hasMore: false
    };
  }

  static async banUser(userId: string, reason: string, duration?: number) {
    // Kullanıcı yasakla
  }

  static async unbanUser(userId: string) {
    // Kullanıcı yasağını kaldır
  }

  static async grantProMembership(userId: string, duration: number) {
    // Pro üyelik ver
  }

  static async getPosts(page: number, limit: number, filters: any) {
    // Post listesi
    return {
      items: [],
      total: 0,
      page,
      limit,
      hasMore: false
    };
  }

  static async deletePost(postId: number, reason: string) {
    // Post sil
  }

  static async getReports(page: number, limit: number, filters: any) {
    // Şikayet listesi
    return {
      items: [],
      total: 0,
      page,
      limit,
      hasMore: false
    };
  }

  static async resolveReport(reportId: number, action: string, notes: string) {
    // Şikayeti çöz
  }

  static async getAdminLogs(page: number, limit: number) {
    // Admin işlem logları
    return {
      items: [],
      total: 0,
      page,
      limit,
      hasMore: false
    };
  }
}

export default router;
