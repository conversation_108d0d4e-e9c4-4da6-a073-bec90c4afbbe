import express, { Request, Response } from 'express';
import Joi from 'joi';
import { DatabaseService } from '../services/supabase';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/AppError';
import { ApiResponse } from '../types';

const router = express.Router();

// Validation schema
const searchSchema = Joi.object({
  query: Joi.string().min(1).max(100).required(),
  type: Joi.string().valid('posts', 'users', 'spots', 'species').required(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  // Filters for posts
  species: Joi.string().optional(),
  location: Joi.string().optional(),
  date_from: Joi.date().iso().optional(),
  date_to: Joi.date().iso().optional(),
  weight_min: Joi.number().min(0).optional(),
  weight_max: Joi.number().min(0).optional(),
  user_id: Joi.string().uuid().optional(),
  // Filters for spots
  spot_type: Joi.string().valid('fishing', 'marina', 'bait_shop', 'restaurant').optional()
});

// GET /api/search - Universal search endpoint
router.get('/', asyncHandler(async (req: Request, res: Response) => {
  const { error, value } = searchSchema.validate(req.query);
  if (error) {
    throw new AppError(error.details[0].message, 400);
  }

  const { query, type, page, limit, ...filters } = value;
  let result;

  switch (type) {
    case 'posts':
      result = await DatabaseService.searchPosts(query, filters, page, limit);
      break;
    
    case 'users':
      result = await DatabaseService.searchUsers(query, page, limit);
      break;
    
    case 'spots':
      result = await DatabaseService.searchSpots(query, page, limit);
      break;
    
    case 'species':
      result = await DatabaseService.searchSpecies(query, page, limit);
      break;
    
    default:
      throw new AppError('Invalid search type', 400);
  }

  const response: ApiResponse = {
    success: true,
    data: {
      results: result.data,
      type,
      query,
      pagination: {
        page,
        limit,
        total: result.count || 0,
        totalPages: Math.ceil((result.count || 0) / limit),
        hasNext: (page * limit) < (result.count || 0),
        hasPrev: page > 1
      }
    }
  };

  res.json(response);
}));

export default router; 