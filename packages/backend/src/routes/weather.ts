import { Router } from 'express';
import { asyncHandler, AppError } from '../middleware/errorHandler';
import { ApiResponse } from '../types';
import axios from 'axios';

const router = Router();

// Open-Meteo API base URL
const OPEN_METEO_BASE_URL = 'https://api.open-meteo.com/v1';

// WMO Weather interpretation codes mapping
const WMO_WEATHER_CODES: { [key: number]: { condition: string; icon: string } } = {
  0: { condition: 'Açık', icon: 'sun' },
  1: { condition: 'Az Bulutlu', icon: 'cloud' },
  2: { condition: 'Parçalı Bulutlu', icon: 'cloud' },
  3: { condition: 'Kapalı', icon: 'cloud' },
  45: { condition: 'Sisli', icon: 'cloud' },
  48: { condition: 'Sis<PERSON>', icon: 'cloud' },
  51: { condition: '<PERSON><PERSON><PERSON>', icon: 'cloud-rain' },
  53: { condition: 'Yağmurlu', icon: 'cloud-rain' },
  55: { condition: '<PERSON><PERSON><PERSON><PERSON>', icon: 'cloud-rain' },
  56: { condition: '<PERSON><PERSON><PERSON>', icon: 'cloud-rain' },
  57: { condition: 'Dondurucu Yağmur', icon: 'cloud-rain' },
  61: { condition: 'Hafif Yağmurlu', icon: 'cloud-rain' },
  63: { condition: 'Yağmurlu', icon: 'cloud-rain' },
  65: { condition: 'Şiddetli Yağmurlu', icon: 'cloud-rain' },
  66: { condition: 'Hafif Dondurucu Yağmur', icon: 'cloud-rain' },
  67: { condition: 'Dondurucu Yağmur', icon: 'cloud-rain' },
  71: { condition: 'Hafif Karlı', icon: 'cloud-snow' },
  73: { condition: 'Karlı', icon: 'cloud-snow' },
  75: { condition: 'Şiddetli Karlı', icon: 'cloud-snow' },
  77: { condition: 'Kar Taneleri', icon: 'cloud-snow' },
  80: { condition: 'Hafif Sağanak', icon: 'cloud-rain' },
  81: { condition: 'Sağanak', icon: 'cloud-rain' },
  82: { condition: 'Şiddetli Sağanak', icon: 'cloud-rain' },
  85: { condition: 'Hafif Kar Sağanağı', icon: 'cloud-snow' },
  86: { condition: 'Kar Sağanağı', icon: 'cloud-snow' },
  95: { condition: 'Gök Gürültülü', icon: 'cloud-lightning' },
  96: { condition: 'Gök Gürültülü Dolu', icon: 'cloud-lightning' },
  99: { condition: 'Şiddetli Gök Gürültülü Dolu', icon: 'cloud-lightning' }
};

// Balıkçılık koşulları hesaplama fonksiyonu
const calculateFishingConditions = (weatherData: any) => {
  const temp = weatherData.temperature_2m[0];
  const windSpeed = weatherData.windspeed_10m[0];
  const pressure = 1013; // Default pressure
  const precip = weatherData.precipitation_probability[0];
  const weathercode = weatherData.weathercode;

  let score = 0;
  const factors = [];

  // Sıcaklık faktörü (15-25°C ideal)
  if (temp >= 15 && temp <= 25) {
    score += 1;
    factors.push({ label: 'Sıcaklık', value: 'İdeal', icon: 'thermometer', color: '#22C55E' });
  } else if (temp >= 10 && temp <= 30) {
    score += 0.5;
    factors.push({ label: 'Sıcaklık', value: 'Uygun', icon: 'thermometer', color: '#F59E0B' });
  } else {
    factors.push({ label: 'Sıcaklık', value: 'Uygun Değil', icon: 'thermometer', color: '#EF4444' });
  }

  // Rüzgar faktörü (15 km/h altı ideal)
  if (windSpeed < 15) {
    score += 1;
    factors.push({ label: 'Rüzgar', value: 'Hafif', icon: 'wind', color: '#22C55E' });
  } else if (windSpeed < 25) {
    score += 0.5;
    factors.push({ label: 'Rüzgar', value: 'Orta', icon: 'wind', color: '#F59E0B' });
  } else {
    factors.push({ label: 'Rüzgar', value: 'Güçlü', icon: 'wind', color: '#EF4444' });
  }

  // Basınç faktörü (1010-1025 hPa ideal)
  if (pressure >= 1010 && pressure <= 1025) {
    score += 1;
    factors.push({ label: 'Basınç', value: 'İdeal', icon: 'activity', color: '#22C55E' });
  } else if (pressure >= 1000 && pressure <= 1035) {
    score += 0.5;
    factors.push({ label: 'Basınç', value: 'Uygun', icon: 'activity', color: '#F59E0B' });
  } else {
    factors.push({ label: 'Basınç', value: 'Uygun Değil', icon: 'activity', color: '#EF4444' });
  }

  // Yağış faktörü (%30 altı ideal)
  if (precip <= 30) {
    score += 1;
    factors.push({ label: 'Yağış', value: 'Düşük', icon: 'droplets', color: '#22C55E' });
  } else if (precip <= 60) {
    score += 0.5;
    factors.push({ label: 'Yağış', value: 'Orta', icon: 'droplets', color: '#F59E0B' });
  } else {
    factors.push({ label: 'Yağış', value: 'Yüksek', icon: 'droplets', color: '#EF4444' });
  }

  // Hava durumu kodu faktörü
  const weatherCode = weathercode[0];
  const weatherInfo = WMO_WEATHER_CODES[weatherCode] || { condition: 'Bilinmeyen', icon: 'cloud' };
  
  if ([0, 1, 2].includes(weatherCode)) {
    score += 1;
    factors.push({ label: 'Hava', value: 'Açık', icon: 'sun', color: '#22C55E' });
  } else if ([3, 45, 48].includes(weatherCode)) {
    score += 0.5;
    factors.push({ label: 'Hava', value: 'Bulutlu', icon: 'cloud', color: '#F59E0B' });
  } else {
    factors.push({ label: 'Hava', value: 'Yağışlı', icon: 'cloud-rain', color: '#EF4444' });
  }

  // Skor sınırları (0-4 arası)
  score = Math.max(0, Math.min(4, Math.round(score)));

  return { score, factors };
};

// GET /api/weather/current - Get current weather for coordinates
router.get('/current', asyncHandler(async (req, res) => {
  const lat = parseFloat(req.query.lat as string);
  const lon = parseFloat(req.query.lon as string);

  if (isNaN(lat) || isNaN(lon)) {
    throw new AppError('Valid latitude and longitude required', 400);
  }

  try {
    // Open-Meteo API'den veri çek
    const response = await axios.get(`${OPEN_METEO_BASE_URL}/forecast`, {
      params: {
        latitude: lat,
        longitude: lon,
        current: 'temperature_2m,relative_humidity_2m,apparent_temperature,precipitation,rain,showers,snowfall,weathercode,cloudcover,pressure_msl,windspeed_10m,winddirection_10m,windgusts_10m',
        hourly: 'temperature_2m,relative_humidity_2m,precipitation_probability,weathercode,rain,showers,snowfall,windspeed_10m,winddirection_10m',
        daily: 'weathercode,temperature_2m_max,temperature_2m_min,precipitation_probability_max,precipitation_sum,rain_sum,showers_sum,snowfall_sum,sunrise,sunset,uv_index_max,windspeed_10m_max,windgusts_10m_max,winddirection_10m_dominant',
        timezone: 'auto',
        forecast_days: 7
      }
    });

    const data = response.data;
    
    // Balıkçılık koşullarını hesapla
    const fishingConditions = calculateFishingConditions(data.hourly);

    // Saatlik tahmin formatla
    const hourly = data.hourly.time.slice(0, 24).map((time: string, index: number) => {
      const hour = new Date(time).getHours();
      const weatherCode = data.hourly.weathercode[index];
      const weatherInfo = WMO_WEATHER_CODES[weatherCode] || { condition: 'Bilinmeyen', icon: 'cloud' };
      
      return {
        time: `${hour}:00`,
        temp: Math.round(data.hourly.temperature_2m[index]),
        condition: weatherInfo.condition,
        rain: Math.round(data.hourly.precipitation_probability[index]),
        humidity: Math.round(data.hourly.relative_humidity_2m[index]),
        windSpeed: Math.round(data.hourly.windspeed_10m[index]),
        windDirection: Math.round(data.hourly.winddirection_10m[index])
      };
    });

    // Günlük tahmin formatla
    const daily = data.daily.time.map((time: string, index: number) => {
      const date = new Date(time);
      const dayNames = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
      const dayName = dayNames[date.getDay()];
      const weatherCode = data.daily.weathercode[index];
      const weatherInfo = WMO_WEATHER_CODES[weatherCode] || { condition: 'Bilinmeyen', icon: 'cloud' };
      
      return {
        day: index === 0 ? 'Bugün' : index === 1 ? 'Yarın' : dayName,
        high: Math.round(data.daily.temperature_2m_max[index]),
        low: Math.round(data.daily.temperature_2m_min[index]),
        condition: weatherInfo.condition,
        rain: Math.round(data.daily.precipitation_probability_max[index]),
        windSpeed: Math.round(data.daily.windspeed_10m_max[index]),
        windDirection: Math.round(data.daily.winddirection_10m_dominant[index])
      };
    });

    const weatherData = {
      current: {
        temperature: Math.round(data.current.temperature_2m),
        condition: WMO_WEATHER_CODES[data.current.weathercode]?.condition || 'Bilinmeyen',
        windSpeed: Math.round(data.current.windspeed_10m),
        windDirection: Math.round(data.current.winddirection_10m),
        humidity: Math.round(data.current.relative_humidity_2m),
        pressure: Math.round(data.current.pressure_msl),
        uvIndex: Math.round(data.current.uv_index_max || 0),
        visibility: 10, // Open-Meteo'da visibility yok, default değer
        apparent_temperature: Math.round(data.current.apparent_temperature)
      },
      fishing: {
        score: fishingConditions.score,
        factors: fishingConditions.factors
      },
      hourly,
      daily
    };

    const apiResponse: ApiResponse<any> = {
      success: true,
      data: weatherData
    };

    res.json(apiResponse);
  } catch (error) {
    console.error('Open-Meteo API error:', error);
    throw new AppError('Weather data could not be fetched', 500);
  }
}));

// GET /api/weather/forecast - Get weather forecast
router.get('/forecast', asyncHandler(async (req, res) => {
  const lat = parseFloat(req.query.lat as string);
  const lon = parseFloat(req.query.lon as string);
  const days = parseInt(req.query.days as string) || 5;

  if (isNaN(lat) || isNaN(lon)) {
    throw new AppError('Valid latitude and longitude required', 400);
  }

  try {
    // Open-Meteo API'den veri çek
    const response = await axios.get(`${OPEN_METEO_BASE_URL}/forecast`, {
      params: {
        latitude: lat,
        longitude: lon,
        daily: 'temperature_2m_max,temperature_2m_min,precipitation_probability_max,precipitation_sum,weathercode,windspeed_10m_max,winddirection_10m_dominant',
        timezone: 'auto',
        forecast_days: Math.min(days, 7)
      }
    });

    const data = response.data;
    
    const forecastData = data.daily.time.map((time: string, index: number) => {
      const date = new Date(time);
      const dayNames = ['Pazar', 'Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi'];
      const dayName = dayNames[date.getDay()];
      const weatherCode = data.daily.weathercode[index];
      const weatherInfo = WMO_WEATHER_CODES[weatherCode] || { condition: 'Bilinmeyen', icon: 'cloud' };
      
      return {
        date: time,
        day: index === 0 ? 'Bugün' : index === 1 ? 'Yarın' : dayName,
        temperature: {
          high: Math.round(data.daily.temperature_2m_max[index]),
          low: Math.round(data.daily.temperature_2m_min[index])
        },
        condition: weatherInfo.condition,
        wind: {
          speed: Math.round(data.daily.windspeed_10m_max[index]),
          direction: Math.round(data.daily.winddirection_10m_dominant[index])
        },
        humidity: 60, // Open-Meteo daily'de humidity yok, default değer
        pressure: 1013, // Open-Meteo daily'de pressure yok, default değer
        fishingScore: 70 // Basit hesaplama
      };
    });

    const apiResponse: ApiResponse<any> = {
      success: true,
      data: {
        location: { lat, lon },
        forecast: forecastData
      }
    };

    res.json(apiResponse);
  } catch (error) {
    console.error('Open-Meteo API error:', error);
    throw new AppError('Weather forecast could not be fetched', 500);
  }
}));

// GET /api/weather/reverse-geocode - Get location name from coordinates
router.get('/reverse-geocode', asyncHandler(async (req, res) => {
  const lat = parseFloat(req.query.lat as string);
  const lon = parseFloat(req.query.lon as string);

  if (isNaN(lat) || isNaN(lon)) {
    throw new AppError('Valid latitude and longitude required', 400);
  }

  try {
    // Nominatim (OpenStreetMap) API'den konum adını çek
    const response = await axios.get('https://nominatim.openstreetmap.org/reverse', {
      params: {
        lat: lat,
        lon: lon,
        format: 'json',
        'accept-language': 'tr' // Türkçe sonuçlar için
      },
      headers: {
        // Nominatim bir User-Agent istiyor
        'User-Agent': 'FishivoApp/1.0 (https://fishivo.com)' 
      }
    });

    const data = response.data;
    let locationName = 'Bilinmeyen Konum';

    if (data && data.address) {
      const addr = data.address;
      // İl, ilçe, kasaba, köy gibi detayları önceliklendirerek al
      locationName = addr.city || addr.town || addr.village || addr.county || addr.state || addr.country || 'Bilinmeyen Konum';
    }

    const apiResponse: ApiResponse<any> = {
      success: true,
      data: {
        name: locationName,
        coordinates: { lat, lon }
      }
    };

    res.json(apiResponse);
  } catch (error) {
    console.error('Geocoding API error:', error);
    // Hata durumunda koordinatları döndür
    const apiResponse: ApiResponse<any> = {
      success: true,
      data: {
        name: `${lat.toFixed(4)}, ${lon.toFixed(4)}`,
        coordinates: { lat, lon }
      }
    };
    res.json(apiResponse);
  }
}));

export default router; 