import { supabase } from './supabase';

export interface UnitCategory {
  id: string;
  name: string;
  name_en: string;
  description: string;
  icon: string;
  base_unit: string;
  sort_order: number;
}

export interface UnitDefinition {
  id: string;
  category_id: string;
  name: string;
  name_en: string;
  symbol: string;
  is_base_unit: boolean;
  conversion_factor?: number;
  conversion_formula?: string;
  reverse_formula?: string;
  precision_digits: number;
  min_value?: number;
  max_value?: number;
  regions: string[];
  popularity: number;
  default_for_regions: string[];
  use_case?: string;
  sort_order: number;
}

export interface UserUnitPreferences {
  user_id: string;
  weight_unit: string;
  length_unit: string;
  distance_unit: string;
  temperature_unit: string;
  depth_unit: string;
  speed_unit: string;
  pressure_unit: string;
  region: string;
  auto_detect_region: boolean;
  updated_at: string;
}

export interface ConversionResult {
  value: number;
  fromUnit: string;
  toUnit: string;
  category: string;
  formula?: string;
  cached: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  value: number;
  unit: string;
  context?: string;
  errors: string[];
  warnings: string[];
}

export class DatabaseUnitService {
  
  /**
   * Get all unit categories
   */
  static async getCategories(): Promise<UnitCategory[]> {
    try {
      const { data, error } = await supabase
        .from('unit_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');
      
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting unit categories:', error);
      return [];
    }
  }

  /**
   * Get units for a specific category
   */
  static async getUnitsForCategory(categoryId: string): Promise<UnitDefinition[]> {
    try {
      const { data, error } = await supabase
        .from('unit_definitions')
        .select('*')
        .eq('category_id', categoryId)
        .eq('is_active', true)
        .order('sort_order');
      
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting units for category:', error);
      return [];
    }
  }

  /**
   * Convert between units
   */
  static async convertUnit(value: number, fromUnit: string, toUnit: string): Promise<ConversionResult> {
    try {
      // Check cache first
      const cached = await this.getCachedConversion(value, fromUnit, toUnit);
      if (cached) {
        return { ...cached, cached: true };
      }

      // Get unit definitions
      const [fromUnitDef, toUnitDef] = await Promise.all([
        this.getUnitDefinition(fromUnit),
        this.getUnitDefinition(toUnit)
      ]);

      if (!fromUnitDef || !toUnitDef) {
        throw new Error(`Unit definition not found: ${fromUnit} or ${toUnit}`);
      }

      if (fromUnitDef.category_id !== toUnitDef.category_id) {
        throw new Error(`Cannot convert between different categories: ${fromUnitDef.category_id} and ${toUnitDef.category_id}`);
      }

      let convertedValue = value;
      const category = fromUnitDef.category_id;

      // Special handling for temperature
      if (category === 'temperature') {
        convertedValue = this.convertTemperature(value, fromUnit, toUnit);
      } else {
        // Convert to base unit first
        // If fromUnit is not base unit, divide by its conversion factor to get base unit value
        if (!fromUnitDef.is_base_unit && fromUnitDef.conversion_factor) {
          convertedValue = value / fromUnitDef.conversion_factor;
        }

        // Convert from base unit to target unit
        // If toUnit is not base unit, multiply by its conversion factor to get target unit value
        if (!toUnitDef.is_base_unit && toUnitDef.conversion_factor) {
          convertedValue = convertedValue * toUnitDef.conversion_factor;
        }
      }

      // Round to appropriate precision
      const precision = toUnitDef.precision_digits;
      convertedValue = Math.round(convertedValue * Math.pow(10, precision)) / Math.pow(10, precision);

      const result: ConversionResult = {
        value: convertedValue,
        fromUnit,
        toUnit,
        category,
        cached: false
      };

      // Cache the result
      await this.cacheConversion(value, fromUnit, toUnit, convertedValue);

      return result;
    } catch (error) {
      console.error('Error converting units:', error);
      throw error;
    }
  }

  /**
   * Convert temperature between celsius and fahrenheit
   */
  private static convertTemperature(value: number, fromUnit: string, toUnit: string): number {
    if (fromUnit === toUnit) return value;
    
    if (fromUnit === 'celsius' && toUnit === 'fahrenheit') {
      return (value * 9/5) + 32;
    } else if (fromUnit === 'fahrenheit' && toUnit === 'celsius') {
      return (value - 32) * 5/9;
    }
    
    return value;
  }

  /**
   * Validate a value for a specific unit and context
   */
  static async validateValue(value: number, unit: string, context?: string): Promise<ValidationResult> {
    try {
      const unitDef = await this.getUnitDefinition(unit);
      if (!unitDef) {
        return {
          isValid: false,
          value,
          unit,
          context,
          errors: [`Unknown unit: ${unit}`],
          warnings: []
        };
      }

      const errors: string[] = [];
      const warnings: string[] = [];

      // Check basic range
      if (unitDef.min_value !== null && value < unitDef.min_value) {
        errors.push(`Value ${value} is below minimum ${unitDef.min_value} for ${unit}`);
      }
      if (unitDef.max_value !== null && value > unitDef.max_value) {
        errors.push(`Value ${value} is above maximum ${unitDef.max_value} for ${unit}`);
      }

      // Check context-specific rules
      if (context) {
        const contextRules = await this.getValidationRules(unit, context);
        for (const rule of contextRules) {
          if (rule.min_value !== null && value < rule.min_value) {
            errors.push(rule.error_message || `Value too low for ${context}`);
          }
          if (rule.max_value !== null && value > rule.max_value) {
            errors.push(rule.error_message || `Value too high for ${context}`);
          }
        }
      }

      return {
        isValid: errors.length === 0,
        value,
        unit,
        context,
        errors,
        warnings
      };
    } catch (error) {
      console.error('Error validating value:', error);
      return {
        isValid: false,
        value,
        unit,
        context,
        errors: ['Validation error occurred'],
        warnings: []
      };
    }
  }

  /**
   * Get user's unit preferences
   */
  static async getUserPreferences(userId: string): Promise<UserUnitPreferences | null> {
    try {
      const { data, error } = await supabase
        .from('user_unit_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return null;
    }
  }

  /**
   * Update user's unit preferences
   */
  static async updateUserPreferences(userId: string, preferences: Partial<UserUnitPreferences>): Promise<UserUnitPreferences> {
    try {
      const { data, error } = await supabase
        .from('user_unit_preferences')
        .upsert({
          user_id: userId,
          ...preferences,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }
  }

  /**
   * Convert value to base unit
   */
  static async convertToBaseUnit(value: number, fromUnit: string, category: string): Promise<number> {
    const baseUnits = {
      weight: 'kg',
      length: 'cm',
      distance: 'km',
      temperature: 'celsius',
      depth: 'meters',
      speed: 'kmh',
      pressure: 'hpa'
    };

    const baseUnit = baseUnits[category];
    if (!baseUnit) throw new Error(`Unknown category: ${category}`);

    const result = await this.convertUnit(value, fromUnit, baseUnit);
    return result.value;
  }

  /**
   * Convert value from base unit to target unit
   */
  static async convertFromBaseUnit(value: number, category: string, toUnit: string): Promise<number> {
    const baseUnits = {
      weight: 'kg',
      length: 'cm',
      distance: 'km',
      temperature: 'celsius',
      depth: 'meters',
      speed: 'kmh',
      pressure: 'hpa'
    };

    const baseUnit = baseUnits[category];
    if (!baseUnit) throw new Error(`Unknown category: ${category}`);

    const result = await this.convertUnit(value, baseUnit, toUnit);
    return result.value;
  }

  /**
   * Log unit usage for analytics
   */
  static async logUsage(userId: string, category: string, fromUnit: string, toUnit: string, value: number): Promise<void> {
    try {
      await supabase
        .from('unit_usage_analytics')
        .insert({
          user_id: userId,
          category,
          from_unit: fromUnit,
          to_unit: toUnit,
          value,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Error logging usage:', error);
      // Don't throw - analytics shouldn't break the main flow
    }
  }

  /**
   * Get usage statistics
   */
  static getStats(): any {
    // This would return cached statistics
    return {
      totalConversions: 0,
      popularUnits: [],
      regionalUsage: {}
    };
  }

  /**
   * Refresh cache
   */
  static async refresh(): Promise<void> {
    try {
      await supabase
        .from('unit_conversion_cache')
        .delete()
        .neq('id', 0); // Delete all cache entries
    } catch (error) {
      console.error('Error refreshing cache:', error);
    }
  }

  // Private helper methods

  private static async getUnitDefinition(unitId: string): Promise<UnitDefinition | null> {
    try {
      const { data, error } = await supabase
        .from('unit_definitions')
        .select('*')
        .eq('id', unitId)
        .eq('is_active', true)
        .single();
      
      if (error && error.code !== 'PGRST116') {
        throw error;
      }
      
      return data;
    } catch (error) {
      console.error('Error getting unit definition:', error);
      return null;
    }
  }

  private static async getCachedConversion(value: number, fromUnit: string, toUnit: string): Promise<ConversionResult | null> {
    try {
      const { data, error } = await supabase
        .from('unit_conversion_cache')
        .select('*')
        .eq('from_unit', fromUnit)
        .eq('to_unit', toUnit)
        .eq('input_value', value)
        .single();
      
      if (error && error.code !== 'PGRST116') {
        throw error;
      }
      
      if (data) {
        return {
          value: data.output_value,
          fromUnit,
          toUnit,
          category: data.category,
          cached: true
        };
      }
      
      return null;
    } catch (error) {
      console.error('Error getting cached conversion:', error);
      return null;
    }
  }

  private static async cacheConversion(inputValue: number, fromUnit: string, toUnit: string, outputValue: number): Promise<void> {
    try {
      const fromUnitDef = await this.getUnitDefinition(fromUnit);
      if (!fromUnitDef) return;

      await supabase
        .from('unit_conversion_cache')
        .insert({
          from_unit: fromUnit,
          to_unit: toUnit,
          input_value: inputValue,
          output_value: outputValue,
          category: fromUnitDef.category_id,
          created_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Error caching conversion:', error);
      // Don't throw - caching shouldn't break the main flow
    }
  }

  private static async getValidationRules(unit: string, context: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('unit_validation_rules')
        .select('*')
        .eq('unit_id', unit)
        .eq('context', context)
        .eq('is_active', true);
      
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting validation rules:', error);
      return [];
    }
  }
} 