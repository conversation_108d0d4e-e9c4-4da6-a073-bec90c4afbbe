// Backend Unit Conversion Service
// Converts user units to base units and vice versa

import { UserUnits, BASE_UNITS } from '../types/units';

// Conversion factors to base units
const CONVERSION_FACTORS = {
  weight: {
    kg: 1,
    g: 0.001,
    lbs: 0.453592,
    oz: 0.0283495
  },
  length: {
    cm: 1,
    m: 100,
    inch: 2.54,
    ft: 30.48
  },
  distance: {
    km: 1,
    m: 0.001,
    miles: 1.60934,
    nm: 1.852
  },
  temperature: {
    celsius: (c: number) => c,
    fahrenheit: (f: number) => (f - 32) * 5/9
  },
  depth: {
    meters: 1,
    feet: 0.3048,
    fathoms: 1.8288
  },
  speed: {
    kmh: 1,
    mph: 1.60934,
    knots: 1.852,
    ms: 3.6
  },
  pressure: {
    hpa: 1,
    mbar: 1,
    inhg: 33.8639,
    mmhg: 1.33322
  }
} as const;

export class UnitConversionService {
  
  /**
   * Convert user unit to base unit (for database storage)
   */
  static convertToBaseUnit(value: number, fromUnit: string, category: keyof UserUnits): number {
    if (!value) return 0;
    
    const baseUnit = BASE_UNITS[category];
    if (fromUnit === baseUnit) return value;
    
    const factors = CONVERSION_FACTORS[category];
    
    if (category === 'temperature') {
      if (fromUnit === 'fahrenheit') {
        return CONVERSION_FACTORS.temperature.fahrenheit(value);
      }
      return value; // celsius
    }
    
    const factor = factors[fromUnit as keyof typeof factors];
    if (typeof factor === 'number') {
      return value * factor;
    }
    
    return value;
  }
  
  /**
   * Convert base unit to user unit (for API response)
   */
  static convertFromBaseUnit(value: number, toUnit: string, category: keyof UserUnits): number {
    if (!value) return 0;
    
    const baseUnit = BASE_UNITS[category];
    if (toUnit === baseUnit) return value;
    
    const factors = CONVERSION_FACTORS[category];
    
    if (category === 'temperature') {
      if (toUnit === 'fahrenheit') {
        return (value * 9/5) + 32;
      }
      return value; // celsius
    }
    
    const factor = factors[toUnit as keyof typeof factors];
    if (typeof factor === 'number') {
      return value / factor;
    }
    
    return value;
  }
  
  /**
   * Convert catch data from user units to base units
   */
  static convertCatchToBaseUnits(catchData: any, userUnits: UserUnits): any {
    const converted = { ...catchData };
    
    if (catchData.weight) {
      converted.weight = this.convertToBaseUnit(catchData.weight, userUnits.weight, 'weight');
    }
    
    if (catchData.length) {
      converted.length = this.convertToBaseUnit(catchData.length, userUnits.length, 'length');
    }
    
    if (catchData.water_temperature) {
      converted.water_temperature = this.convertToBaseUnit(
        catchData.water_temperature, 
        userUnits.temperature, 
        'temperature'
      );
    }
    
    return converted;
  }
  
  /**
   * Convert catch data from base units to user units
   */
  static convertCatchFromBaseUnits(catchData: any, userUnits: UserUnits): any {
    const converted = { ...catchData };
    
    if (catchData.weight) {
      converted.weight = this.convertFromBaseUnit(catchData.weight, userUnits.weight, 'weight');
    }
    
    if (catchData.length) {
      converted.length = this.convertFromBaseUnit(catchData.length, userUnits.length, 'length');
    }
    
    if (catchData.water_temperature) {
      converted.water_temperature = this.convertFromBaseUnit(
        catchData.water_temperature, 
        userUnits.temperature, 
        'temperature'
      );
    }
    
    return converted;
  }
  
  /**
   * Get precision for a unit
   */
  static getPrecision(unit: string, category: keyof UserUnits): number {
    const precisionMap = {
      weight: { kg: 2, lbs: 2, g: 0, oz: 1 },
      length: { cm: 1, inch: 1, m: 2, ft: 1 },
      temperature: { celsius: 1, fahrenheit: 1 },
      depth: { meters: 1, feet: 1, fathoms: 1 },
      speed: { kmh: 1, mph: 1, knots: 1, ms: 1 },
      pressure: { hpa: 0, inhg: 2, mbar: 0, mmhg: 0 }
    };
    
    return precisionMap[category]?.[unit] || 1;
  }
} 