// User Preferences Service
// Manages user unit preferences and regional defaults

import { UserUnits } from '../types/units';
import { DatabaseService } from './supabase';

// Regional defaults
const REGIONAL_DEFAULTS: Record<string, UserUnits> = {
  'TR': {
    weight: 'kg',
    length: 'cm',
    distance: 'km',
    temperature: 'celsius',
    depth: 'meters',
    speed: 'kmh',
    pressure: 'hpa'
  },
  'US': {
    weight: 'lbs',
    length: 'inch',
    distance: 'miles',
    temperature: 'fahrenheit',
    depth: 'feet',
    speed: 'mph',
    pressure: 'inhg'
  },
  'UK': {
    weight: 'lbs',
    length: 'inch',
    distance: 'miles',
    temperature: 'celsius',
    depth: 'feet',
    speed: 'mph',
    pressure: 'hpa'
  },
  'EU': {
    weight: 'kg',
    length: 'cm',
    distance: 'km',
    temperature: 'celsius',
    depth: 'meters',
    speed: 'kmh',
    pressure: 'hpa'
  }
};

const DEFAULT_UNITS = REGIONAL_DEFAULTS['TR']; // Fallback

export interface UserPreferences {
  user_id: string;
  units: UserUnits;
  region: string;
  updated_at: string;
}

export class UserPreferencesService {
  
  /**
   * Get user's unit preferences
   */
  static async getUserPreferences(userId: string): Promise<UserUnits> {
    try {
      // Try to get from database
      const preferences = await DatabaseService.getUserPreferences(userId);
      
      if (preferences) {
        // Convert database format to UserUnits format
        return {
          weight: preferences.weight_unit,
          length: preferences.length_unit,
          distance: preferences.distance_unit,
          temperature: preferences.temperature_unit,
          depth: preferences.depth_unit,
          speed: preferences.speed_unit,
          pressure: preferences.pressure_unit
        };
      }
      
      // Return default if not found
      return DEFAULT_UNITS;
      
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return DEFAULT_UNITS;
    }
  }
  
  /**
   * Update user's unit preferences
   */
  static async updateUserPreferences(userId: string, units: Partial<UserUnits>): Promise<UserUnits> {
    try {
      // Get current preferences
      const currentPreferences = await this.getUserPreferences(userId);
      
      // Merge with new units
      const updatedUnits = { ...currentPreferences, ...units };
      
      // Save to database
      await DatabaseService.updateUserPreferences(userId, updatedUnits);
      
      return updatedUnits;
      
    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }
  }
  
  /**
   * Initialize user preferences based on region
   */
  static async initializeUserPreferences(userId: string, region: string = 'TR'): Promise<UserUnits> {
    try {
      const regionalDefaults = REGIONAL_DEFAULTS[region] || DEFAULT_UNITS;
      
      await DatabaseService.createUserPreferences(userId, regionalDefaults, region);
      
      return regionalDefaults;
      
    } catch (error) {
      console.error('Error initializing user preferences:', error);
      return DEFAULT_UNITS;
    }
  }
  
  /**
   * Get regional defaults
   */
  static getRegionalDefaults(region: string): UserUnits {
    return REGIONAL_DEFAULTS[region] || DEFAULT_UNITS;
  }
  
  /**
   * Detect region from request (IP, headers, etc.)
   */
  static detectRegionFromRequest(req: any): string {
    // Check user's country/region from various sources
    const country = req.headers['cf-ipcountry'] || // Cloudflare
                   req.headers['x-country-code'] || // Custom header
                   req.ip?.country || // IP geolocation
                   'TR'; // Default
    
    return country;
  }
} 