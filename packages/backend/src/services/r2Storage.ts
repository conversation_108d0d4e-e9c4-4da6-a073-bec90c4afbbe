import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';

interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
}

export class R2StorageService {
  private s3Client: S3Client;
  private bucketName: string;
  private publicUrl: string;

  constructor() {
    // Environment variables should be loaded once at app startup
    // No dotenv.config() calls in services - trust process.env
    const accountId = process.env.CLOUDFLARE_ACCOUNT_ID;
    const accessKeyId = process.env.CLOUDFLARE_R2_ACCESS_KEY_ID;
    const secretAccessKey = process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY;
    
    this.bucketName = process.env.CLOUDFLARE_R2_BUCKET_NAME || 'fishivo';
    this.publicUrl = process.env.CLOUDFLARE_R2_PUBLIC_URL || '';

    if (!accountId || !accessKeyId || !secretAccessKey) {
      console.warn('⚠️ R2 credentials missing:', {
        accountId: accountId ? 'SET' : 'MISSING',
        accessKeyId: accessKeyId ? 'SET' : 'MISSING',
        secretAccessKey: secretAccessKey ? 'SET' : 'MISSING'
      });
      throw new Error('Missing R2 credentials');
    }

    // Debug logging for credentials (safely)
    console.log('🔧 R2 Configuration:', {
      accountId: accountId,
      accessKeyId: accessKeyId?.substring(0, 8) + '...',
      secretAccessKey: secretAccessKey?.substring(0, 8) + '...',
      bucketName: this.bucketName,
      publicUrl: this.publicUrl,
      endpoint: `https://${accountId}.r2.cloudflarestorage.com`
    });

    // Correct Cloudflare R2 configuration
    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: `https://${accountId}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: accessKeyId,
        secretAccessKey: secretAccessKey,
      },
      // Standard S3 configuration for R2
      forcePathStyle: false,
    });

    console.log('✅ R2 Storage Service initialized with correct endpoint configuration for Cloudflare R2');
  }

  /**
   * Upload file to R2
   */
  async uploadFile(
    buffer: Buffer,
    key: string,
    contentType: string = 'application/octet-stream'
  ): Promise<UploadResult> {
    try {
      console.log('📤 Starting R2 upload:', {
        key,
        contentType,
        bufferSize: buffer.length,
        bucketName: this.bucketName
      });

      // Ensure key doesn't start with bucket name
      const cleanKey = key.startsWith(`${this.bucketName}/`) ? key.substring(this.bucketName.length + 1) : key;
      
      console.log('🔧 Upload parameters:', {
        Bucket: this.bucketName,
        Key: cleanKey,
        ContentType: contentType
      });

      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: cleanKey,
        Body: buffer,
        ContentType: contentType,
      });

      console.log('🔄 Sending PutObjectCommand to R2...');
      const result = await this.s3Client.send(command);
      
      console.log('✅ R2 upload successful:', {
        key: cleanKey,
        etag: result.ETag,
        versionId: result.VersionId
      });

      const publicUrl = this.publicUrl ? `${this.publicUrl}/${cleanKey}` : undefined;
      
      return {
        success: true,
        url: publicUrl,
        key: cleanKey
      };

    } catch (error: any) {
      console.error('❌ R2 upload failed:', {
        key,
        error: error.message,
        code: error.Code,
        statusCode: error.$metadata?.httpStatusCode,
        requestId: error.$metadata?.requestId,
        stack: error.stack
      });

      return {
        success: false,
        error: `R2 upload failed: ${error.message}`
      };
    }
  }

  /**
   * Delete file from R2
   */
  async deleteFile(key: string): Promise<boolean> {
    try {
      console.log('🗑️ Deleting from R2:', { key, bucketName: this.bucketName });

      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      console.log('✅ R2 delete successful:', { key });
      return true;

    } catch (error: any) {
      console.error('❌ R2 delete failed:', {
        key,
        error: error.message,
        code: error.Code
      });
      return false;
    }
  }

  /**
   * Generate public URL for file
   */
  private generatePublicUrl(key: string): string {
    if (this.publicUrl) {
      return `${this.publicUrl}/${key}`;
    }
    // Fallback to S3 URL format (private)
    return `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com/${this.bucketName}/${key}`;
  }

  /**
   * Check if R2 is configured
   */
  isConfigured(): boolean {
    return !!this.s3Client;
  }

  /**
   * Upload multiple files
   */
  async uploadMultipleFiles(
    files: Array<{
      buffer: Buffer;
      metadata: {
        userId: string;
        type: 'catch' | 'spot' | 'profile' | 'equipment';
        fileName: string;
        contentType: string;
        fishSpecies?: string;
        spotName?: string;
      };
    }>
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = [];

    for (const file of files) {
      // Generate unique file key
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const fileExtension = file.metadata.fileName.split('.').pop() || 'jpg';
      const key = `${file.metadata.type}/${file.metadata.userId}/${timestamp}_${randomId}.${fileExtension}`;
      
      const result = await this.uploadFile(file.buffer, key, file.metadata.contentType);
      results.push(result);
    }

    return results;
  }

  async getFile(key: string): Promise<Buffer | null> {
    try {
      console.log('📥 Getting from R2:', { key, bucketName: this.bucketName });

      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const result = await this.s3Client.send(command);
      
      if (result.Body) {
        const buffer = Buffer.from(await result.Body.transformToByteArray());
        console.log('✅ R2 get successful:', { key, size: buffer.length });
        return buffer;
      }

      return null;

    } catch (error: any) {
      console.error('❌ R2 get failed:', {
        key,
        error: error.message,
        code: error.Code
      });
      return null;
    }
  }
}

// Consolidated singleton instance
let r2StorageInstance: R2StorageService | null = null;

export function getR2Instance(): R2StorageService {
  if (!r2StorageInstance) {
    r2StorageInstance = new R2StorageService();
  }
  return r2StorageInstance;
}

// Main export - use this for all R2 operations
export default getR2Instance(); 