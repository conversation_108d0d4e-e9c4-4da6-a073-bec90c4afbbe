import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import crypto from 'crypto';
import path from 'path';

interface CloudflareR2Config {
  endpoint: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucket: string;
  publicDomain?: string;
}

class CloudflareR2Service {
  private s3Client: S3Client;
  private bucket: string;
  private publicDomain?: string;

  constructor(config: CloudflareR2Config) {
    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });
    this.bucket = config.bucket;
    this.publicDomain = config.publicDomain;
  }

  /**
   * Dosya yükleme
   */
  async uploadFile(
    file: Buffer,
    originalName: string,
    folder: string = '',
    contentType?: string
  ): Promise<{ url: string; key: string }> {
    try {
      // Unique filename oluştur
      const ext = path.extname(originalName);
      const name = path.basename(originalName, ext);
      const timestamp = Date.now();
      const randomString = crypto.randomBytes(8).toString('hex');
      const fileName = `${name}_${timestamp}_${randomString}${ext}`;
      
      // Folder path
      const key = folder ? `${folder}/${fileName}` : fileName;

      // Upload command
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        Body: file,
        ContentType: contentType || this.getContentType(ext),
        CacheControl: 'public, max-age=31536000', // 1 yıl cache
      });

      await this.s3Client.send(command);

      // Public URL oluştur
      const url = this.publicDomain 
        ? `${this.publicDomain}/${key}`
        : `${this.s3Client.config.endpoint}/${this.bucket}/${key}`;

      return { url, key };
    } catch (error) {
      console.error('R2 upload error:', error);
      throw new Error('Dosya yüklenirken hata oluştu');
    }
  }

  /**
   * Dosya silme
   */
  async deleteFile(key: string): Promise<void> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      await this.s3Client.send(command);
    } catch (error) {
      console.error('R2 delete error:', error);
      throw new Error('Dosya silinirken hata oluştu');
    }
  }

  /**
   * Presigned URL oluştur (yükleme için)
   */
  async getUploadUrl(
    key: string,
    contentType: string,
    expiresIn: number = 3600
  ): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        ContentType: contentType,
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn });
    } catch (error) {
      console.error('R2 presigned URL error:', error);
      throw new Error('Upload URL oluşturulamadı');
    }
  }

  /**
   * Dosya download URL'i al
   */
  async getDownloadUrl(key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn });
    } catch (error) {
      console.error('R2 download URL error:', error);
      throw new Error('Download URL oluşturulamadı');
    }
  }

  /**
   * Content type belirleme
   */
  private getContentType(ext: string): string {
    const contentTypes: Record<string, string> = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.mp4': 'video/mp4',
      '.avi': 'video/x-msvideo',
      '.mov': 'video/quicktime',
      '.pdf': 'application/pdf',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    };

    return contentTypes[ext.toLowerCase()] || 'application/octet-stream';
  }

  /**
   * Belirli klasördeki dosyaları listele
   */
  async listFiles(folder: string = '', maxKeys: number = 1000): Promise<string[]> {
    try {
      const { ListObjectsV2Command } = await import('@aws-sdk/client-s3');
      
      const command = new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: folder,
        MaxKeys: maxKeys,
      });

      const response = await this.s3Client.send(command);
      return response.Contents?.map(obj => obj.Key!) || [];
    } catch (error) {
      console.error('R2 list error:', error);
      throw new Error('Dosyalar listelenirken hata oluştu');
    }
  }

  /**
   * Batch delete - çoklu dosya silme
   */
  async deleteFiles(keys: string[]): Promise<void> {
    try {
      const { DeleteObjectsCommand } = await import('@aws-sdk/client-s3');
      
      const command = new DeleteObjectsCommand({
        Bucket: this.bucket,
        Delete: {
          Objects: keys.map(key => ({ Key: key })),
        },
      });

      await this.s3Client.send(command);
    } catch (error) {
      console.error('R2 batch delete error:', error);
      throw new Error('Dosyalar silinirken hata oluştu');
    }
  }
}

// Environment variables'dan config oluştur
const r2Config: CloudflareR2Config = {
  endpoint: process.env.CLOUDFLARE_R2_ENDPOINT || '',
  accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || '',
  secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY || '',
  bucket: process.env.CLOUDFLARE_R2_BUCKET_NAME || 'fishivo',
  publicDomain: process.env.CLOUDFLARE_R2_PUBLIC_DOMAIN, // Opsiyonel custom domain
};

// Singleton instance
export const r2Service = new CloudflareR2Service(r2Config);
export { CloudflareR2Service };
export type { CloudflareR2Config }; 