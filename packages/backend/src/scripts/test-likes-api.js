// API beğeni sistemi test script'i
// Bu script, beğeni API'sini test eder
const axios = require('axios');
require('dotenv').config();

// API URL
const API_URL = process.env.API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

// Test fonksiyonu
async function testLikesApi() {
  console.log(`🔍 Beğeni API testi başlatılıyor... (${API_URL})`);
  
  try {
    // 1. Önce API'nin çalışıp çalışmadığını kontrol et
    try {
      const healthResponse = await axios.get(`${API_URL}/health`);
      console.log(`✅ API sağlık kontrolü: ${healthResponse.status === 200 ? 'Başarılı' : 'Başarısız'}`);
    } catch (error) {
      console.log(`⚠️ API sağlık kontrolü yapılamadı: ${error.message}`);
      console.log('API çalışmıyor olabilir veya /health endpoint bulunmuyor olabilir.');
    }
    
    // 2. Test için kullanılacak örnek veriler
    const postId = 3; // Test edilecek post ID
    
    // 3. Beğenenler listesini al (anonim olarak)
    try {
      console.log(`\n📋 Post ${postId} için beğenenler listesi alınıyor (anonim)...`);
      const likersResponse = await axios.get(`${API_URL}/api/posts/${postId}/likers?page=1&limit=20`);
      console.log('✅ Beğenenler listesi alındı:');
      console.log(JSON.stringify(likersResponse.data, null, 2));
    } catch (error) {
      if (error.response && error.response.data && error.response.data.code === 'TOKEN_REQUIRED') {
        console.log('⚠️ Beklenen hata: Token gerekli (anonim erişim engellenmiş)');
      } else {
        console.error('❌ Beğenenler listesi alınırken beklenmeyen hata:', 
          error.response ? error.response.data : error.message);
      }
    }
    
    // 4. Supabase'den direkt olarak beğeni bilgilerini al
    console.log('\n📊 Supabase üzerinden beğeni bilgileri kontrol ediliyor...');
    const { createClient } = require('@supabase/supabase-js');
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Supabase bağlantı bilgileri eksik!');
      return;
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Post'un beğenilerini kontrol et
    const { count: postLikes, error: postLikesError } = await supabase
      .from('likes')
      .select('*', { count: 'exact', head: true })
      .eq('post_id', postId);
      
    if (postLikesError) {
      console.error(`❌ Post ${postId} beğeni sayısı alınırken hata:`, postLikesError);
      return;
    }
    
    console.log(`❤️ Post ${postId} beğeni sayısı: ${postLikes}`);
    
    // Beğenenleri ve kullanıcı bilgilerini al
    const { data: likesData, error: likesError } = await supabase
      .from('likes')
      .select(`
        id,
        user_id,
        created_at,
        users (
          id, 
          username, 
          full_name, 
          avatar_url, 
          is_pro
        )
      `)
      .eq('post_id', postId)
      .order('created_at', { ascending: false });
    
    if (likesError) {
      console.error(`❌ Post ${postId} beğenenler alınırken hata:`, likesError);
      return;
    }
    
    console.log(`📊 Post ${postId} için ${likesData?.length || 0} beğenen bulundu`);
    
    // Beğenenleri göster
    if (likesData && likesData.length > 0) {
      console.log('👥 Beğenenler:');
      likesData.forEach((like, index) => {
        const user = like.users || {};
        console.log(`  ${index + 1}. ${user.username || 'Kullanıcı'} (${like.user_id}) - ${new Date(like.created_at).toLocaleString()}`);
      });
      
      // Beğenenlerin JSON formatında göster
      console.log('\n📝 Beğenenler (JSON formatında):');
      console.log(JSON.stringify(likesData, null, 2));
    } else {
      console.log('⚠️ Bu post için beğenen bulunamadı');
    }
    
    console.log('\n✅ Beğeni API testi tamamlandı');
  } catch (error) {
    console.error('❌ Test sırasında beklenmeyen hata:', error);
  }
}

// Testi çalıştır
testLikesApi()
  .then(() => {
    console.log('🏁 Test tamamlandı');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Test başarısız oldu:', error);
    process.exit(1);
  }); 