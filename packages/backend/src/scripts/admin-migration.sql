-- Admin Panel Database Migration
-- <PERSON>u script'i Supabase SQL Editor'da çalıştırın

-- 1. Users tablosuna role kolonu ekle
ALTER TABLE users ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user';
ALTER TABLE users ADD COLUMN IF NOT EXISTS permissions J<PERSON><PERSON><PERSON> DEFAULT '[]';
ALTER TABLE users ADD COLUMN IF NOT EXISTS banned_until TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS ban_reason TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS pro_until TIMESTAMPTZ;

-- 2. <PERSON><PERSON> logs tablosu
CREATE TABLE IF NOT EXISTS admin_logs (
  id BIGSERIAL PRIMARY KEY,
  admin_id UUID REFERENCES users(id) ON DELETE SET NULL,
  action TEXT NOT NULL,
  target_type TEXT, -- 'user', 'post', 'spot', 'report'
  target_id TEXT,
  details JSON<PERSON>,
  ip_address INET,
  user_agent TEXT,
  request_body JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Reports tablosu
CREATE TABLE IF NOT EXISTS reports (
  id BIGSERIAL PRIMARY KEY,
  reporter_id UUID REFERENCES users(id) ON DELETE SET NULL,
  reported_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  target_type TEXT NOT NULL, -- 'post', 'comment', 'user', 'spot'
  target_id BIGINT,
  reason TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'pending', -- 'pending', 'investigating', 'resolved', 'dismissed'
  resolved_by UUID REFERENCES users(id) ON DELETE SET NULL,
  resolved_at TIMESTAMPTZ,
  resolution_notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Fish species tablosu
CREATE TABLE IF NOT EXISTS fish_species (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  scientific_name TEXT,
  category TEXT, -- 'Deniz Balığı', 'Tatlı Su Balığı'
  min_size INTEGER, -- cm
  max_size INTEGER, -- cm
  avg_weight DECIMAL(5,2), -- kg
  season JSONB, -- ['İlkbahar', 'Yaz', 'Sonbahar', 'Kış']
  habitat JSONB, -- ['Kıyı', 'Kayalık', 'Kumsal']
  bait JSONB, -- ['Canlı yem', 'Suni yem']
  difficulty TEXT, -- 'Kolay', 'Orta', 'Zor'
  image_url TEXT,
  description TEXT,
  status TEXT DEFAULT 'active', -- 'active', 'draft', 'archived'
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Fishing areas tablosu
CREATE TABLE IF NOT EXISTS fishing_areas (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT, -- 'Deniz', 'Tatlı Su'
  region TEXT,
  coordinates JSONB, -- {lat: number, lng: number}
  area_size DECIMAL(10,2), -- km²
  depth_range JSONB, -- {min: number, max: number}
  fish_species JSONB, -- ['Levrek', 'Çupra']
  facilities JSONB, -- ['İskele', 'Park', 'WC']
  regulations JSONB, -- {min_size_limits: boolean, seasonal_restrictions: [], daily_limit: number}
  access TEXT, -- 'Halka Açık', 'Ücretli', 'Özel'
  difficulty TEXT, -- 'Kolay', 'Orta', 'Zor'
  image_url TEXT,
  description TEXT,
  status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  approved_by UUID REFERENCES users(id) ON DELETE SET NULL,
  approved_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Equipment tablosu
CREATE TABLE IF NOT EXISTS equipment (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  category TEXT, -- 'Makine', 'Olta', 'Yem', 'İğne', 'Suni Yem'
  subcategory TEXT,
  brand TEXT,
  model TEXT,
  price_range JSONB, -- {min: number, max: number}
  specifications JSONB,
  suitable_for JSONB, -- ['Levrek', 'Çupra']
  fishing_type JSONB, -- ['Kıyı', 'Tekne']
  difficulty_level TEXT, -- 'Kolay', 'Orta', 'Zor'
  image_url TEXT,
  description TEXT,
  pros JSONB, -- ['Hafif', 'Dayanıklı']
  cons JSONB, -- ['Pahalı']
  user_rating DECIMAL(2,1),
  review_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'active', -- 'active', 'draft', 'archived'
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 7. System settings tablosu
CREATE TABLE IF NOT EXISTS system_settings (
  id BIGSERIAL PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  value JSONB NOT NULL,
  description TEXT,
  updated_by UUID REFERENCES users(id) ON DELETE SET NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8. İndeksler
CREATE INDEX IF NOT EXISTS idx_admin_logs_admin_id ON admin_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_logs_created_at ON admin_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_reports_status ON reports(status);
CREATE INDEX IF NOT EXISTS idx_reports_target ON reports(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_fish_species_status ON fish_species(status);
CREATE INDEX IF NOT EXISTS idx_fishing_areas_status ON fishing_areas(status);
CREATE INDEX IF NOT EXISTS idx_equipment_category ON equipment(category);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- 9. İlk admin kullanıcısı oluştur (email'i kendi email'iniz ile değiştirin)
UPDATE users 
SET role = 'super_admin', permissions = '["all"]'
WHERE email = '<EMAIL>';

-- 10. Varsayılan sistem ayarları
INSERT INTO system_settings (key, value, description) VALUES
('registration_enabled', 'true', 'Yeni kullanıcı kayıtlarına izin ver'),
('post_moderation', 'auto', 'Post moderasyon modu: auto, manual'),
('spot_approval_required', 'true', 'Yeni spotlar için admin onayı gerekli'),
('daily_post_limit', '10', 'Kullanıcı başına günlük post limiti'),
('max_file_size', '5242880', 'Maksimum dosya boyutu (bytes)'),
('daily_spot_limit', '3', 'Kullanıcı başına günlük spot ekleme limiti'),
('daily_message_limit', '50', 'Kullanıcı başına günlük mesaj limiti')
ON CONFLICT (key) DO NOTHING;

-- 11. RLS (Row Level Security) politikaları
ALTER TABLE admin_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE fish_species ENABLE ROW LEVEL SECURITY;
ALTER TABLE fishing_areas ENABLE ROW LEVEL SECURITY;
ALTER TABLE equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Admin logs sadece admin'ler görebilir
CREATE POLICY "Admin logs viewable by admins only" ON admin_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'super_admin')
    )
  );

-- Reports herkes oluşturabilir, admin'ler yönetebilir
CREATE POLICY "Anyone can create reports" ON reports
  FOR INSERT WITH CHECK (auth.uid() = reporter_id);

CREATE POLICY "Admins can manage reports" ON reports
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'super_admin')
    )
  );

-- Fish species, fishing areas, equipment - admin'ler yönetebilir
CREATE POLICY "Admins can manage fish species" ON fish_species
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'super_admin')
    )
  );

CREATE POLICY "Admins can manage fishing areas" ON fishing_areas
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'super_admin')
    )
  );

CREATE POLICY "Admins can manage equipment" ON equipment
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'super_admin')
    )
  );

-- System settings sadece super admin'ler yönetebilir
CREATE POLICY "Super admins can manage system settings" ON system_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role = 'super_admin'
    )
  );
