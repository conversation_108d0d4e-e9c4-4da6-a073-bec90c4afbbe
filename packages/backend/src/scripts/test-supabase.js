require('dotenv').config({path: '../../.env'});
const { createClient } = require('@supabase/supabase-js');

console.log('🔍 Supabase Connection Test:');
console.log('URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log('ANON_KEY exists:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
console.log('SERVICE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testConnection() {
  console.log('🔍 Supabase bağlantısı test ediliyor...');
  
  try {
    const { data, error } = await supabase.auth.getUser();
    
    if (error) {
      console.error('❌ Supabase bağlantı hatası:', error);
      return;
    }
    
    console.log('✅ Supabase bağlantısı başarılı');
  } catch (error) {
    console.error('❌ Beklenmeyen hata:', error);
  }
}

async function testLikeSystem() {
  console.log('🔍 Beğeni sistemi testi başlatılıyor...');
  
  try {
    // Toplam beğeni sayısını kontrol et
    const { count: totalLikes, error: countError } = await supabase
      .from('likes')
      .select('*', { count: 'exact', head: true });
      
    if (countError) {
      console.error('❌ Beğeni sayısı alınırken hata:', countError);
      return;
    }
    
    console.log(`📊 Toplam beğeni sayısı: ${totalLikes}`);
    
    // Rastgele bir post ID seç
    const { data: posts, error: postsError } = await supabase
      .from('posts')
      .select('id')
      .limit(1);
      
    if (postsError || !posts || posts.length === 0) {
      console.error('❌ Post bulunamadı:', postsError);
      return;
    }
    
    const postId = posts[0].id;
    console.log(`📝 Test için seçilen post ID: ${postId}`);
    
    // Post'un beğenilerini kontrol et
    const { count: postLikes, error: postLikesError } = await supabase
      .from('likes')
      .select('*', { count: 'exact', head: true })
      .eq('post_id', postId);
      
    if (postLikesError) {
      console.error(`❌ Post ${postId} beğeni sayısı alınırken hata:`, postLikesError);
      return;
    }
    
    console.log(`❤️ Post ${postId} beğeni sayısı: ${postLikes}`);
    
    // Beğenenleri ve kullanıcı bilgilerini al
    const { data: likesData, error: likesError } = await supabase
      .from('likes')
      .select(`
        id,
        user_id,
        created_at,
        users (
          id, 
          username, 
          full_name, 
          avatar_url, 
          is_pro
        )
      `)
      .eq('post_id', postId)
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (likesError) {
      console.error(`❌ Post ${postId} beğenenler alınırken hata:`, likesError);
      return;
    }
    
    console.log(`📊 Post ${postId} için ${likesData?.length || 0} beğenen bulundu`);
    
    // Beğenenleri göster
    if (likesData && likesData.length > 0) {
      console.log('👥 Beğenenler:');
      likesData.forEach((like, index) => {
        const user = like.users || {};
        console.log(`  ${index + 1}. ${user.username || 'Kullanıcı'} (${like.user_id}) - ${new Date(like.created_at).toLocaleString()}`);
      });
    } else {
      console.log('⚠️ Bu post için beğenen bulunamadı');
    }
    
    console.log('✅ Beğeni sistemi testi tamamlandı');
  } catch (error) {
    console.error('❌ Test sırasında beklenmeyen hata:', error);
  }
}

// Ana test fonksiyonu
async function runTests() {
  try {
    await testConnection();
    await testLikeSystem();
    console.log('🏁 Tüm testler tamamlandı');
  } catch (error) {
    console.error('❌ Testler başarısız oldu:', error);
  }
  process.exit(0);
}

// Testleri çalıştır
runTests(); 