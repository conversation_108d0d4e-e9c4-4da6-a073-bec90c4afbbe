require('dotenv').config({path: '../../.env'});

console.log('🔍 Environment Variables Test:');
console.log('SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
console.log('SERVICE_KEY exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY);
console.log('ANON_KEY exists:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
console.log('JWT_SECRET exists:', !!process.env.SUPABASE_JWT_SECRET);
console.log('CLOUDFLARE_R2_BUCKET:', process.env.CLOUDFLARE_R2_BUCKET_NAME); 