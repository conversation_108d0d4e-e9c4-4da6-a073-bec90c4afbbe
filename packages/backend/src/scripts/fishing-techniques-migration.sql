-- Fishing techniques tablosu oluştur
CREATE TABLE IF NOT EXISTS fishing_techniques (
  id BIGSERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE,
  icon TEXT,
  description TEXT,
  difficulty TEXT CHECK (difficulty IN ('Kolay', '<PERSON><PERSON>', '<PERSON>or')),
  best_for JSONB, -- ['<PERSON><PERSON>', '<PERSON>ü<PERSON>', '<PERSON>lam<PERSON>']
  equipment JSONB, -- ['Jigging kamışı', 'Jigging makinesi']
  water_types JSONB, -- ['Deniz', 'Ta<PERSON><PERSON> su']
  season TEXT,
  tips JSONB, -- ['<PERSON><PERSON><PERSON><PERSON> hareket yapın', '<PERSON><PERSON><PERSON><PERSON><PERSON> kontrol edin']
  image_url TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- İndeks oluştur
CREATE INDEX IF NOT EXISTS idx_fishing_techniques_status ON fishing_techniques(status);
CREATE INDEX IF NOT EXISTS idx_fishing_techniques_difficulty ON fishing_techniques(difficulty);

-- Seed data ekle
INSERT INTO fishing_techniques (name, icon, description, difficulty, best_for, equipment, water_types, season, tips) VALUES
('Jigging', 'target', 'Dikey hareket ile balık avlama tekniği', 'Orta', 
 '["Levrek", "Lüfer", "Palamut"]', 
 '["Jigging kamışı", "Jigging makinesi", "Metal jig"]', 
 '["Deniz"]', 
 'Tüm sezonlar', 
 '["Düzenli hareket yapın", "Derinliği kontrol edin"]'),

('Trolling', 'anchor', 'Tekne ile çekerek balık avlama', 'Zor', 
 '["Ton balığı", "Orkinos", "Palamut"]', 
 '["Trolling kamışı", "Güçlü makine", "Trolling yemi"]', 
 '["Deniz"]', 
 'Yaz-Sonbahar', 
 '["Sabit hız tutun", "Doğru derinlik ayarı"]'),

('Casting', 'crosshair', 'Hedefli atış ile balık avlama', 'Orta', 
 '["Levrek", "Çupra", "Mercan"]', 
 '["Casting kamışı", "Spinning makine", "Suni yem"]', 
 '["Deniz", "Tatlı su"]', 
 'Tüm sezonlar', 
 '["Doğru hedefleme", "Yem hareketini kontrol edin"]'),

('Bottom Fishing', 'anchor', 'Dipten balık avlama tekniği', 'Kolay', 
 '["Mezgit", "Barbun", "Kefal"]', 
 '["Dip kamışı", "Ağır kurşun", "Canlı yem"]', 
 '["Deniz", "Tatlı su"]', 
 'Tüm sezonlar', 
 '["Ağır kurşun kullanın", "Sabırlı olun"]'),

('Spinning', 'rotate-cw', 'Döner yem ile balık avlama', 'Kolay', 
 '["Alabalık", "Turna", "Levrek"]', 
 '["Spinning kamışı", "Spinning makine", "Spinner"]', 
 '["Tatlı su", "Deniz"]', 
 'İlkbahar-Yaz', 
 '["Düzenli geri çekme", "Farklı hızlar deneyin"]'),

('Fly Fishing', 'feather', 'Sinek yemi ile balık avlama', 'Zor', 
 '["Alabalık", "Somon", "Kefal"]', 
 '["Fly kamışı", "Fly makine", "Sinek yemi"]', 
 '["Tatlı su"]', 
 'İlkbahar-Sonbahar', 
 '["Doğru atış tekniği", "Suya yumuşak iniş"]')

ON CONFLICT (name) DO NOTHING;

-- RLS politikası
ALTER TABLE fishing_techniques ENABLE ROW LEVEL SECURITY;

-- Herkes okuyabilir
CREATE POLICY "Anyone can view fishing techniques" ON fishing_techniques
  FOR SELECT USING (status = 'active');

-- Sadece admin'ler yönetebilir
CREATE POLICY "Admins can manage fishing techniques" ON fishing_techniques
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role IN ('admin', 'super_admin')
    )
  ); 