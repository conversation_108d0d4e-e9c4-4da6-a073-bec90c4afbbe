import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import path from 'path';
import { createServer } from 'http';
import { ENV } from './config';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/logger';
import http from 'http';
import net from 'net';
import app from './app';
import ServerConfig from './config/server';
import { rateLimiter } from './middleware/rateLimiter';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import postRoutes from './routes/posts';
import speciesRoutes from './routes/species';
import uploadRoutes from './routes/upload';
import spotsRoutes from './routes/spots';
import followsRoutes from './routes/follows';
import messagesRoutes from './routes/messages';
import searchRoutes from './routes/search';
import adminRoutes from './routes/admin';
import userPreferencesRoutes from './routes/userPreferences';
import unitsRoutes from './routes/units';
import tripsRoutes from './routes/trips';
import reviewsRoutes from './routes/reviews';
import notificationRoutes from './routes/notifications';
import equipmentRoutes from './routes/equipment';
import weatherRoutes from './routes/weather';
import techniquesRoutes from './routes/techniques';
// import badgesRoutes from './routes/badges';

// Import middleware
import { SECURITY_CONFIG } from './config';

/**
 * Port kullanılabilirliğini kontrol et
 * @param port - Kontrol edilecek port numarası
 * @returns Portun kullanılabilir olup olmadığı
 */
function isPortAvailable(port: number): Promise<boolean> {
  return new Promise<boolean>((resolve) => {
    const tester = net.createServer()
      .once('error', () => {
        // Port kullanımda
        resolve(false);
      })
      .once('listening', () => {
        // Port kullanılabilir durumda
        tester.close(() => resolve(true));
      })
      .listen(port);
  });
}

/**
 * Sunucuyu belirtilen portta başlat
 * @param port - Başlatılacak port numarası
 * @returns HTTP sunucusu
 */
async function startServer(port: number = Number(ServerConfig.PORT)): Promise<http.Server> {
  try {
    // Port kontrolü - kullanılabilir mi?
    const portAvailable = await isPortAvailable(port);
    
    if (!portAvailable) {
      console.warn(`⚠️ Port ${port} is already in use`);
      
      // Port çakışması stratejisine göre işlem yap
      if (ServerConfig.PORT_CONFLICT_ACTION === 'retry') {
        const alternativePort = ServerConfig.findAlternativePort();
        if (alternativePort > 0) {
          console.log(`🔄 Switching to alternative port: ${alternativePort}`);
          return startServer(alternativePort);
        } else {
          throw new Error('No available ports found and PORT_CONFLICT_ACTION is retry');
        }
      } else {
        // Varsayılan davranış - çık
        throw new Error(`Port ${port} is already in use and PORT_CONFLICT_ACTION is exit`);
      }
    }
    
    // PID kontrol et ve yaz
    if (ServerConfig.checkPidFile()) {
      console.warn('⚠️ Another instance of this server may be running');
      // Burada bir karar mekanizması ekleyebiliriz, şimdilik devam ediyoruz
    }
    ServerConfig.writePidFile();

    // Sunucuyu başlat
    const server = http.createServer(app);
    
    server.listen(port, '0.0.0.0', () => {
      console.log('🚀 Fishivo API Server running on port', port);
      console.log(`📱 Environment: ${ENV.NODE_ENV}`);
      console.log(`🌐 API URL: ${ENV.API_URL}`);
      console.log(`🔗 Health check: ${ENV.API_URL}${ServerConfig.HEALTH_ENDPOINT}`); 
      
      if (ENV.NODE_ENV === 'development') {
        console.log('🔧 Development mode - detailed logging enabled');
      }
    });

    // Zarif kapatma işleyicileri
    setupGracefulShutdown(server);
    
    return server;
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

/**
 * Zarif kapatma sistemi
 * Sunucu kapatılırken mevcut bağlantıların düzgün şekilde sonlandırılmasını sağlar
 * @param server - HTTP sunucusu
 */
function setupGracefulShutdown(server: http.Server): void {
  // Açık bağlantılar izleme
  const connections = new Set<net.Socket>();
  
  server.on('connection', (connection) => {
    connections.add(connection);
    connection.on('close', () => {
      connections.delete(connection);
    });
  });

  // SIGTERM - Docker container'ları ve Kubernetes tarafından kullanılır
  process.on('SIGTERM', () => gracefulShutdown(server, connections, 'SIGTERM'));
  
  // SIGINT - Ctrl+C
  process.on('SIGINT', () => gracefulShutdown(server, connections, 'SIGINT'));
  
  // Beklenmeyen hatalar
  process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    gracefulShutdown(server, connections, 'uncaughtException');
  });
  
  process.on('unhandledRejection', (reason) => {
    console.error('❌ Unhandled Rejection:', reason);
    gracefulShutdown(server, connections, 'unhandledRejection');
  });
}

/**
 * Sunucuyu zarif bir şekilde kapatır
 * @param server - HTTP sunucusu
 * @param connections - Aktif bağlantılar kümesi
 * @param signal - Tetikleyici sinyal
 */
function gracefulShutdown(
  server: http.Server,
  connections: Set<net.Socket>,
  signal: string
): void {
  console.log(`🛑 Received ${signal}, starting graceful shutdown`);
  
  // PID dosyasını temizle
  ServerConfig.cleanPidFile();
  
  // Yeni bağlantıları reddet
  server.close(() => {
    console.log('✅ Server closed successfully');
    process.exit(0);
  });
  
  // Timeout - belirtilen süre içinde kapatamazsa zorla kapat
  const forcedShutdownTimeout = setTimeout(() => {
    console.error('⏱️ Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 5000);
  
  // Timeout'u temizle - NodeJS.Timeout tipinde olduğunu belirtelim
  (forcedShutdownTimeout as unknown as NodeJS.Timeout).unref();
  
  // Açık bağlantıları kapat
  if (connections.size > 0) {
    console.log(`🔌 Closing ${connections.size} open connection(s)`);
    connections.forEach((conn) => conn.end());
    
    // Tüm bağlantılar kapatıldıktan sonra yeniden kontrol et
    setTimeout(() => {
      if (connections.size > 0) {
        console.log(`🔌 Destroying ${connections.size} remaining connection(s)`);
        connections.forEach((conn) => conn.destroy());
      }
    }, 2500);
  }
}

export default startServer;

// Direct execution - sadece doğrudan çalıştırıldığında server'ı başlat
if (require.main === module) {
  startServer().catch((err) => {
    console.error('❌ Failed to start server:', err);
    process.exit(1);
  });
}