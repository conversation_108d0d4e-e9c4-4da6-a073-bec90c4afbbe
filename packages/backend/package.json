{"name": "@fishivo/backend", "version": "1.0.0", "description": "Fishivo Backend API - Fishbrain Clone", "main": "dist/server.js", "scripts": {"dev": "ts-node --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["fishivo", "fishing", "social", "api", "backend", "fishbrain", "clone"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "3.504.0", "@aws-sdk/s3-request-presigner": "3.504.0", "@fishivo/config": "1.0.0", "@fishivo/services": "1.0.0", "@fishivo/shared": "1.0.0", "@fishivo/utils": "1.0.0", "@supabase/supabase-js": "^2.50.2", "@types/compression": "1.7.5", "@types/express-session": "1.17.10", "@types/passport": "1.0.16", "@types/passport-facebook": "3.0.3", "@types/passport-google-oauth20": "2.0.14", "axios": "1.6.7", "bcryptjs": "2.4.3", "compression": "1.7.4", "cookie-parser": "1.4.6", "cors": "2.8.5", "dotenv": "16.4.1", "express": "4.18.2", "express-rate-limit": "7.1.5", "express-session": "1.17.3", "form-data": "4.0.0", "google-auth-library": "9.4.2", "helmet": "7.1.0", "joi": "17.12.0", "jsonwebtoken": "9.0.2", "morgan": "1.10.0", "multer": "1.4.5-lts.1", "passport": "0.7.0", "passport-facebook": "3.0.0", "passport-google-oauth20": "2.0.0", "rate-limiter-flexible": "4.0.1", "uuid": "9.0.1"}, "devDependencies": {"@types/bcryptjs": "2.4.6", "@types/cookie-parser": "1.4.6", "@types/cors": "2.8.17", "@types/express": "4.17.21", "@types/jest": "29.5.11", "@types/jsonwebtoken": "9.0.5", "@types/morgan": "1.9.9", "@types/multer": "1.4.11", "@types/node": "20.11.5", "@types/uuid": "9.0.7", "@typescript-eslint/eslint-plugin": "6.19.1", "@typescript-eslint/parser": "6.19.1", "eslint": "8.56.0", "jest": "^29.2.1", "ts-node": "10.9.2", "typescript": "5.3.3"}, "engines": {"node": ">=18.0.0"}}