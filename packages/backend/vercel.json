{"$schema": "https://openapi.vercel.sh/vercel.json", "version": 2, "name": "fishivo-backend", "buildCommand": "cd ../.. && turbo run build --filter=@fishivo/backend", "installCommand": "cd ../.. && yarn install", "outputDirectory": "dist", "functions": {"dist/index.js": {"runtime": "nodejs18.x"}}, "routes": [{"src": "/(.*)", "dest": "/dist/index.js"}], "env": {"NODE_ENV": "production", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "NEXT_PUBLIC_SUPABASE_URL": "@next_public_supabase_url", "JWT_SECRET": "@jwt_secret", "SESSION_SECRET": "@session_secret", "GOOGLE_CLIENT_SECRET": "@google_client_secret", "FACEBOOK_APP_SECRET": "@facebook_app_secret", "CLOUDFLARE_R2_ACCESS_KEY_ID": "@cloudflare_r2_access_key_id", "CLOUDFLARE_R2_SECRET_ACCESS_KEY": "@cloudflare_r2_secret_access_key"}, "regions": ["fra1"]}