import { defineConfig } from 'tsup';

export default defineConfig({
  entry: ['src/index.ts'],
  format: ['cjs', 'esm'],
  dts: {
    resolve: true,
    compilerOptions: {
      composite: false,
      skipLibCheck: true,
    },
  },
  splitting: false,
  sourcemap: true,
  clean: true,
  external: [
    'react',
    'react-native',
    'react-native-safe-area-context',
    'react-native-svg',
    'react-native-vector-icons',
    'react-native-linear-gradient',
    '@rnmapbox/maps',
    'lucide-react-native',
    '@react-native-async-storage/async-storage'
  ],
  esbuild: {
    jsx: 'automatic',
    loader: {
      '.js': 'jsx',
    },
  },
});