{"name": "@fishivo/ui", "version": "1.0.0", "description": "Shared UI components for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx"}, "keywords": ["ui", "components", "react", "react-native"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@fishivo/hooks": "1.0.0", "@fishivo/services": "1.0.0", "@fishivo/shared": "1.0.0", "@fishivo/utils": "1.0.0", "lucide-react-native": "^0.523.0", "react": "18.2.0", "react-native": "^0.74.0", "react-native-svg": "^15.4.0"}, "devDependencies": {"@types/react": "18.2.21", "eslint": "8.56.0", "tsup": "^8.0.0", "typescript": "5.3.3"}, "peerDependencies": {"react": "^18.2.0", "react-native": "^0.74.0"}}