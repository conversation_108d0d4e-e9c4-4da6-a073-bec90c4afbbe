/**
 * Icon Components
 * Shared icon components for Fishivo applications
 */

// Re-export the main Icon component
export { default as Icon } from '../components/Icon';

// Export other icon-related components
export { default as FishingIcons } from '../components/FishingIcons';
export { default as CrownIcon } from '../components/CrownIcon';

// Export icon types if needed
// Note: IconProps is not exported from Icon component, so we'll define it here
export interface IconProps {
  name: string;
  size?: number;
  color?: string;
}
