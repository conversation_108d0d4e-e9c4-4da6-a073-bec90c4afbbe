import React, { useState } from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import Icon from './Icon';
import { theme } from '@fishivo/shared';
import { useLocation, LocationData, formatLocationString } from '../../../../apps/mobile/src/services/LocationService';

interface MapLocationButtonProps {
  onLocationFound?: (location: LocationData) => void;
  onLocationUpdate?: (locationString: string, coordinates: { latitude: number; longitude: number }) => void;
  onLocationError?: (title: string, message: string) => void;
  onLocationSuccess?: (title: string, message: string) => void;
  style?: any;
  iconSize?: number;
  iconColor?: string;
  showAccuracy?: boolean;
}

const MapLocationButton: React.FC<MapLocationButtonProps> = ({
  onLocationFound,
  onLocationUpdate,
  onLocationError,
  onLocationSuccess,
  style = {},
  iconSize = 18,
  iconColor = theme.colors.primary,
  showAccuracy = false,
}) => {
  const { getCurrentLocation, isLoading } = useLocation();
  const [localLoading, setLocalLoading] = useState(false);

  const handleLocationPress = async () => {
    try {
      setLocalLoading(true);
      
      console.log('🎯 MapLocationButton: Konum alınıyor...');
      const location = await getCurrentLocation();
      
      if (location) {
        console.log('✅ MapLocationButton: Konum bulundu:', {
          lat: location.latitude.toFixed(6),
          lng: location.longitude.toFixed(6),
          accuracy: location.accuracy,
        });

        // Ana callback
        if (onLocationFound) {
          onLocationFound(location);
        }

        // Legacy callback - eski sistemle uyumlu
        if (onLocationUpdate) {
          const locationString = formatLocationString(location);
          onLocationUpdate(locationString, {
            latitude: location.latitude,
            longitude: location.longitude,
          });
        }

        // Accuracy bilgisi göster
        if (showAccuracy && location.accuracy && onLocationSuccess) {
          onLocationSuccess(
            'Konum Bulundu',
            `Doğruluk: ±${Math.round(location.accuracy)}m`
          );
        }
      } else {
        console.log('❌ MapLocationButton: Konum bulunamadı');
        if (onLocationError) {
          onLocationError(
            'Konum Bulunamadı',
            'GPS sinyali alınamadı. Lütfen açık alanda tekrar deneyin.'
          );
        }
      }
    } catch (error) {
      console.error('MapLocationButton Error:', error);
      if (onLocationError) {
        onLocationError(
          'Hata',
          'Konum alınırken bir hata oluştu.'
        );
      }
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.locationButton, style]}
      onPress={handleLocationPress}
      disabled={isLoading || localLoading}
      activeOpacity={0.7}
    >
      {(isLoading || localLoading) ? (
        <ActivityIndicator size="small" color={iconColor} />
      ) : (
        <Icon name="my-location" size={iconSize} color={iconColor} />
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  locationButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
});

export default MapLocationButton; 