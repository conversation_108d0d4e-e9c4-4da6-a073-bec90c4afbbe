import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Platform,
} from 'react-native';
import Mapbox from '@rnmapbox/maps';
import Geolocation from '@react-native-community/geolocation';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import Icon from './Icon';
import { theme } from '@fishivo/shared';

// Mapbox token setup
Mapbox.setAccessToken('pk.eyJ1IjoiZmlzaGl2byIsImEiOiJjbWJsaWphZWwwbjdpMmtxeTMwaGU5Zm4yIn0.LUiv6j3SGgFjTAJfpuuwDA');

interface MapComponentProps {
  // Konum seçimi için callback
  onLocationSelect?: (coordinates: [number, number]) => void;
  // İlk konum
  initialCoordinates?: [number, number];
  // Crosshair göster/gizle
  showCrosshair?: boolean;
  // Koordinat display göster/gizle
  showCoordinates?: boolean;
  // Konum butonu göster/gizle
  showLocationButton?: boolean;
  // Layer selector göster/gizle
  showLayerSelector?: boolean;
  // 3D toggle göster/gizle
  show3DToggle?: boolean;
  // Konum hatası callback
  onLocationError?: (message: string) => void;
  // Stil
  style?: any;
}

const MapComponent: React.FC<MapComponentProps> = ({
  onLocationSelect,
  initialCoordinates = [29.0158, 41.0053],
  showCrosshair = true,
  showCoordinates = true,
  showLocationButton = true,
  showLayerSelector = true,
  show3DToggle = true,
  onLocationError,
  style,
}) => {
  const mapRef = useRef<Mapbox.MapView>(null);
  const [currentCoordinates, setCurrentCoordinates] = useState<[number, number]>(initialCoordinates);
  const [isLocationLoading, setIsLocationLoading] = useState(true);
  const [mapMounted, setMapMounted] = useState(false);
  const [locationPermissionGranted, setLocationPermissionGranted] = useState(false);
  const [layersExpanded, setLayersExpanded] = useState(false);
  const [currentZoom, setCurrentZoom] = useState(16);
  const [mapPitch, setMapPitch] = useState(0);
  const [mapBearing, setMapBearing] = useState(0);
  const [currentStyle, setCurrentStyle] = useState('streets-v12');
  const layersAnimation = useRef(new Animated.Value(0)).current;

  const mapStyles = [
    { id: 'streets-v12', name: 'Harita', styleURL: 'mapbox://styles/mapbox/streets-v12', icon: 'map' },
    { id: 'outdoors-v12', name: 'Doğa', styleURL: 'mapbox://styles/mapbox/outdoors-v12', icon: 'trees' },
    { id: 'satellite-streets-v12', name: 'Uydu', styleURL: 'mapbox://styles/mapbox/satellite-streets-v12', icon: 'satellite' },
    { id: 'dark-v11', name: 'Gece', styleURL: 'mapbox://styles/mapbox/dark-v11', icon: 'moon' },
  ];

  // Crosshair koordinatlarını güncelle
  const [crosshairCoordinates, setCrosshairCoordinates] = useState<[number, number]>(initialCoordinates);

  useEffect(() => {
    let isMounted = true;

    const initializeLocation = async () => {
      try {
        // Android ve iOS için permission kontrolü
        const permission = Platform.OS === 'ios'
          ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
          : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;

        const result = await request(permission);
        if (!isMounted) return;

        if (result !== RESULTS.GRANTED) {
          setDefaultLocation();
          return;
        }
        setLocationPermissionGranted(true);

        Geolocation.getCurrentPosition(
          (position) => {
            if (!isMounted) return;

            const userLocation: [number, number] = [
              Number(position.coords.longitude),
              Number(position.coords.latitude)
            ];

            setCurrentCoordinates(userLocation);
            setIsLocationLoading(false);
          },
          (error) => {
            if (isMounted) {
              setDefaultLocation();
            }
          },
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 10000
          }
        );
      } catch (error) {
        if (isMounted) {
          setDefaultLocation();
        }
      }
    };

    initializeLocation();

    return () => {
      isMounted = false;
    };
  }, []);

  const setDefaultLocation = () => {
    const defaultLocation: [number, number] = [29.0100, 41.0082]; // İstanbul
    setCurrentCoordinates(defaultLocation);
    setLocationPermissionGranted(true);
    setIsLocationLoading(false);
  };

  const toggleLayers = () => {
    const toValue = layersExpanded ? 0 : 1;
    setLayersExpanded(!layersExpanded);
    
    Animated.timing(layersAnimation, {
      toValue,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const handleStyleChange = (styleId: string) => {
    setCurrentStyle(styleId);
    setLayersExpanded(false);
    Animated.timing(layersAnimation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const getCurrentStyleInfo = () => {
    return mapStyles.find(style => style.id === currentStyle);
  };

  const togglePitch = () => {
    const newPitch = mapPitch === 0 ? 60 : 0;
    setMapPitch(newPitch);
  };

  // Konum butonuna basınca konuma git
  const goToUserLocation = () => {
    Geolocation.getCurrentPosition(
      (position) => {
        const userLocation: [number, number] = [
          Number(position.coords.longitude),
          Number(position.coords.latitude)
        ];

        // Force update: Önce farklı bir koordinat, sonra gerçek koordinat
        setCurrentCoordinates([userLocation[0] + 0.0001, userLocation[1] + 0.0001]);

        // 50ms sonra gerçek koordinata git (animasyonlu)
        setTimeout(() => {
          setCurrentCoordinates(userLocation);
        }, 50);
      },
      (error) => {
        if (onLocationError) {
          onLocationError('Konumunuz alınamadı. GPS açık mı kontrol edin.');
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000
      }
    );
  };

  const handleRegionDidChange = React.useCallback(async () => {
    if (!mapMounted || !mapRef.current) return;

    try {
      // Timeout ile güvenli bir şekilde center'ı al
      const centerPromise = mapRef.current.getCenter();
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Timeout')), 1000)
      );

      const center = await Promise.race([centerPromise, timeoutPromise]);

      if (center && Array.isArray(center) && center.length >= 2) {
        const [longitude, latitude] = center;
        if (typeof longitude === 'number' && typeof latitude === 'number' &&
            !isNaN(longitude) && !isNaN(latitude) &&
            longitude >= -180 && longitude <= 180 &&
            latitude >= -90 && latitude <= 90) {
          // Sadece display için koordinatları güncelle
          setCrosshairCoordinates([Number(longitude), Number(latitude)]);

          // Callback varsa çağır
          if (onLocationSelect) {
            onLocationSelect([Number(longitude), Number(latitude)]);
          }
        }
      }
    } catch (error) {
      // Sessizce devam et - timeout veya diğer hatalar
    }
  }, [mapMounted, onLocationSelect]);

  // Modal'larda loading state'i gösterme
  if (isLocationLoading && showLocationButton) {
    return (
      <View style={[styles.loadingContainer, style]}>
        <View style={styles.loadingContent}>
          <Icon name="map" size={40} color={theme.colors.primary} />
          <Text style={styles.loadingText}>Harita yükleniyor...</Text>
          <Text style={styles.loadingSubtext}>Konum bilgisi alınıyor</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {/* Mapbox MapView */}
      <Mapbox.MapView
        ref={mapRef}
        style={styles.map}
        styleURL={getCurrentStyleInfo()?.styleURL || 'mapbox://styles/mapbox/streets-v12'}
        zoomEnabled={true}
        scrollEnabled={true}
        pitchEnabled={true}
        rotateEnabled={true}
        compassEnabled={false}
        scaleBarEnabled={false}
        logoEnabled={false}
        attributionEnabled={false}
        onDidFinishLoadingMap={() => setMapMounted(true)}
        onCameraChanged={handleRegionDidChange}
      >
        {/* Camera */}
        <Mapbox.Camera
          centerCoordinate={[Number(currentCoordinates[0]), Number(currentCoordinates[1])]}
          zoomLevel={currentZoom}
          pitch={mapPitch}
          heading={mapBearing}
          animationMode="easeTo"
          animationDuration={400}
        />

        {/* User location puck */}
        {locationPermissionGranted && showLocationButton && (
          <Mapbox.LocationPuck
            visible={true}
            pulsing={{ isEnabled: false }}
            puckBearingEnabled={true}
            puckBearing="heading"
          />
        )}
      </Mapbox.MapView>

      {/* Center Crosshair */}
      {showCrosshair && (
        <View style={styles.crosshair}>
          <View style={styles.crosshairHorizontal} />
          <View style={styles.crosshairVertical} />
          <View style={styles.crosshairCenter} />
        </View>
      )}

      {/* Crosshair Coordinates */}
      {showCoordinates && (
        <View style={styles.locationInfo}>
          <Text style={styles.locationText}>
            {crosshairCoordinates && crosshairCoordinates[0] !== undefined && crosshairCoordinates[1] !== undefined
              ? `${crosshairCoordinates[1].toFixed(6)}°K, ${crosshairCoordinates[0].toFixed(6)}°D`
              : 'Koordinatlar yükleniyor...'}
          </Text>
        </View>
      )}

      {/* Layer Selector */}
      {showLayerSelector && (
        <View style={styles.layerSelectorContainer}>
          <TouchableOpacity 
            style={styles.mainLayerButton}
            onPress={toggleLayers}
          >
            <Icon name="layers" size={20} color={theme.colors.text} />
          </TouchableOpacity>

          <Animated.View 
            style={[
              styles.layerMenuContainer,
              {
                opacity: layersAnimation,
                transform: [{
                  translateY: layersAnimation.interpolate({
                    inputRange: [0, 1],
                    outputRange: [-10, 0],
                  }),
                }],
                pointerEvents: layersExpanded ? 'auto' : 'none',
              }
            ]}
          >
            {mapStyles.map((style) => (
              <Animated.View key={style.id}>
                <TouchableOpacity
                  style={[
                    styles.layerMenuItem,
                    currentStyle === style.id && styles.layerMenuItemActive
                  ]}
                  onPress={() => handleStyleChange(style.id)}
                >
                  <Icon 
                    name={style.icon} 
                    size={16} 
                    color={currentStyle === style.id ? '#FFFFFF' : theme.colors.text} 
                  />
                </TouchableOpacity>
              </Animated.View>
            ))}
          </Animated.View>
        </View>
      )}

      {/* 3D Controls */}
      {show3DToggle && (
        <View style={styles.fab3DContainer}>
          <TouchableOpacity
            style={[
              styles.fab3DButton,
              mapPitch > 0 && styles.fab3DButtonActive
            ]}
            onPress={togglePitch}
          >
            <Icon
              name="3d-rotation"
              size={16}
              color={mapPitch > 0 ? '#FFFFFF' : theme.colors.primary}
            />
          </TouchableOpacity>
        </View>
      )}

      {/* Geolocation Button */}
      {showLocationButton && (
        <View style={styles.geolocateContainer}>
          <TouchableOpacity
            style={styles.geolocateButton}
            onPress={goToUserLocation}
            activeOpacity={0.7}
          >
            <Icon
              name="my-location"
              size={20}
              color={theme.colors.primary}
            />
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    overflow: 'hidden',
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  loadingText: {
    fontSize: theme.typography.lg,
    fontWeight: theme.typography.semibold,
    color: theme.colors.text,
    marginTop: theme.spacing.md,
  },
  loadingSubtext: {
    fontSize: theme.typography.sm,
    fontWeight: theme.typography.normal,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  map: {
    flex: 1,
    overflow: 'hidden',
  },
  // Sniper Crosshair
  crosshair: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 120,
    height: 120,
    marginTop: -60,
    marginLeft: -60,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  crosshairHorizontal: {
    position: 'absolute',
    width: 120,
    height: 1,
    backgroundColor: '#FF0000',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
    elevation: 5,
  },
  crosshairVertical: {
    position: 'absolute',
    width: 1,
    height: 120,
    backgroundColor: '#FF0000',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
    elevation: 5,
  },
  crosshairCenter: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#FF0000',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
    elevation: 5,
  },
  locationInfo: {
    position: 'absolute',
    top: 20,
    alignSelf: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignItems: 'center',
  },
  locationText: {
    fontSize: 10,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  layerSelectorContainer: {
    position: 'absolute',
    top: 120,
    right: theme.spacing.md,
    alignItems: 'center',
  },
  mainLayerButton: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  layerMenuContainer: {
    position: 'absolute',
    top: 50,
    right: 0,
    gap: 8,
  },
  layerMenuItem: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  layerMenuItemActive: {
    backgroundColor: theme.colors.primary,
  },
  fab3DContainer: {
    position: 'absolute',
    top: 70,
    right: theme.spacing.md,
  },
  fab3DButton: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  fab3DButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  geolocateContainer: {
    position: 'absolute',
    top: 20,
    right: theme.spacing.md,
  },
  geolocateButton: {
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
});

export default MapComponent;
