import React from 'react';
import { View, Text, ActivityIndicator, StyleSheet } from 'react-native';
import { theme } from '@fishivo/shared';

interface WeatherLoadingStateProps {
  message?: string;
}

const WeatherLoadingState: React.FC<WeatherLoadingStateProps> = ({ message = 'Hava durumu yükleniyor...' }) => (
  <View style={styles.loadingContainer}>
    <ActivityIndicator size="large" color={theme.colors.primary} />
    <Text style={styles.loadingText}>{message}</Text>
  </View>
);

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.lg,
    color: theme.colors.textSecondary,
  },
});

export default WeatherLoadingState; 