import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  TextInput,
  ActivityIndicator,
} from 'react-native';
import Icon from './Icon';
import BottomSheetModal from './BottomSheetModal';
import { theme } from '@fishivo/shared';
import { apiService } from '@fishivo/services';

interface FishingTechniqueSelectorModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (technique: string) => void;
  selectedTechnique?: string;
}

interface FishingTechnique {
  id: number;
  name: string;
  icon: string;
  description: string;
  difficulty: string;
  best_for: string[];
  equipment: string[];
  water_types: string[];
  season: string;
  tips: string[];
}

const FishingTechniqueSelectorModal: React.FC<FishingTechniqueSelectorModalProps> = ({
  visible,
  onClose,
  onSelect,
  selectedTechnique,
}) => {
  const [searchText, setSearchText] = useState('');
  const [techniques, setTechniques] = useState<FishingTechnique[]>([]);
  const [filteredTechniques, setFilteredTechniques] = useState<FishingTechnique[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadTechniques = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await apiService.getFishingTechniques();
      setTechniques(data);
      setFilteredTechniques(data);
    } catch (err) {
      console.error('Error loading fishing techniques:', err);
      setError('Balıkçılık teknikleri yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  };

  const searchTechniques = async (query: string) => {
    if (query.trim() === '') {
      setFilteredTechniques(techniques);
      return;
    }

    try {
      setIsLoading(true);
      const data = await apiService.searchFishingTechniques(query);
      setFilteredTechniques(data);
    } catch (err) {
      console.error('Error searching techniques:', err);
      // Fallback to local filtering
      const filtered = techniques.filter(technique =>
        technique.name.toLowerCase().includes(query.toLowerCase()) ||
        technique.description.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredTechniques(filtered);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchTechniques(searchText);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchText, techniques]);

  useEffect(() => {
    if (visible) {
      setSearchText('');
      loadTechniques();
    }
  }, [visible]);

  const handleSelect = (technique: FishingTechnique) => {
    onSelect(technique.name);
    onClose();
  };

  const renderTechniqueItem = ({ item }: { item: FishingTechnique }) => (
    <TouchableOpacity
      style={[
        styles.techniqueItem,
        selectedTechnique === item.name && styles.selectedTechniqueItem
      ]}
      onPress={() => handleSelect(item)}
      activeOpacity={0.7}
    >
      <View style={styles.techniqueContent}>
        <View style={styles.techniqueIcon}>
          <Icon name={item.icon || "target"} size={20} color={theme.colors.primary} />
        </View>
        <View style={styles.techniqueTextContainer}>
          <Text style={[
            styles.techniqueName,
            selectedTechnique === item.name && styles.selectedTechniqueName
          ]}>
            {item.name}
          </Text>
          <Text style={styles.techniqueSubtext}>
            {item.description}
          </Text>
        </View>
      </View>
      {selectedTechnique === item.name && (
        <Icon name="check" size={16} color={theme.colors.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <BottomSheetModal
      visible={visible}
      onClose={onClose}
      maxHeight="80%"
    >
      <Text style={styles.title}>Balıkçılık Tekniği Seç</Text>
      <Text style={styles.subtitle}>Kullandığınız balıkçılık tekniğini seçin</Text>

      {/* Arama */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={16} color={theme.colors.textSecondary} />
        <TextInput
          style={styles.searchInput}
          value={searchText}
          onChangeText={setSearchText}
          placeholder="Teknik ara..."
          placeholderTextColor={theme.colors.textSecondary}
        />
        {searchText.length > 0 && (
          <TouchableOpacity onPress={() => setSearchText('')}>
            <Icon name="x" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        )}
      </View>

      {/* Teknikler Listesi */}
      <View style={styles.listContainer}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Teknikler yükleniyor...</Text>
          </View>
        ) : error ? (
          <View style={styles.errorContainer}>
            <Icon name="alert-circle" size={32} color={theme.colors.error} />
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={loadTechniques}>
              <Text style={styles.retryText}>Tekrar Dene</Text>
            </TouchableOpacity>
          </View>
        ) : filteredTechniques.length > 0 ? (
          <FlatList
            data={filteredTechniques}
            renderItem={renderTechniqueItem}
            keyExtractor={(item) => item.id.toString()}
            showsVerticalScrollIndicator={false}
            style={styles.techniquesList}
          />
        ) : (
          <View style={styles.noResultsContainer}>
            <Icon name="search" size={32} color={theme.colors.textSecondary} />
            <Text style={styles.noResultsText}>Teknik bulunamadı</Text>
            <Text style={styles.noResultsSubtext}>
              Farklı anahtar kelimeler deneyin
            </Text>
          </View>
        )}
      </View>

      <TouchableOpacity
        style={styles.cancelButton}
        onPress={onClose}
        activeOpacity={0.7}
      >
        <Text style={styles.cancelText}>İptal</Text>
      </TouchableOpacity>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: theme.typography.xl,
    fontWeight: theme.typography.bold,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingHorizontal: theme.spacing.sm,
    marginBottom: theme.spacing.lg,
    gap: theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: theme.typography.base,
    color: theme.colors.text,
    paddingVertical: theme.spacing.sm,
  },
  listContainer: {
    flex: 1,
    maxHeight: 400,
  },
  techniquesList: {
    flex: 1,
  },
  techniqueItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  selectedTechniqueItem: {
    backgroundColor: `${theme.colors.primary}15`,
    borderColor: theme.colors.primary,
  },
  techniqueContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  techniqueIcon: {
    width: 36,
    height: 36,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: `${theme.colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  techniqueName: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    flex: 1,
  },
  selectedTechniqueName: {
    color: theme.colors.primary,
  },
  noResultsContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
  },
  noResultsText: {
    fontSize: theme.typography.lg,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xs,
  },
  noResultsSubtext: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    marginTop: theme.spacing.lg,
  },
  cancelText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  techniqueTextContainer: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  techniqueSubtext: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
  },
  loadingText: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xl,
  },
  errorText: {
    fontSize: theme.typography.base,
    color: theme.colors.error,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
  },
  retryText: {
    fontSize: theme.typography.base,
    color: '#FFFFFF',
    fontWeight: theme.typography.medium,
  },
});

export default FishingTechniqueSelectorModal; 