import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Alert,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import Icon from './Icon';
import BottomSheetModal from './BottomSheetModal';
import { theme } from '@fishivo/shared';
// Mock JSON dosyası kaldırıldı - API'den gelecek

interface WeatherSelectorModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (weather: string) => void;
  selectedWeather?: string;
}

interface WeatherOption {
  id: string;
  name: string;
  icon: string;
  description: string;
}

const WeatherSelectorModal: React.FC<WeatherSelectorModalProps> = ({
  visible,
  onClose,
  onSelect,
  selectedWeather,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [currentWeather, setCurrentWeather] = useState<string | null>(null);
  const [locationError, setLocationError] = useState(false);
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customWeather, setCustomWeather] = useState('');

  const weatherOptions: WeatherOption[] = [
    { id: '1', name: 'Güneşli', icon: 'sun', description: 'Açık ve güneşli hava' },
    { id: '2', name: 'Bulutlu', icon: 'cloud', description: 'Bulutlu hava' },
    { id: '3', name: 'Yağmurlu', icon: 'cloud-rain', description: 'Yağışlı hava' },
    { id: '4', name: 'Rüzgarlı', icon: 'wind', description: 'Rüzgarlı hava' },
    { id: '5', name: 'Sisli', icon: 'cloud-fog', description: 'Sisli hava' },
    { id: '6', name: 'Karlı', icon: 'cloud-snow', description: 'Karlı hava' }
  ];

  useEffect(() => {
    if (visible) {
      setLocationError(false);
      setCurrentWeather(null);
      setShowCustomInput(false);
      setCustomWeather('');
      fetchCurrentWeather();
    }
  }, [visible]);

  const fetchCurrentWeather = async () => {
    setIsLoading(true);
    
    try {
      // Gerçek weather API çağrısı yapılacak
      // TODO: Geolocation + Weather API implementasyonu
      console.log('Weather API çağrısı yapılacak');
      
      // Geçici mock implementation
      await new Promise(resolve => setTimeout(resolve, 1500));
      const availableConditions = ['Güneşli', 'Bulutlu', 'Yağmurlu', 'Rüzgarlı', 'Sisli'];
      const defaultWeather = availableConditions[0];
      
      setCurrentWeather(defaultWeather);
    } catch (error) {

      setLocationError(true);
    } finally {
      setIsLoading(false);
    }
  };

  const handleWeatherSelect = (weather: string) => {
    onSelect(weather);
    onClose();
  };

  const handleUseCurrentWeather = () => {
    if (currentWeather) {
      handleWeatherSelect(currentWeather);
    }
  };

  const handleRetryWeather = () => {
    fetchCurrentWeather();
  };

  const handleAddCustomWeather = () => {
    if (customWeather.trim()) {
      handleWeatherSelect(customWeather.trim());
    }
  };

  const handleShowCustomInput = () => {
    setShowCustomInput(true);
  };

  const renderWeatherOption = ({ item }: { item: WeatherOption }) => {
    const isSelected = selectedWeather === item.name;
    
    return (
      <TouchableOpacity
        style={[
          styles.weatherItem,
          isSelected && styles.selectedWeatherItem
        ]}
        onPress={() => handleWeatherSelect(item.name)}
        activeOpacity={0.7}
      >
        <View style={styles.weatherContent}>
          <View style={styles.weatherIcon}>
            <Icon name={item.icon} size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.weatherInfo}>
            <Text style={[
              styles.weatherName,
              isSelected && styles.selectedWeatherName
            ]}>
              {item.name}
            </Text>
            <Text style={styles.weatherDescription}>
              {item.description}
            </Text>
          </View>
        </View>
        {isSelected && (
          <Icon name="check" size={16} color={theme.colors.primary} />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <BottomSheetModal
      visible={visible}
      onClose={onClose}
      maxHeight="80%"
    >
      <Text style={styles.title}>Hava Durumu</Text>
      <Text style={styles.subtitle}>Mevcut hava durumunu seçin</Text>

      {/* Otomatik Hava Durumu */}
      <View style={styles.currentWeatherContainer}>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Mevcut hava durumu alınıyor...</Text>
          </View>
        ) : currentWeather ? (
          <TouchableOpacity
            style={styles.currentWeatherCard}
            onPress={handleUseCurrentWeather}
            activeOpacity={0.7}
          >
            <View style={styles.currentWeatherIcon}>
              <Icon name="map-pin" size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.currentWeatherInfo}>
              <Text style={styles.currentWeatherTitle}>Mevcut Konum</Text>
              <Text style={styles.currentWeatherValue}>{currentWeather}</Text>
            </View>
            <Icon name="chevron-right" size={16} color={theme.colors.textSecondary} />
          </TouchableOpacity>
        ) : locationError ? (
          <View style={styles.errorContainer}>
            <Icon name="alert-circle" size={24} color={theme.colors.error} />
            <View style={styles.errorInfo}>
              <Text style={styles.errorText}>Hava durumu alınamadı</Text>
              <Text style={styles.errorSubtext}>Manuel olarak seçin</Text>
            </View>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={handleRetryWeather}
            >
              <Icon name="refresh-cw" size={16} color={theme.colors.primary} />
            </TouchableOpacity>
          </View>
        ) : null}
      </View>

      {/* Manuel Seçenekler */}
      <View style={styles.manualSection}>
        <Text style={styles.sectionTitle}>Manuel Seçim</Text>
        <View style={styles.listContainer}>
          <FlatList
            data={weatherOptions}
            renderItem={renderWeatherOption}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            style={styles.weatherList}
          />
        </View>
      </View>

      {/* Özel Hava Durumu */}
      <View style={styles.customSection}>
        <Text style={styles.sectionTitle}>Özel Hava Durumu</Text>
        {showCustomInput ? (
          <View style={styles.customInputContainer}>
            <TextInput
              style={styles.customInput}
              value={customWeather}
              onChangeText={setCustomWeather}
              placeholder="Hava durumunu yazın (örn: Hafif rüzgarlı)"
              placeholderTextColor={theme.colors.textSecondary}
              autoFocus
            />
            <View style={styles.customInputButtons}>
              <TouchableOpacity
                style={styles.customCancelButton}
                onPress={() => setShowCustomInput(false)}
                activeOpacity={0.7}
              >
                <Text style={styles.customCancelText}>İptal</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.customAddButton,
                  !customWeather.trim() && styles.customAddButtonDisabled
                ]}
                onPress={handleAddCustomWeather}
                activeOpacity={0.7}
                disabled={!customWeather.trim()}
              >
                <Text style={[
                  styles.customAddText,
                  !customWeather.trim() && styles.customAddTextDisabled
                ]}>
                  Ekle
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.addCustomButton}
            onPress={handleShowCustomInput}
            activeOpacity={0.7}
          >
            <Icon name="plus" size={16} color={theme.colors.primary} />
            <Text style={styles.addCustomButtonText}>Özel Hava Durumu Ekle</Text>
          </TouchableOpacity>
        )}
      </View>

      <TouchableOpacity
        style={styles.cancelButton}
        onPress={onClose}
        activeOpacity={0.7}
      >
        <Text style={styles.cancelText}>İptal</Text>
      </TouchableOpacity>
    </BottomSheetModal>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: theme.typography.xl,
    fontWeight: theme.typography.bold,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.base,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  currentWeatherContainer: {
    marginBottom: theme.spacing.lg,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingVertical: theme.spacing.lg,
    gap: theme.spacing.sm,
  },
  loadingText: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  currentWeatherCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${theme.colors.primary}15`,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  currentWeatherIcon: {
    width: 36,
    height: 36,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  currentWeatherInfo: {
    flex: 1,
  },
  currentWeatherTitle: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
    marginBottom: 2,
  },
  currentWeatherValue: {
    fontSize: theme.typography.base,
    color: theme.colors.primary,
    fontWeight: theme.typography.semibold,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.error,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  errorInfo: {
    flex: 1,
    marginLeft: theme.spacing.sm,
  },
  errorText: {
    fontSize: theme.typography.sm,
    color: theme.colors.error,
    fontWeight: theme.typography.medium,
    marginBottom: 2,
  },
  errorSubtext: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
  },
  retryButton: {
    width: 32,
    height: 32,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: theme.colors.surface,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  manualSection: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: theme.spacing.sm,
  },
  listContainer: {
    flex: 1,
    maxHeight: 300,
  },
  weatherList: {
    flex: 1,
  },
  weatherItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  selectedWeatherItem: {
    backgroundColor: `${theme.colors.primary}15`,
    borderColor: theme.colors.primary,
  },
  weatherContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  weatherIcon: {
    width: 36,
    height: 36,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: `${theme.colors.primary}15`,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.sm,
  },
  weatherInfo: {
    flex: 1,
  },
  weatherName: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
    marginBottom: 2,
  },
  selectedWeatherName: {
    color: theme.colors.primary,
  },
  weatherDescription: {
    fontSize: theme.typography.sm,
    color: theme.colors.textSecondary,
  },
  cancelButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
    marginTop: theme.spacing.lg,
  },
  cancelText: {
    fontSize: theme.typography.base,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  // Custom Weather Styles
  customSection: {
    marginTop: theme.spacing.lg,
  },
  customInputContainer: {
    gap: theme.spacing.md,
  },
  customInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: theme.typography.base,
    color: theme.colors.text,
  },
  customInputButtons: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  customCancelButton: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.border,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
  },
  customCancelText: {
    fontSize: theme.typography.sm,
    color: theme.colors.text,
    fontWeight: theme.typography.medium,
  },
  customAddButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
  },
  customAddButtonDisabled: {
    backgroundColor: theme.colors.border,
  },
  customAddText: {
    fontSize: theme.typography.sm,
    color: '#FFFFFF',
    fontWeight: theme.typography.medium,
  },
  customAddTextDisabled: {
    color: theme.colors.textSecondary,
  },
  addCustomButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    gap: theme.spacing.xs,
  },
  addCustomButtonText: {
    fontSize: theme.typography.sm,
    color: theme.colors.primary,
    fontWeight: theme.typography.medium,
  },
});

export default WeatherSelectorModal; 