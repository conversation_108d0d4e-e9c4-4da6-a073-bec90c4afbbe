// Weather utilities - standalone functions without theme dependency

export const getConditionIcon = (condition: string): string => {
  switch (condition) {
    // Açık hava durumları
    case 'Açık': return 'sun';
    
    // Bulutlu hava durumları
    case 'Az Bulutlu':
    case 'Parçalı Bulutlu':
    case 'Kapalı':
    case 'Sisli': return 'cloud';
    
    // Yağmurlu hava durumları
    case 'Hafif Yağmurlu':
    case 'Yağmurlu':
    case 'Şiddetli Yağmurlu':
    case 'Sağanak Yağış': return 'cloud-rain';
    
    // Kar yağışı
    case 'Kar Yağışlı':
    case 'Karla Karışık Yağmur': return 'cloud-snow';
    
    // Fırtınalı hava
    case 'Gök Gürültülü':
    case 'Gök Gürültülü Yağmurlu':
    case 'Fırtınalı': return 'zap';
    
    // Puslu hava
    case 'Puslu': return 'cloud';
    
    default: return 'cloud';
  }
};

export const getRatingColor = (rating: string): string => {
  switch (rating) {
    case 'Mükemmel': return '#22C55E';
    case 'İyi': return '#10B981';
    case 'Orta': return '#F59E0B';
    case 'Kötü': return '#EF4444';
    default: return '#8E8E93'; // Default gray color
  }
};

export const getLocationIcon = (type: string): string => {
  switch (type) {
    case 'current': return 'navigation';
    case 'manual': return 'map-pin';
    case 'spot': return 'flag';
    default: return 'map-pin';
  }
};

export const getWindDirectionText = (degrees: number): string => {
  const directions = ['K', 'KKD', 'KD', 'DKD', 'D', 'DGD', 'GD', 'GKD', 'G', 'GKB', 'KB', 'KKB'];
  const index = Math.round(degrees / 30) % 12;
  return directions[index];
};

export const getUVIndexLevel = (uvIndex: number): string => {
  if (uvIndex <= 2) return 'Düşük';
  if (uvIndex <= 5) return 'Orta';
  if (uvIndex <= 7) return 'Yüksek';
  if (uvIndex <= 10) return 'Çok Yüksek';
  return 'Aşırı';
}; 