// Mock JSON dosyaları kaldırıldı - Geçici hardcoded values kullanılıyor
// TODO: API'den bu veriler alınacak

const unitDefinitions = {
  categories: {
    weight: {
      units: {
        kg: { symbol: 'kg', precision: 2, isBaseUnit: true },
        g: { symbol: 'g', precision: 0, conversionFactor: 1000 },
        lbs: { symbol: 'lbs', precision: 2, conversionFactor: 2.20462 },
        oz: { symbol: 'oz', precision: 1, conversionFactor: 35.274 }
      }
    },
    length: {
      units: {
        cm: { symbol: 'cm', precision: 1, isBaseUnit: true },
        m: { symbol: 'm', precision: 2, conversionFactor: 0.01 },
        inch: { symbol: 'in', precision: 1, conversionFactor: 0.393701 },
        ft: { symbol: 'ft', precision: 2, conversionFactor: 0.0328084 }
      }
    },
    temperature: {
      units: {
        celsius: { symbol: '°C', precision: 1, isBaseUnit: true },
        fahrenheit: { symbol: '°F', precision: 1 }
      }
    }
  },
  conversionRules: {
    displayRules: {
      autoSwitchThresholds: {
        weight: { g_to_kg: 1000, oz_to_lbs: 16 },
        length: { cm_to_m: 100, inch_to_ft: 12 }
      }
    }
  }
};

const userUnitPreferences = {
  defaultPreferences: {
    TR: {
      weight: 'kg',
      length: 'cm',
      distance: 'km',
      temperature: 'celsius',
      depth: 'meters',
      speed: 'kmh',
      pressure: 'hpa'
    }
  },
  userPreferences: []
};

export interface MeasurementValue {
  value: number;
  unit: string;
  originalUnit: string;
  displayValue: string;
}

export interface UserUnitPreferences {
  weight: string;
  length: string;
  distance: string;
  temperature: string;
  depth: string;
  speed: string;
  pressure: string;
}

// Kullanıcının ölçü birimi tercihlerini al
export const getUserUnitPreferences = (userId: string): UserUnitPreferences => {
  const userPrefs = userUnitPreferences.userPreferences.find(pref => pref.userId === userId);
  return userPrefs ? userPrefs.preferences : userUnitPreferences.defaultPreferences.TR;
};

// Ölçü birimi dönüştürme
export const convertUnit = (value: number, fromUnit: string, toUnit: string, category: string): number => {
  if (fromUnit === toUnit) return value;
  
  const categoryData = unitDefinitions.categories[category];
  if (!categoryData) return value;
  
  const fromUnitData = categoryData.units[fromUnit];
  const toUnitData = categoryData.units[toUnit];
  
  if (!fromUnitData || !toUnitData) return value;
  
  // Önce base unit'e çevir
  let baseValue = value;
  if (!fromUnitData.isBaseUnit) {
    if (category === 'temperature' && fromUnit === 'fahrenheit') {
      baseValue = (value - 32) * 5/9;
    } else if (fromUnitData.conversionFactor) {
      baseValue = value / fromUnitData.conversionFactor;
    }
  }
  
  // Base unit'ten hedef unit'e çevir
  let finalValue = baseValue;
  if (!toUnitData.isBaseUnit) {
    if (category === 'temperature' && toUnit === 'fahrenheit') {
      finalValue = (baseValue * 9/5) + 32;
    } else if (toUnitData.conversionFactor) {
      finalValue = baseValue * toUnitData.conversionFactor;
    }
  }
  
  return Math.round(finalValue * Math.pow(10, toUnitData.precision)) / Math.pow(10, toUnitData.precision);
};

// Ölçü değerini kullanıcının tercihlerine göre formatla
export const formatMeasurementValue = (measurement: any, category: string, userPrefs: UserUnitPreferences): string => {
  if (!measurement || typeof measurement === 'string') return measurement;
  
  const userPreferredUnit = userPrefs[category];
  if (measurement.unit === userPreferredUnit) {
    return measurement.displayValue;
  }
  
  // Dönüştürme yap
  const convertedValue = convertUnit(measurement.value, measurement.unit, userPreferredUnit, category);
  const categoryData = unitDefinitions.categories[category];
  const unitData = categoryData?.units[userPreferredUnit];
  
  if (unitData) {
    return `${convertedValue} ${unitData.symbol}`;
  }
  
  return measurement.displayValue;
};

// Ölçü birimi etiketini al
export const getUnitLabel = (category: string, userPrefs: UserUnitPreferences): string => {
  const unitKey = userPrefs[category];
  const categoryData = unitDefinitions.categories[category];
  if (categoryData && categoryData.units[unitKey]) {
    return categoryData.units[unitKey].symbol;
  }
  return unitKey;
};

// String formatından yeni formata dönüştür
export const convertStringToMeasurement = (value: string, category: string): MeasurementValue | null => {
  if (!value || typeof value !== 'string') return null;
  
  // "2.5 kg" formatından parse et
  const match = value.match(/^([\d.]+)\s*([a-zA-Z°]+)$/);
  if (!match) return null;
  
  const numValue = parseFloat(match[1]);
  const unit = match[2].toLowerCase().replace('°c', 'celsius').replace('°f', 'fahrenheit');
  
  return {
    value: numValue,
    unit: unit,
    originalUnit: unit,
    displayValue: value
  };
};

// Otomatik birim dönüştürme (1000g -> 1kg gibi)
export const autoConvertUnit = (value: number, unit: string, category: string): MeasurementValue => {
  const categoryData = unitDefinitions.categories[category];
  if (!categoryData) {
    return {
      value,
      unit,
      originalUnit: unit,
      displayValue: `${value} ${unit}`
    };
  }
  
  const conversionRules = unitDefinitions.conversionRules.displayRules.autoSwitchThresholds[category];
  let finalUnit = unit;
  let finalValue = value;
  
  if (conversionRules) {
    // Gram'dan kilogram'a dönüştürme
    if (unit === 'g' && value >= conversionRules.g_to_kg) {
      finalValue = value / 1000;
      finalUnit = 'kg';
    }
    // Ons'tan pound'a dönüştürme
    else if (unit === 'oz' && value >= conversionRules.oz_to_lbs) {
      finalValue = value / 16;
      finalUnit = 'lbs';
    }
    // Santimetre'den metre'ye dönüştürme
    else if (unit === 'cm' && value >= conversionRules.cm_to_m) {
      finalValue = value / 100;
      finalUnit = 'm';
    }
    // İnç'ten feet'e dönüştürme
    else if (unit === 'inch' && value >= conversionRules.inch_to_ft) {
      finalValue = value / 12;
      finalUnit = 'ft';
    }
  }
  
  const unitData = categoryData.units[finalUnit];
  const precision = unitData?.precision || 2;
  const roundedValue = Math.round(finalValue * Math.pow(10, precision)) / Math.pow(10, precision);
  const symbol = unitData?.symbol || finalUnit;
  
  return {
    value: roundedValue,
    unit: finalUnit,
    originalUnit: unit,
    displayValue: `${roundedValue} ${symbol}`
  };
}; 