/**
 * Location utility functions for Fishivo app
 */

export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

export interface MapboxCoordinates {
  latitude: number;
  longitude: number;
}

/**
 * Format location string from location object
 */
export function formatLocationString(location: Location): string {
  if (!location) return '';
  
  const parts: string[] = [];
  
  if (location.address) {
    parts.push(location.address);
  }
  
  if (location.city) {
    parts.push(location.city);
  }
  
  if (location.country) {
    parts.push(location.country);
  }
  
  // If no address components, use coordinates
  if (parts.length === 0) {
    return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`;
  }
  
  return parts.join(', ');
}

/**
 * Get Mapbox coordinates from location object
 */
export function getMapboxCoordinates(location: Location): MapboxCoordinates {
  if (!location) {
    throw new Error('Location is required');
  }
  
  return {
    latitude: location.latitude,
    longitude: location.longitude
  };
}

/**
 * Calculate distance between two coordinates using Haversine formula
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return distance;
}

/**
 * Convert degrees to radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Validate coordinates
 */
export function isValidCoordinates(latitude: number, longitude: number): boolean {
  return (
    typeof latitude === 'number' &&
    typeof longitude === 'number' &&
    latitude >= -90 &&
    latitude <= 90 &&
    longitude >= -180 &&
    longitude <= 180 &&
    !isNaN(latitude) &&
    !isNaN(longitude)
  );
}
