{"name": "@fishivo/utils", "version": "1.0.0", "description": "Shared utility functions for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "keywords": ["utils", "utilities", "helpers"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@fishivo/shared": "1.0.0"}, "devDependencies": {"@types/jest": "29.5.11", "@types/node": "20.11.5", "eslint": "8.56.0", "jest": "^29.2.1", "tsup": "^8.0.0", "typescript": "5.3.3"}}