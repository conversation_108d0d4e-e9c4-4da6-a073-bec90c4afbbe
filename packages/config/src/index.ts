/**
 * Configuration exports for Fishivo
 * Simplified configuration structure
 */

// API Configuration
export const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001',
  timeout: 10000,
  retries: 3,
};

// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
};

// App Configuration
export const APP_CONFIG = {
  name: 'Fishivo',
  version: '1.0.0',
  environment: process.env.NODE_ENV || 'development',
};

// Navigation Configuration
export const NAVIGATION_CONFIG = {
  initialRouteName: 'Home',
  headerShown: true,
};

// Theme Configuration
export const THEME_CONFIG = {
  darkMode: true,
  primaryColor: '#007AFF',
};

// Colors
export const COLORS = {
  primary: '#007AFF',
  secondary: '#5856D6',
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
};

// Fonts
export const FONTS = {
  regular: 'System',
  medium: 'System-Medium',
  bold: 'System-Bold',
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
};

// Constants
export const CONSTANTS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  SUPPORTED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
};

// Weather Constants
export const WEATHER_CONSTANTS = {
  UPDATE_INTERVAL: 30 * 60 * 1000, // 30 minutes
  CACHE_DURATION: 10 * 60 * 1000, // 10 minutes
};

// Fishing Constants
export const FISHING_CONSTANTS = {
  SPECIES: ['Bass', 'Trout', 'Pike', 'Salmon'],
  TECHNIQUES: ['Spinning', 'Fly Fishing', 'Trolling'],
};

// Environment Configuration
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',
};

// Validation Rules
export const VALIDATION_RULES = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
  },
};

// Feature Flags
export const FEATURE_FLAGS = {
  enablePushNotifications: true,
  enableSocialLogin: true,
  enableProFeatures: true,
};