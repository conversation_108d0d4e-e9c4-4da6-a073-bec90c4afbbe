import { useState, useCallback, useEffect } from 'react';
import { supabase } from '@fishivo/services';
import { User, Session } from '@supabase/supabase-js';

interface SupabaseState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
}

interface AuthCredentials {
  email: string;
  password: string;
}

interface SignUpData extends AuthCredentials {
  options?: {
    data?: {
      full_name?: string;
      username?: string;
      [key: string]: any;
    };
  };
}

export const useSupabase = () => {
  const [supabaseState, setSupabaseState] = useState<SupabaseState>({
    user: null,
    session: null,
    isLoading: true,
    error: null,
    isInitialized: false,
  });

  useEffect(() => {
    // Get initial session
    const initializeAuth = async () => {
      try {
        console.log('🔐 Initializing Supabase auth...');

        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('❌ Error getting session:', error);
          setSupabaseState(prev => ({
            ...prev,
            error: error.message,
            isLoading: false,
            isInitialized: true,
          }));
          return;
        }

        console.log('✅ Supabase auth initialized:', { hasSession: !!session });

        setSupabaseState(prev => ({
          ...prev,
          user: session?.user || null,
          session,
          isLoading: false,
          isInitialized: true,
        }));
      } catch (error) {
        const message = error instanceof Error ? error.message : 'Auth initialization failed';
        console.error('❌ Auth initialization error:', error);
        setSupabaseState(prev => ({
          ...prev,
          error: message,
          isLoading: false,
          isInitialized: true,
        }));
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.email);

        setSupabaseState(prev => ({
          ...prev,
          user: session?.user || null,
          session,
          isLoading: false,
          error: null,
        }));
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = useCallback(async (credentials: AuthCredentials) => {
    try {
      setSupabaseState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🔑 Signing in user:', credentials.email);

      const { data, error } = await supabase.auth.signInWithPassword(credentials);

      if (error) {
        throw error;
      }

      console.log('✅ User signed in successfully');

      return data;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Sign in failed';
      console.error('❌ Sign in error:', error);
      setSupabaseState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const signUp = useCallback(async (signUpData: SignUpData) => {
    try {
      setSupabaseState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('📝 Signing up user:', signUpData.email);

      const { data, error } = await supabase.auth.signUp(signUpData);

      if (error) {
        throw error;
      }

      console.log('✅ User signed up successfully');

      return data;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Sign up failed';
      console.error('❌ Sign up error:', error);
      setSupabaseState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const signOut = useCallback(async () => {
    try {
      setSupabaseState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🚪 Signing out user...');

      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      console.log('✅ User signed out successfully');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Sign out failed';
      console.error('❌ Sign out error:', error);
      setSupabaseState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const signInWithOAuth = useCallback(async (provider: 'google' | 'facebook' | 'github') => {
    try {
      setSupabaseState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🔗 Signing in with OAuth provider:', provider);

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: window.location.origin,
        },
      });

      if (error) {
        throw error;
      }

      console.log('✅ OAuth sign in initiated');

      return data;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'OAuth sign in failed';
      console.error('❌ OAuth sign in error:', error);
      setSupabaseState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const resetPassword = useCallback(async (email: string) => {
    try {
      setSupabaseState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🔄 Resetting password for:', email);

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        throw error;
      }

      console.log('✅ Password reset email sent');

      setSupabaseState(prev => ({
        ...prev,
        isLoading: false,
      }));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Password reset failed';
      console.error('❌ Password reset error:', error);
      setSupabaseState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const updatePassword = useCallback(async (newPassword: string) => {
    try {
      setSupabaseState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🔄 Updating password...');

      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw error;
      }

      console.log('✅ Password updated successfully');

      setSupabaseState(prev => ({
        ...prev,
        isLoading: false,
      }));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Password update failed';
      console.error('❌ Password update error:', error);
      setSupabaseState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const refreshSession = useCallback(async () => {
    try {
      console.log('🔄 Refreshing session...');

      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        throw error;
      }

      console.log('✅ Session refreshed');

      return data;
    } catch (error) {
      console.error('❌ Session refresh error:', error);
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setSupabaseState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    // State
    user: supabaseState.user,
    session: supabaseState.session,
    isLoading: supabaseState.isLoading,
    error: supabaseState.error,
    isInitialized: supabaseState.isInitialized,
    isAuthenticated: !!supabaseState.user,

    // Auth methods
    signIn,
    signUp,
    signOut,
    signInWithOAuth,
    resetPassword,
    updatePassword,
    refreshSession,

    // Utilities
    clearError,

    // Direct access to supabase client for advanced usage
    client: supabase,
  };
};
