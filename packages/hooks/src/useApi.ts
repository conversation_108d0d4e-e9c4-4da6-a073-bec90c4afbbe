import { useState, useCallback, useRef } from 'react';
import { apiService } from '@fishivo/services';

// Local type definitions to avoid export issues
interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

interface ApiState {
  isLoading: boolean;
  error: string | null;
  lastRequestTime: number | null;
}

interface ApiRequestOptions {
  skipLoading?: boolean;
  retries?: number;
  timeout?: number;
}

export const useApi = (): {
  isLoading: boolean;
  error: string | null;
  lastRequestTime: number | null;
  auth: any;
  posts: any;
  likes: any;
  follows: any;
  users: any;
  weather: any;
  locations: any;
  upload: any;
  species: any;
  spots: any;
  makeRequest: <T>(requestFn: () => Promise<T>, options?: ApiRequestOptions) => Promise<T>;
  cancelRequest: () => void;
  clearError: () => void;
} => {
  const [apiState, setApiState] = useState<ApiState>({
    isLoading: false,
    error: null,
    lastRequestTime: null,
  });

  const abortControllerRef = useRef<AbortController | null>(null);

  const makeRequest = useCallback(async <T>(
    requestFn: () => Promise<T>,
    options: ApiRequestOptions = {}
  ): Promise<T> => {
    const { skipLoading = false, retries = 3 } = options;

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    try {
      if (!skipLoading) {
        setApiState(prev => ({
          ...prev,
          isLoading: true,
          error: null,
        }));
      }

      console.log('🌐 Making API request...');

      let lastError: Error | null = null;
      
      for (let attempt = 1; attempt <= retries; attempt++) {
        try {
          const result = await requestFn();
          
          console.log('✅ API request successful');

          setApiState(prev => ({
            ...prev,
            isLoading: false,
            lastRequestTime: Date.now(),
          }));

          return result;
        } catch (error) {
          lastError = error instanceof Error ? error : new Error('Unknown error');
          
          if (attempt < retries) {
            console.warn(`⚠️ API request failed (attempt ${attempt}/${retries}), retrying...`);
            // Wait before retry with exponential backoff
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
          }
        }
      }

      throw lastError;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'API isteği başarısız oldu';
      console.error('❌ API request failed:', error);
      
      setApiState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
        lastRequestTime: Date.now(),
      }));
      
      throw error;
    }
  }, []);

  // Authentication API methods
  const auth = {
    getCurrentUser: useCallback(async () => {
      return makeRequest(() => apiService.getCurrentUser());
    }, [makeRequest]),

    updateProfile: useCallback(async (profileData: any) => {
      return makeRequest(() => apiService.updateProfile(profileData));
    }, [makeRequest]),

    getUserProfile: useCallback(async (userId: string) => {
      return makeRequest(() => apiService.getUserProfile(userId));
    }, [makeRequest]),
  };

  // Posts/Catches API methods
  const posts = {
    getCatches: useCallback(async (page = 1, limit = 10, filters?: any) => {
      return makeRequest(() => apiService.getCatches(page, limit, filters));
    }, [makeRequest]),

    getPostById: useCallback(async (postId: number) => {
      return makeRequest(() => apiService.getPostById(postId));
    }, [makeRequest]),

    createPost: useCallback(async (postData: any) => {
      return makeRequest(() => apiService.createPost(postData));
    }, [makeRequest]),
  };

  // Likes API methods
  const likes = {
    isPostLiked: useCallback(async (postId: number) => {
      return makeRequest(() => apiService.isPostLiked(postId), { skipLoading: true });
    }, [makeRequest]),

    likePost: useCallback(async (postId: number) => {
      return makeRequest(() => apiService.likePost(postId));
    }, [makeRequest]),

    unlikePost: useCallback(async (postId: number) => {
      return makeRequest(() => apiService.unlikePost(postId));
    }, [makeRequest]),

    getLikeCount: useCallback(async (postId: number) => {
      const counts = await makeRequest(() => apiService.getBatchLikeCounts([postId]), { skipLoading: true });
      return counts[postId] || 0;
    }, [makeRequest]),

    getBatchLikeCounts: useCallback(async (postIds: number[]) => {
      return makeRequest(() => apiService.getBatchLikeCounts(postIds), { skipLoading: true });
    }, [makeRequest]),
  };

  // Follow API methods
  const follows = {
    isFollowing: useCallback(async (userId: string) => {
      return makeRequest(() => apiService.isFollowing(userId), { skipLoading: true });
    }, [makeRequest]),

    followUser: useCallback(async (userId: string) => {
      return makeRequest(() => apiService.followUser(userId));
    }, [makeRequest]),

    unfollowUser: useCallback(async (userId: string) => {
      return makeRequest(() => apiService.unfollowUser(userId));
    }, [makeRequest]),

    getUserSocialCounts: useCallback(async (userId: string) => {
      return makeRequest(() => apiService.getUserSocialCounts(userId), { skipLoading: true });
    }, [makeRequest]),
  };

  // Users API methods
  const users = {
    searchUsers: useCallback(async (query: string, page = 1, limit = 10) => {
      return makeRequest(() => apiService.searchUsers(query, page, limit));
    }, [makeRequest]),

    getUserCatches: useCallback(async (userId: string) => {
      return makeRequest(() => apiService.getUserCatches(userId));
    }, [makeRequest]),

    getBlockedUsers: useCallback(async (page = 1, limit = 20) => {
      return makeRequest(() => apiService.getBlockedUsers(page, limit));
    }, [makeRequest]),

    blockUser: useCallback(async (userId: string) => {
      return makeRequest(() => apiService.blockUser(userId));
    }, [makeRequest]),

    unblockUser: useCallback(async (userId: string) => {
      return makeRequest(() => apiService.unblockUser(userId));
    }, [makeRequest]),
  };

  // Weather API methods
  const weather = {
    getCurrentWeather: useCallback(async (lat: number, lon: number) => {
      return makeRequest(() => apiService.getCurrentWeather(lat, lon));
    }, [makeRequest]),

    getWeatherForecast: useCallback(async (lat: number, lon: number, days = 5) => {
      return makeRequest(() => apiService.getWeatherForecast(lat, lon, days));
    }, [makeRequest]),

    getLocationName: useCallback(async (lat: number, lon: number) => {
      return makeRequest(() => apiService.getLocationName(lat, lon));
    }, [makeRequest]),
  };

  // Location API methods
  const locations = {
    getUserLocations: useCallback(async () => {
      return makeRequest(() => apiService.getUserLocations());
    }, [makeRequest]),
  };

  // Upload API methods
  const upload = {
    uploadImage: useCallback(async (imageUri: string) => {
      return makeRequest(() => apiService.uploadImage(imageUri));
    }, [makeRequest]),
  };

  // Species API methods
  const species = {
    getSpecies: useCallback(async () => {
      return makeRequest(() => apiService.getSpecies());
    }, [makeRequest]),

    getSpeciesById: useCallback(async (id: number) => {
      return makeRequest(() => apiService.getSpeciesById(id));
    }, [makeRequest]),

    searchSpecies: useCallback(async (query: string) => {
      return makeRequest(() => apiService.searchSpecies(query));
    }, [makeRequest]),
  };

  // Spots API methods
  const spots = {
    getSpots: useCallback(async (page = 1, limit = 10) => {
      return makeRequest(() => apiService.getSpots(page, limit));
    }, [makeRequest]),

    getSpotById: useCallback(async (spotId: number) => {
      return makeRequest(() => apiService.getSpotById(spotId));
    }, [makeRequest]),

    searchSpots: useCallback(async (query: string, page = 1, limit = 10) => {
      return makeRequest(() => apiService.searchSpots(query, page, limit));
    }, [makeRequest]),
  };

  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setApiState(prev => ({
        ...prev,
        isLoading: false,
      }));
    }
  }, []);

  const clearError = useCallback(() => {
    setApiState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    // State
    isLoading: apiState.isLoading,
    error: apiState.error,
    lastRequestTime: apiState.lastRequestTime,

    // API methods grouped by domain
    auth,
    posts,
    likes,
    follows,
    users,
    weather,
    locations,
    upload,
    species,
    spots,

    // Utilities
    makeRequest,
    cancelRequest,
    clearError,
  };
};
