import { useState, useCallback, useEffect } from 'react';

interface NavigationState {
  currentRoute: string;
  previousRoute: string | null;
  params: Record<string, any>;
  canGoBack: boolean;
  isLoading: boolean;
}

interface NavigationOptions {
  replace?: boolean;
  reset?: boolean;
  params?: Record<string, any>;
}

// For web environment, we'll use the History API
export const useNavigation = () => {
  const [navigationState, setNavigationState] = useState<NavigationState>({
    currentRoute: window.location.pathname,
    previousRoute: null,
    params: {},
    canGoBack: window.history.length > 1,
    isLoading: false,
  });

  useEffect(() => {
    // Parse URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const params: Record<string, any> = {};
    
    urlParams.forEach((value, key) => {
      params[key] = value;
    });

    setNavigationState(prev => ({
      ...prev,
      currentRoute: window.location.pathname,
      params,
    }));

    // Listen for browser navigation events
    const handlePopState = (event: PopStateEvent) => {
      setNavigationState(prev => ({
        ...prev,
        currentRoute: window.location.pathname,
        canGoBack: window.history.length > 1,
      }));
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  const navigate = useCallback((route: string, options: NavigationOptions = {}) => {
    try {
      setNavigationState(prev => ({
        ...prev,
        isLoading: true,
      }));

      console.log('🧭 Navigating to:', route, options);

      const url = new URL(route, window.location.origin);
      
      // Add parameters to URL
      if (options.params) {
        Object.entries(options.params).forEach(([key, value]) => {
          url.searchParams.set(key, String(value));
        });
      }

      setNavigationState(prev => ({
        ...prev,
        previousRoute: prev.currentRoute,
        currentRoute: route,
        params: options.params || {},
        canGoBack: !options.reset,
        isLoading: false,
      }));

      if (options.replace) {
        window.history.replaceState(null, '', url.toString());
      } else if (options.reset) {
        window.history.replaceState(null, '', url.toString());
        // Clear history by replacing current state
      } else {
        window.history.pushState(null, '', url.toString());
      }

      console.log('✅ Navigation completed');
    } catch (error) {
      console.error('❌ Navigation error:', error);
      setNavigationState(prev => ({
        ...prev,
        isLoading: false,
      }));
      throw error;
    }
  }, []);

  const goBack = useCallback(() => {
    try {
      if (navigationState.canGoBack) {
        console.log('⬅️ Going back...');
        window.history.back();
      } else {
        console.warn('Cannot go back - no previous route');
      }
    } catch (error) {
      console.error('❌ Go back error:', error);
      throw error;
    }
  }, [navigationState.canGoBack]);

  const goForward = useCallback(() => {
    try {
      console.log('➡️ Going forward...');
      window.history.forward();
    } catch (error) {
      console.error('❌ Go forward error:', error);
      throw error;
    }
  }, []);

  const replace = useCallback((route: string, params?: Record<string, any>) => {
    navigate(route, { replace: true, params });
  }, [navigate]);

  const reset = useCallback((route: string, params?: Record<string, any>) => {
    navigate(route, { reset: true, params });
  }, [navigate]);

  const push = useCallback((route: string, params?: Record<string, any>) => {
    navigate(route, { params });
  }, [navigate]);

  // Common navigation methods for Fishivo app
  const navigateToHome = useCallback(() => {
    navigate('/');
  }, [navigate]);

  const navigateToProfile = useCallback((userId?: string) => {
    if (userId) {
      navigate(`/profile/${userId}`);
    } else {
      navigate('/profile');
    }
  }, [navigate]);

  const navigateToPost = useCallback((postId: string) => {
    navigate(`/post/${postId}`);
  }, [navigate]);

  const navigateToAddCatch = useCallback(() => {
    navigate('/add-catch');
  }, [navigate]);

  const navigateToMap = useCallback(() => {
    navigate('/map');
  }, [navigate]);

  const navigateToNotifications = useCallback(() => {
    navigate('/notifications');
  }, [navigate]);

  const navigateToSettings = useCallback(() => {
    navigate('/settings');
  }, [navigate]);

  const navigateToEditProfile = useCallback(() => {
    navigate('/edit-profile');
  }, [navigate]);

  const navigateToSearch = useCallback((query?: string) => {
    const params = query ? { q: query } : undefined;
    navigate('/search', { params });
  }, [navigate]);

  const navigateToSpot = useCallback((spotId: string) => {
    navigate(`/spot/${spotId}`);
  }, [navigate]);

  const navigateToSpecies = useCallback((speciesId: string) => {
    navigate(`/species/${speciesId}`);
  }, [navigate]);

  const navigateToAuth = useCallback((mode: 'login' | 'register' = 'login') => {
    navigate('/auth', { params: { mode } });
  }, [navigate]);

  const getParam = useCallback((key: string): string | null => {
    return navigationState.params[key] || null;
  }, [navigationState.params]);

  const setParams = useCallback((params: Record<string, any>) => {
    const url = new URL(window.location.href);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value === null || value === undefined) {
        url.searchParams.delete(key);
      } else {
        url.searchParams.set(key, String(value));
      }
    });

    window.history.replaceState(null, '', url.toString());

    setNavigationState(prev => ({
      ...prev,
      params: { ...prev.params, ...params },
    }));
  }, []);

  return {
    // Navigation state
    currentRoute: navigationState.currentRoute,
    previousRoute: navigationState.previousRoute,
    params: navigationState.params,
    canGoBack: navigationState.canGoBack,
    isLoading: navigationState.isLoading,

    // Basic navigation
    navigate,
    goBack,
    goForward,
    replace,
    reset,
    push,

    // App-specific navigation
    navigateToHome,
    navigateToProfile,
    navigateToPost,
    navigateToAddCatch,
    navigateToMap,
    navigateToNotifications,
    navigateToSettings,
    navigateToEditProfile,
    navigateToSearch,
    navigateToSpot,
    navigateToSpecies,
    navigateToAuth,

    // Parameter management
    getParam,
    setParams,
  };
};
