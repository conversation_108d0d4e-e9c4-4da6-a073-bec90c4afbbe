import { useState, useEffect, useCallback } from 'react';
import { apiService } from '@fishivo/services';

// Local types to avoid export issues
interface PostData {
  id: number;
  user_id: string;
  content: string;
  image_url?: string;
  location?: any;
  catch_details?: any;
  likes_count: number;
  comments_count: number;
  created_at: string;
  updated_at?: string;
  user?: any;
}

interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

interface PostsState {
  posts: PostData[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  total: number;
}

interface PostFilters {
  userId?: string;
  spotId?: number;
  tripId?: number;
}

export const usePosts = (filters?: PostFilters) => {
  const [postsState, setPostsState] = useState<PostsState>({
    posts: [],
    isLoading: false,
    error: null,
    hasMore: true,
    page: 1,
    total: 0,
  });

  useEffect(() => {
    loadPosts(true);
  }, [filters?.userId, filters?.spotId, filters?.tripId]);

  const loadPosts = useCallback(async (reset = false) => {
    try {
      const currentPage = reset ? 1 : postsState.page;

      setPostsState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        page: currentPage,
      }));

      console.log('🎣 Loading posts:', { page: currentPage, filters });

      // Use the real API method
      const response: PaginatedResponse<PostData> = await apiService.getCatches(
        currentPage,
        10,
        filters
      );

      console.log('✅ Posts loaded:', response);

      setPostsState(prev => ({
        ...prev,
        posts: reset ? response.items : [...prev.posts, ...response.items],
        isLoading: false,
        hasMore: response.hasMore,
        page: currentPage + 1,
        total: response.total,
      }));

      return response.items;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Posts yüklenirken hata oluştu';
      console.error('❌ Posts loading error:', error);
      setPostsState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, [filters, postsState.page]);

  const loadMorePosts = useCallback(async () => {
    if (!postsState.hasMore || postsState.isLoading) return;
    return await loadPosts(false);
  }, [postsState.hasMore, postsState.isLoading, loadPosts]);

  const createPost = useCallback(async (postData: any) => {
    try {
      setPostsState(prev => ({ ...prev, isLoading: true, error: null }));

      console.log('📝 Creating post:', postData);

      // Use the real API method
      const newPost = await apiService.createPost(postData);

      console.log('✅ Post created:', newPost);

      setPostsState(prev => ({
        ...prev,
        posts: [newPost, ...prev.posts],
        isLoading: false,
        total: prev.total + 1,
      }));

      return newPost;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Post oluşturulurken hata oluştu';
      console.error('❌ Post creation error:', error);
      setPostsState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getPostById = useCallback(async (postId: number) => {
    try {
      console.log('🔍 Getting post by ID:', postId);

      // Use the real API method
      const post = await apiService.getPostById(postId);

      console.log('✅ Post fetched:', post);

      return post;
    } catch (error) {
      console.error('❌ Get post by ID error:', error);
      throw error;
    }
  }, []);

  const refreshPosts = useCallback(async () => {
    return await loadPosts(true);
  }, [loadPosts]);

  const clearError = useCallback(() => {
    setPostsState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    posts: postsState.posts,
    isLoading: postsState.isLoading,
    error: postsState.error,
    hasMore: postsState.hasMore,
    total: postsState.total,
    loadPosts,
    loadMorePosts,
    createPost,
    getPostById,
    refreshPosts,
    clearError,
  };
};