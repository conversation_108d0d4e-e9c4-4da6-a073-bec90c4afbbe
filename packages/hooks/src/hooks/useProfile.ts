import { useState, useCallback } from 'react';
import { apiService } from '@fishivo/services';

// Local type definitions to avoid export issues
interface User {
  id: string;
  email: string;
  username: string;
  full_name: string;
  bio?: string;
  avatar_url?: string;
  location?: string;
  website?: string;
  created_at: string;
  updated_at: string;
  is_verified: boolean;
  is_private: boolean;
}

interface ApiResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  message?: string;
}

interface ProfileState {
  profile: User | null;
  isLoading: boolean;
  error: string | null;
}

export const useProfile = () => {
  const [profileState, setProfileState] = useState<ProfileState>({
    profile: null,
    isLoading: false,
    error: null,
  });

  const getProfile = useCallback(async (userId: string) => {
    try {
      setProfileState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('👤 Getting user profile:', userId);

      // Use the real API method
      const response: ApiResponse<User> = await apiService.getUserProfile(userId);

      console.log('✅ User profile fetched:', response);

      if (response.success && response.data) {
        setProfileState(prev => ({
          ...prev,
          profile: response.data,
          isLoading: false,
        }));
        return response.data;
      } else {
        throw new Error(response.error || 'Profil yüklenemedi');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcı profili yüklenirken hata oluştu';
      console.error('❌ Get user profile error:', error);
      setProfileState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const updateProfile = useCallback(async (profileData: Partial<User>) => {
    try {
      setProfileState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🔄 Updating profile:', profileData);

      // Use the real API method
      const updatedUser = await apiService.updateProfile(profileData);

      console.log('✅ Profile updated:', updatedUser);

      setProfileState(prev => ({
        ...prev,
        profile: updatedUser,
        isLoading: false,
      }));

      return updatedUser;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Profil güncellenirken hata oluştu';
      console.error('❌ Update profile error:', error);
      setProfileState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getUserCatches = useCallback(async (userId: string) => {
    try {
      console.log('🎣 Getting user catches:', userId);

      // Use the real API method
      const response: ApiResponse<any> = await apiService.getUserCatches(userId);

      console.log('✅ User catches fetched:', response);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Kullanıcı yakalamaları yüklenemedi');
      }
    } catch (error) {
      console.error('❌ Get user catches error:', error);
      throw error;
    }
  }, []);

  const getUserSocialCounts = useCallback(async (userId: string) => {
    try {
      console.log('📊 Getting user social counts:', userId);

      // Use the real API method
      const counts = await apiService.getUserSocialCounts(userId);

      console.log('✅ User social counts fetched:', counts);

      return counts;
    } catch (error) {
      console.error('❌ Get user social counts error:', error);
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setProfileState(prev => ({ ...prev, error: null }));
  }, []);

  const clearProfile = useCallback(() => {
    setProfileState({
      profile: null,
      isLoading: false,
      error: null,
    });
  }, []);

  return {
    profile: profileState.profile,
    isLoading: profileState.isLoading,
    error: profileState.error,
    getProfile,
    updateProfile,
    getUserCatches,
    getUserSocialCounts,
    clearError,
    clearProfile,
  };
};
