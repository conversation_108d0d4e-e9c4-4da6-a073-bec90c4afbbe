import { useState, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

interface UsersState {
  users: any[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  total: number;
}

export const useUsers = () => {
  const [usersState, setUsersState] = useState<UsersState>({
    users: [],
    isLoading: false,
    error: null,
    hasMore: true,
    page: 1,
    total: 0,
  });

  const searchUsers = useCallback(async (query: string, reset = true) => {
    try {
      const currentPage = reset ? 1 : usersState.page;

      setUsersState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        page: currentPage,
      }));

      console.log('🔍 Searching users:', { query, page: currentPage });

      // Use the real API method
      const response: PaginatedResponse<any> = await apiService.searchUsers(
        query,
        currentPage,
        10
      );

      console.log('✅ Users search result:', response);

      setUsersState(prev => ({
        ...prev,
        users: reset ? response.items : [...prev.users, ...response.items],
        isLoading: false,
        hasMore: response.hasMore,
        page: currentPage + 1,
        total: response.total,
      }));

      return response.items;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcılar aranırken hata oluştu';
      console.error('❌ Search users error:', error);
      setUsersState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, [usersState.page]);

  const loadMoreUsers = useCallback(async (query: string) => {
    if (!usersState.hasMore || usersState.isLoading) return;
    return await searchUsers(query, false);
  }, [usersState.hasMore, usersState.isLoading, searchUsers]);

  const getUserProfile = useCallback(async (userId: string) => {
    try {
      setUsersState(prev => ({ ...prev, isLoading: true, error: null }));

      console.log('👤 Getting user profile:', userId);

      // Use the real API method
      const response = await apiService.getUserProfile(userId);

      console.log('✅ User profile fetched:', response);

      setUsersState(prev => ({ ...prev, isLoading: false }));

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Profil yüklenemedi');
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcı profili yüklenirken hata oluştu';
      console.error('❌ Get user profile error:', error);
      setUsersState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getUserCatches = useCallback(async (userId: string) => {
    try {
      console.log('🎣 Getting user catches:', userId);

      // Use the real API method
      const response = await apiService.getUserCatches(userId);

      console.log('✅ User catches fetched:', response);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.error || 'Kullanıcı yakalamaları yüklenemedi');
      }
    } catch (error) {
      console.error('❌ Get user catches error:', error);
      throw error;
    }
  }, []);

  const blockUser = useCallback(async (userId: string) => {
    try {
      setUsersState(prev => ({ ...prev, isLoading: true, error: null }));

      console.log('🚫 Blocking user:', userId);

      // Use the real API method
      await apiService.blockUser(userId);

      console.log('✅ User blocked successfully');

      setUsersState(prev => ({
        ...prev,
        users: prev.users.filter(user => user.id !== userId),
        isLoading: false,
      }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcı engellenirken hata oluştu';
      console.error('❌ Block user error:', error);
      setUsersState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const unblockUser = useCallback(async (userId: string) => {
    try {
      setUsersState(prev => ({ ...prev, isLoading: true, error: null }));

      console.log('✅ Unblocking user:', userId);

      // Use the real API method
      await apiService.unblockUser(userId);

      console.log('✅ User unblocked successfully');

      setUsersState(prev => ({ ...prev, isLoading: false }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kullanıcı engeli kaldırılırken hata oluştu';
      console.error('❌ Unblock user error:', error);
      setUsersState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getBlockedUsers = useCallback(async (page = 1, limit = 20) => {
    try {
      setUsersState(prev => ({ ...prev, isLoading: true, error: null }));

      console.log('🚫 Getting blocked users:', { page, limit });

      // Use the real API method
      const response: PaginatedResponse<any> = await apiService.getBlockedUsers(page, limit);

      console.log('✅ Blocked users fetched:', response);

      setUsersState(prev => ({
        ...prev,
        users: response.items,
        isLoading: false,
        hasMore: response.hasMore,
        total: response.total,
      }));

      return response;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Engellenen kullanıcılar yüklenirken hata oluştu';
      console.error('❌ Get blocked users error:', error);
      setUsersState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const clearUsers = useCallback(() => {
    setUsersState({
      users: [],
      isLoading: false,
      error: null,
      hasMore: true,
      page: 1,
      total: 0,
    });
  }, []);

  const clearError = useCallback(() => {
    setUsersState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    users: usersState.users,
    isLoading: usersState.isLoading,
    error: usersState.error,
    hasMore: usersState.hasMore,
    total: usersState.total,
    searchUsers,
    loadMoreUsers,
    getUserProfile,
    getUserCatches,
    blockUser,
    unblockUser,
    getBlockedUsers,
    clearUsers,
    clearError,
  };
};