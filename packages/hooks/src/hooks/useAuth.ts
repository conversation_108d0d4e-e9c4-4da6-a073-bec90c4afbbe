import { useState, useEffect, useCallback } from 'react';
import { apiService, supabase } from '@fishivo/services';

interface AuthUser {
  id: string;
  email: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  location?: string;
  [key: string]: any;
}

interface AuthState {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    initializeAuth();
    setupAuthListener();
  }, []);

  const initializeAuth = async () => {
    try {
      // Check if user is already authenticated using existing API
      const token = await apiService.getToken();
      if (token) {
        const userResponse = await apiService.getCurrentUser();
        if (userResponse.success && userResponse.data) {
          setAuthState({
            user: userResponse.data,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
          return;
        }
      }

      setAuthState(prev => ({ ...prev, isLoading: false }));
    } catch (error) {
      console.error('Auth initialization error:', error);
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    }
  };

  const setupAuthListener = () => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);

        if (event === 'SIGNED_IN' && session?.user) {
          try {
            // Store token using existing API method
            if (session.access_token) {
              await apiService.setToken(session.access_token);
            }

            // Get user data from API using existing method
            const userResponse = await apiService.getCurrentUser();
            if (userResponse.success && userResponse.data) {
              setAuthState({
                user: userResponse.data,
                isAuthenticated: true,
                isLoading: false,
                error: null,
              });
            }
          } catch (error) {
            console.error('Error handling sign in:', error);
            setAuthState(prev => ({ ...prev, isLoading: false }));
          }
        } else if (event === 'SIGNED_OUT') {
          await apiService.removeToken();
          setAuthState({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      }
    );

    return () => subscription.unsubscribe();
  };

  const login = useCallback(async (email: string, password: string) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Auth listener will handle the rest
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Giriş yapılırken hata oluştu';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const register = useCallback(async (email: string, password: string, fullName: string, username?: string) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            username: username,
          },
        },
      });

      if (error) throw error;

      // Auth listener will handle the rest
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kayıt olurken hata oluştu';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // Auth listener will handle the rest
    } catch (error) {
      console.error('Logout error:', error);
      // Force logout even if there's an error using existing API method
      await apiService.removeToken();
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    }
  }, []);

  const updateProfile = useCallback(async (profileData: Partial<AuthUser>) => {
    try {
      if (!authState.user) throw new Error('Kullanıcı oturumu bulunamadı');

      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Use existing API method - apiService.updateProfile
      const updatedUser = await apiService.updateProfile(profileData);

      setAuthState(prev => ({
        ...prev,
        user: { ...prev.user, ...updatedUser },
        isLoading: false,
      }));

      return updatedUser;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Profil güncellenirken hata oluştu';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, [authState.user]);

  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  const signInWithGoogle = useCallback(async () => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      // Use Google Sign-In service
      const { GoogleSignInService } = await import('@fishivo/services');
      const result = await GoogleSignInService.signIn();

      if (result.success && result.user) {
        // For now, we'll use the Google user data directly
        // In a real implementation, you'd send the token to your backend
        setAuthState({
          user: {
            id: result.user.id || '',
            email: result.user.email || '',
            full_name: result.user.user_metadata?.full_name || result.user.email || '',
            avatar_url: result.user.user_metadata?.avatar_url || undefined,
          },
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
      } else {
        throw new Error(result.error || 'Google ile giriş iptal edildi');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Google ile giriş başarısız';
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      throw error;
    }
  }, []);

  const clearAuthError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    error: authState.error,
    authError: authState.error, // Alias for backward compatibility
    login,
    register,
    logout,
    updateProfile,
    signInWithGoogle,
    clearError,
    clearAuthError,
  };
};