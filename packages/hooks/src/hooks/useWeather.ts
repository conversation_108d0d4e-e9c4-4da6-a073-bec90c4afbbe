import { useState, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface WeatherData {
  temperature: number;
  humidity: number;
  windSpeed: number;
  windDirection: number;
  pressure: number;
  visibility: number;
  uvIndex: number;
  condition: string;
  description: string;
  icon: string;
  location: {
    name: string;
    country: string;
  };
  timestamp: string;
}

interface ForecastData {
  date: string;
  maxTemp: number;
  minTemp: number;
  condition: string;
  description: string;
  icon: string;
  humidity: number;
  windSpeed: number;
  precipitation: number;
}

interface WeatherState {
  currentWeather: WeatherData | null;
  forecast: ForecastData[];
  isLoading: boolean;
  error: string | null;
}

export const useWeather = () => {
  const [weatherState, setWeatherState] = useState<WeatherState>({
    currentWeather: null,
    forecast: [],
    isLoading: false,
    error: null,
  });

  const getCurrentWeather = useCallback(async (latitude: number, longitude: number) => {
    try {
      setWeatherState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🌤️ Getting current weather for:', { latitude, longitude });

      // Use the real API method
      const weatherData = await apiService.getCurrentWeather(latitude, longitude);

      console.log('✅ Current weather fetched:', weatherData);

      setWeatherState(prev => ({
        ...prev,
        currentWeather: weatherData,
        isLoading: false,
      }));

      return weatherData;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Hava durumu alınırken hata oluştu';
      console.error('❌ Get current weather error:', error);
      setWeatherState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getWeatherForecast = useCallback(async (latitude: number, longitude: number, days = 5) => {
    try {
      setWeatherState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('📅 Getting weather forecast for:', { latitude, longitude, days });

      // Use the real API method
      const forecastData = await apiService.getWeatherForecast(latitude, longitude, days);

      console.log('✅ Weather forecast fetched:', forecastData);

      setWeatherState(prev => ({
        ...prev,
        forecast: forecastData,
        isLoading: false,
      }));

      return forecastData;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Hava durumu tahmini alınırken hata oluştu';
      console.error('❌ Get weather forecast error:', error);
      setWeatherState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getWeatherForLocation = useCallback(async (latitude: number, longitude: number, includeForecast = true) => {
    try {
      setWeatherState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🌍 Getting complete weather data for location:', { latitude, longitude });

      // Get both current weather and forecast
      const [currentWeather, forecast] = await Promise.all([
        apiService.getCurrentWeather(latitude, longitude),
        includeForecast ? apiService.getWeatherForecast(latitude, longitude, 5) : Promise.resolve([])
      ]);

      console.log('✅ Complete weather data fetched');

      setWeatherState(prev => ({
        ...prev,
        currentWeather,
        forecast: forecast || [],
        isLoading: false,
      }));

      return {
        current: currentWeather,
        forecast: forecast || [],
      };
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Hava durumu verileri alınırken hata oluştu';
      console.error('❌ Get weather for location error:', error);
      setWeatherState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const isFishingWeatherGood = useCallback((weather: WeatherData): boolean => {
    // Basic fishing weather assessment
    const goodConditions = [
      'clear',
      'partly-cloudy',
      'cloudy',
      'overcast'
    ];

    const badConditions = [
      'rain',
      'heavy-rain',
      'thunderstorm',
      'snow',
      'fog'
    ];

    // Check weather condition
    if (badConditions.some(condition => weather.condition.toLowerCase().includes(condition))) {
      return false;
    }

    // Check wind speed (too windy is bad for fishing)
    if (weather.windSpeed > 25) { // km/h
      return false;
    }

    // Check visibility
    if (weather.visibility < 5) { // km
      return false;
    }

    return true;
  }, []);

  const getFishingRecommendation = useCallback((weather: WeatherData): {
    isGood: boolean;
    score: number;
    reasons: string[];
  } => {
    const reasons: string[] = [];
    let score = 50; // Base score

    // Temperature assessment
    if (weather.temperature >= 15 && weather.temperature <= 25) {
      score += 20;
      reasons.push('İdeal sıcaklık');
    } else if (weather.temperature < 5 || weather.temperature > 35) {
      score -= 20;
      reasons.push('Aşırı sıcaklık');
    }

    // Wind assessment
    if (weather.windSpeed <= 10) {
      score += 15;
      reasons.push('Sakin rüzgar');
    } else if (weather.windSpeed > 25) {
      score -= 25;
      reasons.push('Çok rüzgarlı');
    }

    // Pressure assessment
    if (weather.pressure >= 1013 && weather.pressure <= 1023) {
      score += 10;
      reasons.push('İyi basınç');
    } else if (weather.pressure < 1000) {
      score -= 15;
      reasons.push('Düşük basınç');
    }

    // Humidity assessment
    if (weather.humidity >= 40 && weather.humidity <= 70) {
      score += 5;
    }

    // Weather condition assessment
    const goodConditions = ['clear', 'partly-cloudy', 'cloudy'];
    if (goodConditions.some(condition => weather.condition.toLowerCase().includes(condition))) {
      score += 10;
      reasons.push('İyi hava koşulları');
    }

    const badConditions = ['rain', 'thunderstorm', 'snow'];
    if (badConditions.some(condition => weather.condition.toLowerCase().includes(condition))) {
      score -= 30;
      reasons.push('Kötü hava koşulları');
    }

    return {
      isGood: score >= 60,
      score: Math.max(0, Math.min(100, score)),
      reasons,
    };
  }, []);

  const clearError = useCallback(() => {
    setWeatherState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    currentWeather: weatherState.currentWeather,
    forecast: weatherState.forecast,
    isLoading: weatherState.isLoading,
    error: weatherState.error,
    getCurrentWeather,
    getWeatherForecast,
    getWeatherForLocation,
    isFishingWeatherGood,
    getFishingRecommendation,
    clearError,
  };
};
