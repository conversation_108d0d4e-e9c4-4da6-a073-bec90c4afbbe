import { useCallback } from 'react';

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
  email?: boolean;
  phone?: boolean;
  url?: boolean;
  number?: boolean;
  min?: number;
  max?: number;
}

interface ValidationResult {
  isValid: boolean;
  error: string | null;
}

export const useValidation = () => {
  const validateField = useCallback((value: any, rules: ValidationRule): ValidationResult => {
    // Required validation
    if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return {
        isValid: false,
        error: 'Bu alan zor<PERSON>',
      };
    }

    // If value is empty and not required, it's valid
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return {
        isValid: true,
        error: null,
      };
    }

    const stringValue = String(value).trim();

    // Min length validation
    if (rules.minLength && stringValue.length < rules.minLength) {
      return {
        isValid: false,
        error: `En az ${rules.minLength} karakter olmalıdır`,
      };
    }

    // Max length validation
    if (rules.maxLength && stringValue.length > rules.maxLength) {
      return {
        isValid: false,
        error: `En fazla ${rules.maxLength} karakter olmalıdır`,
      };
    }

    // Email validation
    if (rules.email) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(stringValue)) {
        return {
          isValid: false,
          error: 'Geçerli bir e-posta adresi giriniz',
        };
      }
    }

    // Phone validation (Turkish format)
    if (rules.phone) {
      const phonePattern = /^(\+90|0)?[5][0-9]{9}$/;
      if (!phonePattern.test(stringValue.replace(/\s/g, ''))) {
        return {
          isValid: false,
          error: 'Geçerli bir telefon numarası giriniz',
        };
      }
    }

    // URL validation
    if (rules.url) {
      try {
        new URL(stringValue);
      } catch {
        return {
          isValid: false,
          error: 'Geçerli bir URL giriniz',
        };
      }
    }

    // Number validation
    if (rules.number) {
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return {
          isValid: false,
          error: 'Geçerli bir sayı giriniz',
        };
      }

      // Min value validation
      if (rules.min !== undefined && numValue < rules.min) {
        return {
          isValid: false,
          error: `En az ${rules.min} olmalıdır`,
        };
      }

      // Max value validation
      if (rules.max !== undefined && numValue > rules.max) {
        return {
          isValid: false,
          error: `En fazla ${rules.max} olmalıdır`,
        };
      }
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(stringValue)) {
      return {
        isValid: false,
        error: 'Geçerli format değil',
      };
    }

    // Custom validation
    if (rules.custom) {
      const customError = rules.custom(value);
      if (customError) {
        return {
          isValid: false,
          error: customError,
        };
      }
    }

    return {
      isValid: true,
      error: null,
    };
  }, []);

  // Common validation rules
  const rules = {
    required: { required: true },
    email: { email: true },
    phone: { phone: true },
    url: { url: true },
    
    // Username validation
    username: {
      required: true,
      minLength: 3,
      maxLength: 20,
      pattern: /^[a-zA-Z0-9_]+$/,
      custom: (value: string) => {
        if (value && value.startsWith('_')) {
          return 'Kullanıcı adı alt çizgi ile başlayamaz';
        }
        return null;
      },
    },

    // Password validation
    password: {
      required: true,
      minLength: 8,
      custom: (value: string) => {
        if (!value) return null;
        
        const hasUpperCase = /[A-Z]/.test(value);
        const hasLowerCase = /[a-z]/.test(value);
        const hasNumbers = /\d/.test(value);
        
        if (!hasUpperCase || !hasLowerCase || !hasNumbers) {
          return 'Şifre en az bir büyük harf, bir küçük harf ve bir rakam içermelidir';
        }
        return null;
      },
    },

    // Full name validation
    fullName: {
      required: true,
      minLength: 2,
      maxLength: 50,
      pattern: /^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]+$/,
    },

    // Weight validation (for fishing)
    weight: {
      number: true,
      min: 0.01,
      max: 1000,
    },

    // Length validation (for fishing)
    length: {
      number: true,
      min: 1,
      max: 500,
    },

    // Location name validation
    locationName: {
      required: true,
      minLength: 2,
      maxLength: 100,
    },

    // Bio validation
    bio: {
      maxLength: 500,
    },

    // Post content validation
    postContent: {
      required: true,
      minLength: 1,
      maxLength: 2000,
    },
  };

  const validateEmail = useCallback((email: string): ValidationResult => {
    return validateField(email, rules.email);
  }, [validateField, rules.email]);

  const validatePassword = useCallback((password: string): ValidationResult => {
    return validateField(password, rules.password);
  }, [validateField, rules.password]);

  const validateUsername = useCallback((username: string): ValidationResult => {
    return validateField(username, rules.username);
  }, [validateField, rules.username]);

  const validateFullName = useCallback((fullName: string): ValidationResult => {
    return validateField(fullName, rules.fullName);
  }, [validateField, rules.fullName]);

  const validatePhone = useCallback((phone: string): ValidationResult => {
    return validateField(phone, rules.phone);
  }, [validateField, rules.phone]);

  const validateWeight = useCallback((weight: number | string): ValidationResult => {
    return validateField(weight, rules.weight);
  }, [validateField, rules.weight]);

  const validateLength = useCallback((length: number | string): ValidationResult => {
    return validateField(length, rules.length);
  }, [validateField, rules.length]);

  const validateRequired = useCallback((value: any): ValidationResult => {
    return validateField(value, rules.required);
  }, [validateField, rules.required]);

  return {
    validateField,
    validateEmail,
    validatePassword,
    validateUsername,
    validateFullName,
    validatePhone,
    validateWeight,
    validateLength,
    validateRequired,
    rules,
  };
};
