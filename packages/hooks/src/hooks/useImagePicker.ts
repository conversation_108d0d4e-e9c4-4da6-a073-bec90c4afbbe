import { useState, useCallback } from 'react';
import { ImageUploadService } from '@fishivo/services';

interface ImagePickerState {
  selectedImages: string[];
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
}

interface ImagePickerOptions {
  maxImages?: number;
  quality?: number;
  allowsEditing?: boolean;
  aspect?: [number, number];
}

interface UploadMetadata {
  type: 'catch' | 'spot' | 'profile' | 'equipment';
  fishSpecies?: string;
  spotName?: string;
}

export const useImagePicker = () => {
  const [imageState, setImageState] = useState<ImagePickerState>({
    selectedImages: [],
    isUploading: false,
    uploadProgress: 0,
    error: null,
  });

  const pickFromLibrary = useCallback(async (options: ImagePickerOptions = {}) => {
    try {
      setImageState(prev => ({
        ...prev,
        error: null,
      }));

      console.log('📷 Opening image library...');

      // Create a file input element
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = (options.maxImages || 1) > 1;

      const images = await new Promise<string[]>((resolve, reject) => {
        input.onchange = (event) => {
          const files = (event.target as HTMLInputElement).files;
          if (!files || files.length === 0) {
            resolve([]);
            return;
          }

          const imageUrls: string[] = [];
          let processedCount = 0;

          Array.from(files).forEach((file, index) => {
            if (index >= (options.maxImages || 10)) return;

            const reader = new FileReader();
            reader.onload = (e) => {
              if (e.target?.result) {
                imageUrls.push(e.target.result as string);
              }
              processedCount++;
              if (processedCount === Math.min(files.length, options.maxImages || 10)) {
                resolve(imageUrls);
              }
            };
            reader.onerror = () => reject(new Error('Failed to read image file'));
            reader.readAsDataURL(file);
          });
        };

        input.oncancel = () => resolve([]);
      });

      input.click();

      console.log('✅ Images selected from library:', images.length);

      setImageState(prev => ({
        ...prev,
        selectedImages: images,
      }));

      return images;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Galeri açılırken hata oluştu';
      console.error('❌ Pick from library error:', error);
      setImageState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const takePhoto = useCallback(async (options: ImagePickerOptions = {}) => {
    try {
      setImageState(prev => ({
        ...prev,
        error: null,
      }));

      console.log('📸 Opening camera...');

      // For web, we'll use the camera input
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.capture = 'environment'; // Use back camera

      const image = await new Promise<string | null>((resolve, reject) => {
        input.onchange = (event) => {
          const file = (event.target as HTMLInputElement).files?.[0];
          if (!file) {
            resolve(null);
            return;
          }

          const reader = new FileReader();
          reader.onload = (e) => {
            if (e.target?.result) {
              resolve(e.target.result as string);
            } else {
              resolve(null);
            }
          };
          reader.onerror = () => reject(new Error('Failed to read image file'));
          reader.readAsDataURL(file);
        };

        input.oncancel = () => resolve(null);
      });

      input.click();

      if (image) {
        console.log('✅ Photo taken');

        setImageState(prev => ({
          ...prev,
          selectedImages: [image],
        }));

        return [image];
      }

      return [];
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Kamera açılırken hata oluştu';
      console.error('❌ Take photo error:', error);
      setImageState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const uploadImages = useCallback(async (
    images: string[],
    metadata: UploadMetadata,
    authToken: string
  ) => {
    try {
      setImageState(prev => ({
        ...prev,
        isUploading: true,
        uploadProgress: 0,
        error: null,
      }));

      console.log('☁️ Uploading images:', { count: images.length, metadata });

      const imageUploadService = new ImageUploadService();

      const result = await imageUploadService.uploadMultipleImages(
        images,
        metadata,
        authToken,
        (progress) => {
          setImageState(prev => ({
            ...prev,
            uploadProgress: progress.percentage,
          }));
        }
      );

      console.log('✅ Images uploaded:', result);

      setImageState(prev => ({
        ...prev,
        isUploading: false,
        uploadProgress: 100,
      }));

      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Resimler yüklenirken hata oluştu';
      console.error('❌ Upload images error:', error);
      setImageState(prev => ({
        ...prev,
        isUploading: false,
        uploadProgress: 0,
        error: message,
      }));
      throw error;
    }
  }, []);

  const removeImage = useCallback((index: number) => {
    setImageState(prev => ({
      ...prev,
      selectedImages: prev.selectedImages.filter((_, i) => i !== index),
    }));
  }, []);

  const clearImages = useCallback(() => {
    setImageState(prev => ({
      ...prev,
      selectedImages: [],
      uploadProgress: 0,
    }));
  }, []);

  const clearError = useCallback(() => {
    setImageState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    selectedImages: imageState.selectedImages,
    isUploading: imageState.isUploading,
    uploadProgress: imageState.uploadProgress,
    error: imageState.error,
    pickFromLibrary,
    takePhoto,
    uploadImages,
    removeImage,
    clearImages,
    clearError,
  };
};
