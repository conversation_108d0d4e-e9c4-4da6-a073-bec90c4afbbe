import { useState, useCallback, useEffect } from 'react';

interface StorageState {
  isLoading: boolean;
  error: string | null;
}

// For web environment, we'll use localStorage
const storage = {
  getItem: async (key: string): Promise<string | null> => {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.error('Storage getItem error:', error);
      return null;
    }
  },
  
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.error('Storage setItem error:', error);
      throw error;
    }
  },
  
  removeItem: async (key: string): Promise<void> => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Storage removeItem error:', error);
      throw error;
    }
  },
  
  clear: async (): Promise<void> => {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Storage clear error:', error);
      throw error;
    }
  },
  
  getAllKeys: async (): Promise<string[]> => {
    try {
      return Object.keys(localStorage);
    } catch (error) {
      console.error('Storage getAllKeys error:', error);
      return [];
    }
  },
};

export const useStorage = () => {
  const [storageState, setStorageState] = useState<StorageState>({
    isLoading: false,
    error: null,
  });

  const getItem = useCallback(async (key: string): Promise<string | null> => {
    try {
      setStorageState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('📦 Getting item from storage:', key);

      const value = await storage.getItem(key);

      console.log('✅ Item retrieved from storage:', { key, hasValue: !!value });

      setStorageState(prev => ({
        ...prev,
        isLoading: false,
      }));

      return value;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Veri okunurken hata oluştu';
      console.error('❌ Get item from storage error:', error);
      setStorageState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const setItem = useCallback(async (key: string, value: string): Promise<void> => {
    try {
      setStorageState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('💾 Setting item in storage:', key);

      await storage.setItem(key, value);

      console.log('✅ Item saved to storage:', key);

      setStorageState(prev => ({
        ...prev,
        isLoading: false,
      }));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Veri kaydedilirken hata oluştu';
      console.error('❌ Set item in storage error:', error);
      setStorageState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const removeItem = useCallback(async (key: string): Promise<void> => {
    try {
      setStorageState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🗑️ Removing item from storage:', key);

      await storage.removeItem(key);

      console.log('✅ Item removed from storage:', key);

      setStorageState(prev => ({
        ...prev,
        isLoading: false,
      }));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Veri silinirken hata oluştu';
      console.error('❌ Remove item from storage error:', error);
      setStorageState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getObject = useCallback(async <T>(key: string): Promise<T | null> => {
    try {
      const value = await getItem(key);
      if (value === null) return null;
      
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('❌ Get object from storage error:', error);
      return null;
    }
  }, [getItem]);

  const setObject = useCallback(async (key: string, value: any): Promise<void> => {
    try {
      const jsonValue = JSON.stringify(value);
      await setItem(key, jsonValue);
    } catch (error) {
      console.error('❌ Set object in storage error:', error);
      throw error;
    }
  }, [setItem]);

  const clear = useCallback(async (): Promise<void> => {
    try {
      setStorageState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('🧹 Clearing all storage...');

      await storage.clear();

      console.log('✅ Storage cleared');

      setStorageState(prev => ({
        ...prev,
        isLoading: false,
      }));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Depolama temizlenirken hata oluştu';
      console.error('❌ Clear storage error:', error);
      setStorageState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getAllKeys = useCallback(async (): Promise<string[]> => {
    try {
      console.log('🔑 Getting all storage keys...');

      const keys = await storage.getAllKeys();

      console.log('✅ Storage keys retrieved:', keys.length);

      return keys;
    } catch (error) {
      console.error('❌ Get all keys error:', error);
      return [];
    }
  }, []);

  const multiGet = useCallback(async (keys: string[]): Promise<Array<[string, string | null]>> => {
    try {
      console.log('📦 Getting multiple items from storage:', keys);

      const results: Array<[string, string | null]> = [];
      
      for (const key of keys) {
        const value = await storage.getItem(key);
        results.push([key, value]);
      }

      console.log('✅ Multiple items retrieved from storage');

      return results;
    } catch (error) {
      console.error('❌ Multi get error:', error);
      return [];
    }
  }, []);

  const multiSet = useCallback(async (keyValuePairs: Array<[string, string]>): Promise<void> => {
    try {
      setStorageState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
      }));

      console.log('💾 Setting multiple items in storage:', keyValuePairs.length);

      for (const [key, value] of keyValuePairs) {
        await storage.setItem(key, value);
      }

      console.log('✅ Multiple items saved to storage');

      setStorageState(prev => ({
        ...prev,
        isLoading: false,
      }));
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Çoklu veri kaydedilirken hata oluştu';
      console.error('❌ Multi set error:', error);
      setStorageState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setStorageState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    isLoading: storageState.isLoading,
    error: storageState.error,
    getItem,
    setItem,
    removeItem,
    getObject,
    setObject,
    clear,
    getAllKeys,
    multiGet,
    multiSet,
    clearError,
  };
};
