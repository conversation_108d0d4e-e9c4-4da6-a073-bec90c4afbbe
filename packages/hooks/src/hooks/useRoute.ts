import { useState, useCallback, useEffect, useMemo } from 'react';

interface RouteParams {
  [key: string]: string | undefined;
}

interface RouteState {
  name: string;
  path: string;
  params: RouteParams;
  query: Record<string, string>;
  hash: string;
  isLoading: boolean;
}

interface RouteMatch {
  path: string;
  name: string;
  paramNames: string[];
}

// Define common routes for the Fishivo app
const routes: RouteMatch[] = [
  { path: '/', name: 'Home', paramNames: [] },
  { path: '/auth', name: 'Auth', paramNames: [] },
  { path: '/profile', name: 'Profile', paramNames: [] },
  { path: '/profile/:userId', name: 'UserProfile', paramNames: ['userId'] },
  { path: '/post/:postId', name: 'PostDetail', paramNames: ['postId'] },
  { path: '/add-catch', name: 'Add<PERSON>atch', paramNames: [] },
  { path: '/map', name: 'Map', paramNames: [] },
  { path: '/notifications', name: 'Notifications', paramNames: [] },
  { path: '/settings', name: '<PERSON><PERSON><PERSON>', paramNames: [] },
  { path: '/edit-profile', name: 'EditProfile', paramNames: [] },
  { path: '/search', name: 'Search', paramNames: [] },
  { path: '/spot/:spotId', name: 'SpotDetail', paramNames: ['spotId'] },
  { path: '/species/:speciesId', name: 'SpeciesDetail', paramNames: ['speciesId'] },
  { path: '/users/:userId/catches', name: 'UserCatches', paramNames: ['userId'] },
  { path: '/blocked-users', name: 'BlockedUsers', paramNames: [] },
  { path: '/add-gear', name: 'AddGear', paramNames: [] },
  { path: '/add-spot', name: 'AddSpot', paramNames: [] },
  { path: '/location-management', name: 'LocationManagement', paramNames: [] },
  { path: '/add-location', name: 'AddLocation', paramNames: [] },
  { path: '/explore-search', name: 'ExploreSearch', paramNames: [] },
];

export const useRoute = () => {
  const [routeState, setRouteState] = useState<RouteState>({
    name: 'Unknown',
    path: window.location.pathname,
    params: {},
    query: {},
    hash: window.location.hash,
    isLoading: false,
  });

  const parseRoute = useCallback((pathname: string): { name: string; params: RouteParams } => {
    for (const route of routes) {
      const pathParts = route.path.split('/');
      const currentParts = pathname.split('/');

      if (pathParts.length !== currentParts.length) {
        continue;
      }

      let isMatch = true;
      const params: RouteParams = {};

      for (let i = 0; i < pathParts.length; i++) {
        const pathPart = pathParts[i];
        const currentPart = currentParts[i];

        if (pathPart.startsWith(':')) {
          // This is a parameter
          const paramName = pathPart.slice(1);
          params[paramName] = currentPart;
        } else if (pathPart !== currentPart) {
          // Static part doesn't match
          isMatch = false;
          break;
        }
      }

      if (isMatch) {
        return { name: route.name, params };
      }
    }

    return { name: 'Unknown', params: {} };
  }, []);

  const parseQuery = useCallback((search: string): Record<string, string> => {
    const query: Record<string, string> = {};
    const urlParams = new URLSearchParams(search);
    
    urlParams.forEach((value, key) => {
      query[key] = value;
    });

    return query;
  }, []);

  const updateRouteState = useCallback(() => {
    const pathname = window.location.pathname;
    const search = window.location.search;
    const hash = window.location.hash;

    const { name, params } = parseRoute(pathname);
    const query = parseQuery(search);

    setRouteState({
      name,
      path: pathname,
      params,
      query,
      hash,
      isLoading: false,
    });
  }, [parseRoute, parseQuery]);

  useEffect(() => {
    updateRouteState();

    const handleLocationChange = () => {
      updateRouteState();
    };

    window.addEventListener('popstate', handleLocationChange);

    return () => {
      window.removeEventListener('popstate', handleLocationChange);
    };
  }, [updateRouteState]);

  const getParam = useCallback((key: string): string | undefined => {
    return routeState.params[key];
  }, [routeState.params]);

  const getQuery = useCallback((key: string): string | undefined => {
    return routeState.query[key];
  }, [routeState.query]);

  const isRoute = useCallback((routeName: string): boolean => {
    return routeState.name === routeName;
  }, [routeState.name]);

  const matchesPath = useCallback((path: string): boolean => {
    return routeState.path === path;
  }, [routeState.path]);

  // Route-specific helpers
  const isHome = useMemo(() => isRoute('Home'), [isRoute]);
  const isAuth = useMemo(() => isRoute('Auth'), [isRoute]);
  const isProfile = useMemo(() => isRoute('Profile') || isRoute('UserProfile'), [isRoute]);
  const isPostDetail = useMemo(() => isRoute('PostDetail'), [isRoute]);
  const isAddCatch = useMemo(() => isRoute('AddCatch'), [isRoute]);
  const isMap = useMemo(() => isRoute('Map'), [isRoute]);
  const isNotifications = useMemo(() => isRoute('Notifications'), [isRoute]);
  const isSettings = useMemo(() => isRoute('Settings'), [isRoute]);
  const isSearch = useMemo(() => isRoute('Search'), [isRoute]);

  // Get specific parameters
  const getUserId = useCallback((): string | undefined => {
    return getParam('userId');
  }, [getParam]);

  const getPostId = useCallback((): string | undefined => {
    return getParam('postId');
  }, [getParam]);

  const getSpotId = useCallback((): string | undefined => {
    return getParam('spotId');
  }, [getParam]);

  const getSpeciesId = useCallback((): string | undefined => {
    return getParam('speciesId');
  }, [getParam]);

  const getSearchQuery = useCallback((): string | undefined => {
    return getQuery('q');
  }, [getQuery]);

  const getAuthMode = useCallback((): 'login' | 'register' | undefined => {
    const mode = getQuery('mode');
    return mode === 'login' || mode === 'register' ? mode : undefined;
  }, [getQuery]);

  // Generate route paths
  const generatePath = useCallback((routeName: string, params: RouteParams = {}): string => {
    const route = routes.find(r => r.name === routeName);
    if (!route) {
      console.warn(`Route not found: ${routeName}`);
      return '/';
    }

    let path = route.path;
    route.paramNames.forEach(paramName => {
      const paramValue = params[paramName];
      if (paramValue) {
        path = path.replace(`:${paramName}`, paramValue);
      }
    });

    return path;
  }, []);

  return {
    // Current route state
    name: routeState.name,
    path: routeState.path,
    params: routeState.params,
    query: routeState.query,
    hash: routeState.hash,
    isLoading: routeState.isLoading,

    // Route checking
    isRoute,
    matchesPath,

    // Route-specific flags
    isHome,
    isAuth,
    isProfile,
    isPostDetail,
    isAddCatch,
    isMap,
    isNotifications,
    isSettings,
    isSearch,

    // Parameter getters
    getParam,
    getQuery,
    getUserId,
    getPostId,
    getSpotId,
    getSpeciesId,
    getSearchQuery,
    getAuthMode,

    // Utilities
    generatePath,
  };
};
