import { useState, useCallback } from 'react';
import { apiService } from '@fishivo/services';

interface LikeState {
  isLiked: boolean;
  likesCount: number;
  isPending: boolean;
  error: string | null;
}

export const useLikes = (postId?: number) => {
  const [likeState, setLikeState] = useState<LikeState>({
    isLiked: false,
    likesCount: 0,
    isPending: false,
    error: null,
  });

  const checkLikeStatus = useCallback(async (targetPostId: number) => {
    try {
      console.log('🔍 Checking like status for post:', targetPostId);

      // Use the real API method
      const isLiked = await apiService.isPostLiked(targetPostId);

      console.log('✅ Like status checked:', { postId: targetPostId, isLiked });

      setLikeState(prev => ({
        ...prev,
        isLiked,
        error: null,
      }));

      return isLiked;
    } catch (error) {
      const message = error instanceof Error ? error.message : '<PERSON><PERSON><PERSON> durumu kontrol edilirken hata oluş<PERSON>';
      console.error('❌ Check like status error:', error);
      setLikeState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const likePost = useCallback(async (targetPostId: number) => {
    if (likeState.isPending) return;

    try {
      setLikeState(prev => ({
        ...prev,
        isPending: true,
        error: null
      }));

      console.log('👍 Liking post:', targetPostId);

      // Use the real API method
      await apiService.likePost(targetPostId);

      console.log('✅ Post liked successfully');

      setLikeState(prev => ({
        ...prev,
        isLiked: true,
        likesCount: prev.likesCount + 1,
        isPending: false,
      }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Beğeni eklenirken hata oluştu';
      console.error('❌ Like post error:', error);
      setLikeState(prev => ({
        ...prev,
        isPending: false,
        error: message,
      }));
      throw error;
    }
  }, [likeState.isPending]);

  const unlikePost = useCallback(async (targetPostId: number) => {
    if (likeState.isPending) return;

    try {
      setLikeState(prev => ({
        ...prev,
        isPending: true,
        error: null
      }));

      console.log('👎 Unliking post:', targetPostId);

      // Use the real API method
      await apiService.unlikePost(targetPostId);

      console.log('✅ Post unliked successfully');

      setLikeState(prev => ({
        ...prev,
        isLiked: false,
        likesCount: Math.max(prev.likesCount - 1, 0),
        isPending: false,
      }));

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Beğeni kaldırılırken hata oluştu';
      console.error('❌ Unlike post error:', error);
      setLikeState(prev => ({
        ...prev,
        isPending: false,
        error: message,
      }));
      throw error;
    }
  }, [likeState.isPending]);

  const toggleLike = useCallback(async (targetPostId: number) => {
    if (likeState.isLiked) {
      return await unlikePost(targetPostId);
    } else {
      return await likePost(targetPostId);
    }
  }, [likeState.isLiked, likePost, unlikePost]);

  const getLikeCount = useCallback(async (targetPostId: number) => {
    try {
      console.log('🔢 Getting like count for post:', targetPostId);

      // Use the batch API method for single post
      const counts = await apiService.getBatchLikeCounts([targetPostId]);
      const count = counts[targetPostId] || 0;

      console.log('✅ Like count fetched:', { postId: targetPostId, count });

      setLikeState(prev => ({
        ...prev,
        likesCount: count,
        error: null,
      }));

      return count;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Beğeni sayısı alınırken hata oluştu';
      console.error('❌ Get like count error:', error);
      setLikeState(prev => ({
        ...prev,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getBatchLikeCounts = useCallback(async (postIds: number[]) => {
    try {
      console.log('🔢 Getting batch like counts for posts:', postIds);

      // Use the real API method
      const counts = await apiService.getBatchLikeCounts(postIds);

      console.log('✅ Batch like counts fetched:', counts);

      return counts;
    } catch (error) {
      console.error('❌ Get batch like counts error:', error);
      throw error;
    }
  }, []);

  const clearError = useCallback(() => {
    setLikeState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    isLiked: likeState.isLiked,
    likesCount: likeState.likesCount,
    isPending: likeState.isPending,
    error: likeState.error,
    checkLikeStatus,
    likePost,
    unlikePost,
    toggleLike,
    getLikeCount,
    getBatchLikeCounts,
    clearError,
  };
};