import { useState, useCallback, useMemo } from 'react';

interface FormField {
  value: any;
  error: string | null;
  touched: boolean;
  required?: boolean;
  validator?: (value: any) => string | null;
}

interface FormState {
  [key: string]: FormField;
}

interface FormConfig {
  [key: string]: {
    initialValue?: any;
    required?: boolean;
    validator?: (value: any) => string | null;
  };
}

interface UseFormOptions {
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

export const useForm = (config: FormConfig, options: UseFormOptions = {}) => {
  const { validateOnChange = false, validateOnBlur = true } = options;

  // Initialize form state
  const initialState = useMemo(() => {
    const state: FormState = {};
    Object.keys(config).forEach(key => {
      state[key] = {
        value: config[key].initialValue ?? '',
        error: null,
        touched: false,
        required: config[key].required,
        validator: config[key].validator,
      };
    });
    return state;
  }, [config]);

  const [formState, setFormState] = useState<FormState>(initialState);

  const validateField = useCallback((key: string, value: any): string | null => {
    const field = config[key];
    if (!field) return null;

    // Check required
    if (field.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
      return 'Bu alan zorunludur';
    }

    // Run custom validator
    if (field.validator) {
      return field.validator(value);
    }

    return null;
  }, [config]);

  const setValue = useCallback((key: string, value: any) => {
    setFormState(prev => {
      const newState = { ...prev };
      if (newState[key]) {
        newState[key] = {
          ...newState[key],
          value,
          error: validateOnChange ? validateField(key, value) : newState[key].error,
        };
      }
      return newState;
    });
  }, [validateField, validateOnChange]);

  const setError = useCallback((key: string, error: string | null) => {
    setFormState(prev => {
      const newState = { ...prev };
      if (newState[key]) {
        newState[key] = {
          ...newState[key],
          error,
        };
      }
      return newState;
    });
  }, []);

  const setTouched = useCallback((key: string, touched = true) => {
    setFormState(prev => {
      const newState = { ...prev };
      if (newState[key]) {
        newState[key] = {
          ...newState[key],
          touched,
          error: touched && validateOnBlur ? validateField(key, newState[key].value) : newState[key].error,
        };
      }
      return newState;
    });
  }, [validateField, validateOnBlur]);

  const validateForm = useCallback((): boolean => {
    let isValid = true;
    const newState = { ...formState };

    Object.keys(newState).forEach(key => {
      const error = validateField(key, newState[key].value);
      newState[key] = {
        ...newState[key],
        error,
        touched: true,
      };
      if (error) {
        isValid = false;
      }
    });

    setFormState(newState);
    return isValid;
  }, [formState, validateField]);

  const resetForm = useCallback(() => {
    setFormState(initialState);
  }, [initialState]);

  const resetField = useCallback((key: string) => {
    if (initialState[key]) {
      setFormState(prev => ({
        ...prev,
        [key]: { ...initialState[key] },
      }));
    }
  }, [initialState]);

  const getFieldProps = useCallback((key: string) => {
    const field = formState[key];
    if (!field) return {};

    return {
      value: field.value,
      onChangeText: (value: any) => setValue(key, value),
      onBlur: () => setTouched(key, true),
      error: field.touched ? field.error : null,
    };
  }, [formState, setValue, setTouched]);

  const getValues = useCallback(() => {
    const values: { [key: string]: any } = {};
    Object.keys(formState).forEach(key => {
      values[key] = formState[key].value;
    });
    return values;
  }, [formState]);

  const getErrors = useCallback(() => {
    const errors: { [key: string]: string | null } = {};
    Object.keys(formState).forEach(key => {
      errors[key] = formState[key].error;
    });
    return errors;
  }, [formState]);

  const hasErrors = useMemo(() => {
    return Object.values(formState).some(field => field.error !== null);
  }, [formState]);

  const isValid = useMemo(() => {
    return Object.values(formState).every(field => field.error === null);
  }, [formState]);

  const isDirty = useMemo(() => {
    return Object.keys(formState).some(key => {
      return formState[key].value !== initialState[key].value;
    });
  }, [formState, initialState]);

  const isTouched = useMemo(() => {
    return Object.values(formState).some(field => field.touched);
  }, [formState]);

  return {
    // Field values and state
    values: getValues(),
    errors: getErrors(),
    formState,
    
    // Form state flags
    hasErrors,
    isValid,
    isDirty,
    isTouched,
    
    // Field operations
    setValue,
    setError,
    setTouched,
    getFieldProps,
    
    // Form operations
    validateForm,
    resetForm,
    resetField,
  };
};
