import { useState, useEffect, useCallback } from 'react';
import { apiService } from '@fishivo/services';

// Local type definitions to avoid export issues
interface Post {
  id: number;
  user_id: string;
  content: string;
  images?: string[];
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
    city?: string;
    country?: string;
  };
  created_at: string;
  updated_at: string;
  likes_count: number;
  comments_count: number;
}

// Local types for better type safety
interface CatchData extends Post {
  // Fishing-specific fields
  catch_details?: {
    species_id?: number;
    species_name?: string;
    weight?: number;
    length?: number;
    bait_used?: string;
    technique?: string;
    weather_conditions?: string;
    water_temperature?: number;
    time_of_day?: 'morning' | 'afternoon' | 'evening' | 'night';
    equipment_used?: string[];
  };
}

interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

interface CatchesState {
  catches: CatchData[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
  total: number;
}

interface CatchFilters {
  userId?: string;
  spotId?: number;
  tripId?: number;
  speciesId?: number;
}

export const useCatches = (filters?: CatchFilters): {
  catches: CatchData[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  total: number;
  loadCatches: (reset?: boolean) => Promise<CatchData[]>;
  loadMoreCatches: () => Promise<CatchData[] | undefined>;
  createCatch: (catchData: any) => Promise<any>;
  getCatchById: (catchId: number) => Promise<any>;
  refreshCatches: () => Promise<CatchData[]>;
  clearError: () => void;
} => {
  const [catchesState, setCatchesState] = useState<CatchesState>({
    catches: [],
    isLoading: false,
    error: null,
    hasMore: true,
    page: 1,
    total: 0,
  });

  useEffect(() => {
    loadCatches(true);
  }, [filters?.userId, filters?.spotId, filters?.tripId, filters?.speciesId]);

  const loadCatches = useCallback(async (reset = false) => {
    try {
      const currentPage = reset ? 1 : catchesState.page;

      setCatchesState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        page: currentPage,
      }));

      console.log('🎣 Loading catches:', { page: currentPage, filters });

      // Use the real API method - getCatches is the main fishing posts endpoint
      const response: PaginatedResponse<CatchData> = await apiService.getCatches(
        currentPage,
        10,
        filters
      );

      console.log('✅ Catches loaded:', response);

      setCatchesState(prev => ({
        ...prev,
        catches: reset ? response.items : [...prev.catches, ...response.items],
        isLoading: false,
        hasMore: response.hasMore,
        page: currentPage + 1,
        total: response.total,
      }));

      return response.items;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Avlar yüklenirken hata oluştu';
      console.error('❌ Catches loading error:', error);
      setCatchesState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, [filters, catchesState.page]);

  const loadMoreCatches = useCallback(async () => {
    if (!catchesState.hasMore || catchesState.isLoading) return;
    return await loadCatches(false);
  }, [catchesState.hasMore, catchesState.isLoading, loadCatches]);

  const createCatch = useCallback(async (catchData: any) => {
    try {
      setCatchesState(prev => ({ ...prev, isLoading: true, error: null }));

      console.log('📝 Creating catch:', catchData);

      // Use the real API method
      const newCatch = await apiService.createPost(catchData);

      console.log('✅ Catch created:', newCatch);

      setCatchesState(prev => ({
        ...prev,
        catches: [newCatch, ...prev.catches],
        isLoading: false,
        total: prev.total + 1,
      }));

      return newCatch;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Av kaydı oluşturulurken hata oluştu';
      console.error('❌ Catch creation error:', error);
      setCatchesState(prev => ({
        ...prev,
        isLoading: false,
        error: message,
      }));
      throw error;
    }
  }, []);

  const getCatchById = useCallback(async (catchId: number) => {
    try {
      console.log('🔍 Getting catch by ID:', catchId);

      // Use the real API method
      const catchData = await apiService.getPostById(catchId);

      console.log('✅ Catch fetched:', catchData);

      return catchData;
    } catch (error) {
      console.error('❌ Get catch by ID error:', error);
      throw error;
    }
  }, []);

  const refreshCatches = useCallback(async () => {
    return await loadCatches(true);
  }, [loadCatches]);

  const clearError = useCallback(() => {
    setCatchesState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    catches: catchesState.catches,
    isLoading: catchesState.isLoading,
    error: catchesState.error,
    hasMore: catchesState.hasMore,
    total: catchesState.total,
    loadCatches,
    loadMoreCatches,
    createCatch,
    getCatchById,
    refreshCatches,
    clearError,
  };
};
