import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { UnitsApiService } from '@fishivo/services';

interface UnitCategory {
  id: string;
  name: string;
  name_en: string;
  description: string;
  icon: string;
  base_unit: string;
  sort_order: number;
  is_active: boolean;
}

interface UnitDefinition {
  id: string;
  category_id: string;
  name: string;
  name_en: string;
  symbol: string;
  is_base_unit: boolean;
  conversion_factor?: number;
  conversion_formula?: string;
  reverse_formula?: string;
  precision_digits: number;
  min_value?: number;
  max_value?: number;
  regions: string[];
  popularity: number;
  default_for_regions: string[];
  use_case?: string;
  sort_order: number;
  is_active: boolean;
}

interface UserUnitPreferences {
  weight: string;
  length: string;
  temperature: string;
  distance: string;
  speed: string;
  depth: string;
}

interface UnitsContextValue {
  categories: UnitCategory[];
  units: Record<string, UnitDefinition[]>;
  userPreferences: UserUnitPreferences;
  isLoading: boolean;
  error: string | null;

  // Methods
  loadCategories: () => Promise<void>;
  loadUnitsForCategory: (categoryId: string) => Promise<void>;
  convertValue: (value: number, fromUnit: string, toUnit: string, category: string) => Promise<number>;
  setUserPreference: (category: string, unitId: string) => Promise<void>;
  getUserPreferredUnit: (category: string) => UnitDefinition | null;
  formatValue: (value: number, category: string, unitId?: string) => string;
  clearError: () => void;

  // Additional methods needed by mobile app
  getWeightUnit: () => UnitDefinition | null;
  getLengthUnit: () => UnitDefinition | null;
  getDepthUnit: () => UnitDefinition | null;
  getTemperatureUnit: () => UnitDefinition | null;
  convertFromUserUnit: (value: number, category: string) => Promise<number>;
  convertAndFormat: (value: number, category: string, targetUnit?: string) => Promise<string>;
  convertAndFormatSync: (value: number, category: string, targetUnit?: string) => string;
}

const UnitsContext = createContext<UnitsContextValue | undefined>(undefined);

const defaultPreferences: UserUnitPreferences = {
  weight: 'kg',
  length: 'cm',
  temperature: 'celsius',
  distance: 'km',
  speed: 'kmh',
  depth: 'm',
};

export const UnitsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [categories, setCategories] = useState<UnitCategory[]>([]);
  const [units, setUnits] = useState<Record<string, UnitDefinition[]>>({});
  const [userPreferences, setUserPreferences] = useState<UserUnitPreferences>(defaultPreferences);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeUnits();
    loadUserPreferences();
  }, []);

  const initializeUnits = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🔧 Initializing units system...');
      
      await loadCategories();
      
      console.log('✅ Units system initialized');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Birim sistemi başlatılırken hata oluştu';
      console.error('❌ Units initialization error:', error);
      setError(message);
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      console.log('📋 Loading unit categories...');
      
      const categoriesData = await UnitsApiService.getCategories();
      setCategories(categoriesData);
      
      console.log('✅ Unit categories loaded:', categoriesData.length);
    } catch (error) {
      console.error('❌ Load categories error:', error);
      throw error;
    }
  };

  const loadUnitsForCategory = async (categoryId: string) => {
    try {
      if (units[categoryId]) {
        // Already loaded
        return;
      }

      console.log('🔧 Loading units for category:', categoryId);
      
      const unitsData = await UnitsApiService.getUnitsForCategory(categoryId);
      
      setUnits(prev => ({
        ...prev,
        [categoryId]: unitsData,
      }));
      
      console.log('✅ Units loaded for category:', categoryId, unitsData.length);
    } catch (error) {
      console.error('❌ Load units for category error:', error);
      throw error;
    }
  };

  const convertValue = async (value: number, fromUnit: string, toUnit: string, category: string): Promise<number> => {
    try {
      console.log('🔄 Converting value:', { value, fromUnit, toUnit, category });
      
      const result = await UnitsApiService.convertUnit(value, fromUnit, toUnit);
      
      console.log('✅ Value converted:', result);
      
      return result;
    } catch (error) {
      console.error('❌ Convert value error:', error);
      throw error;
    }
  };

  const setUserPreference = async (category: string, unitId: string) => {
    try {
      console.log('⚙️ Setting user preference:', { category, unitId });
      
      const newPreferences = {
        ...userPreferences,
        [category]: unitId,
      };
      
      setUserPreferences(newPreferences);
      
      // Save to local storage
      localStorage.setItem('fishivo_unit_preferences', JSON.stringify(newPreferences));
      
      console.log('✅ User preference saved');
    } catch (error) {
      console.error('❌ Set user preference error:', error);
      throw error;
    }
  };

  const loadUserPreferences = () => {
    try {
      const saved = localStorage.getItem('fishivo_unit_preferences');
      if (saved) {
        const preferences = JSON.parse(saved);
        setUserPreferences({ ...defaultPreferences, ...preferences });
        console.log('✅ User preferences loaded from storage');
      }
    } catch (error) {
      console.error('❌ Load user preferences error:', error);
      // Use defaults
    }
  };

  const getUserPreferredUnit = (category: string): UnitDefinition | null => {
    const preferredUnitId = userPreferences[category as keyof UserUnitPreferences];
    if (!preferredUnitId) return null;

    // Find the unit in the loaded units
    for (const categoryUnits of Object.values(units)) {
      const unit = categoryUnits.find(u => u.id === preferredUnitId);
      if (unit) return unit;
    }

    return null;
  };

  const formatValue = (value: number, category: string, unitId?: string): string => {
    const unit = unitId 
      ? Object.values(units).flat().find(u => u.id === unitId)
      : getUserPreferredUnit(category);

    if (!unit) {
      return value.toString();
    }

    const precision = unit.precision_digits || 2;
    const formattedValue = value.toFixed(precision);
    
    return `${formattedValue} ${unit.symbol}`;
  };

  // Additional methods needed by mobile app
  const getWeightUnit = (): UnitDefinition | null => {
    return getUserPreferredUnit('weight');
  };

  const getLengthUnit = (): UnitDefinition | null => {
    return getUserPreferredUnit('length');
  };

  const getDepthUnit = (): UnitDefinition | null => {
    return getUserPreferredUnit('depth');
  };

  const getTemperatureUnit = (): UnitDefinition | null => {
    return getUserPreferredUnit('temperature');
  };

  const convertFromUserUnit = async (value: number, category: string): Promise<number> => {
    const userUnit = getUserPreferredUnit(category);
    if (!userUnit) return value;

    // Find base unit for the category
    const categoryUnits = units[category] || [];
    const baseUnit = categoryUnits.find(u => u.is_base_unit);
    if (!baseUnit) return value;

    return convertValue(value, userUnit.id, baseUnit.id, category);
  };

  const convertAndFormat = async (value: number, category: string, targetUnit?: string): Promise<string> => {
    try {
      const userUnit = getUserPreferredUnit(category);
      if (!userUnit) return value.toString();

      const targetUnitId = targetUnit || userUnit.id;
      const convertedValue = await convertValue(value, userUnit.id, targetUnitId, category);

      return formatValue(convertedValue, category, targetUnitId);
    } catch (error) {
      console.error('Convert and format error:', error);
      return value.toString();
    }
  };

  const convertAndFormatSync = (value: number, category: string, targetUnit?: string): string => {
    try {
      const userUnit = getUserPreferredUnit(category);
      if (!userUnit) return value.toString();

      const targetUnitId = targetUnit || userUnit.id;
      // For sync version, we'll use a simple conversion without API call
      // This is a fallback for immediate display needs
      return formatValue(value, category, targetUnitId);
    } catch (error) {
      console.error('Convert and format sync error:', error);
      return value.toString();
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value: UnitsContextValue = {
    categories,
    units,
    userPreferences,
    isLoading,
    error,
    loadCategories,
    loadUnitsForCategory,
    convertValue,
    setUserPreference,
    getUserPreferredUnit,
    formatValue,
    clearError,
    getWeightUnit,
    getLengthUnit,
    getDepthUnit,
    getTemperatureUnit,
    convertFromUserUnit,
    convertAndFormat,
    convertAndFormatSync,
  };

  return <UnitsContext.Provider value={value}>{children}</UnitsContext.Provider>;
};

export const useUnits = () => {
  const context = useContext(UnitsContext);
  if (!context) {
    throw new Error('useUnits must be used within UnitsProvider');
  }
  return context;
};
