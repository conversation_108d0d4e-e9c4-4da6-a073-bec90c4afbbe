import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '@fishivo/services';

// Local type definitions to avoid export issues
interface Location {
  latitude: number;
  longitude: number;
  address?: string;
  city?: string;
  country?: string;
}

interface LocationData extends Location {
  accuracy?: number;
  timestamp?: number;
}

interface SavedLocation {
  id: string;
  name: string;
  location: Location;
  type: 'fishing_spot' | 'marina' | 'home' | 'custom';
  notes?: string;
  created_at: string;
}

interface LocationContextValue {
  currentLocation: LocationData | null;
  savedLocations: SavedLocation[];
  isLoading: boolean;
  error: string | null;
  isLocationEnabled: boolean;
  isWatching: boolean;
  
  // Methods
  getCurrentLocation: () => Promise<LocationData | null>;
  watchLocation: () => void;
  stopWatching: () => void;
  requestPermission: () => Promise<boolean>;
  saveLocation: (name: string, location: Location, type: SavedLocation['type'], notes?: string) => Promise<void>;
  loadSavedLocations: () => Promise<void>;
  removeLocation: (locationId: string) => Promise<void>;
  getLocationName: (lat: number, lon: number) => Promise<string | null>;
  clearError: () => void;
}

const LocationContext = createContext<LocationContextValue | undefined>(undefined);

export const LocationProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null);
  const [savedLocations, setSavedLocations] = useState<SavedLocation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLocationEnabled, setIsLocationEnabled] = useState(false);
  const [isWatching, setIsWatching] = useState(false);
  const [watchId, setWatchId] = useState<number | null>(null);

  useEffect(() => {
    checkLocationPermission();
    loadSavedLocations();
    
    return () => {
      if (watchId !== null) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [watchId]);

  const checkLocationPermission = async (): Promise<boolean> => {
    try {
      if (!navigator.geolocation) {
        setError('Konum servisi bu cihazda desteklenmiyor');
        setIsLocationEnabled(false);
        return false;
      }

      // For web, we can't check permissions directly, so we assume it's available
      setIsLocationEnabled(true);
      setError(null);
      return true;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Konum izni kontrol edilirken hata oluştu';
      setError(message);
      setIsLocationEnabled(false);
      return false;
    }
  };

  const requestPermission = async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('📍 Requesting location permission...');

      // Try to get current position to trigger permission request
      await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          { timeout: 5000 }
        );
      });

      setIsLocationEnabled(true);
      console.log('✅ Location permission granted');
      return true;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Konum izni alınamadı';
      setError(message);
      setIsLocationEnabled(false);
      console.error('❌ Location permission error:', error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentLocation = async (): Promise<LocationData | null> => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('📍 Getting current location...');

      if (!navigator.geolocation) {
        throw new Error('Konum servisi desteklenmiyor');
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 15000,
            maximumAge: 60000,
          }
        );
      });

      const locationData: LocationData = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: position.timestamp,
      };

      // Try to get address information
      try {
        const locationName = await apiService.getLocationName(
          position.coords.latitude,
          position.coords.longitude
        );
        
        if (locationName) {
          locationData.address = locationName.address;
          locationData.city = locationName.city;
          locationData.country = locationName.country;
        }
      } catch (addressError) {
        console.warn('Could not get address for location:', addressError);
      }

      setCurrentLocation(locationData);
      console.log('✅ Current location obtained:', locationData);

      return locationData;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Konum alınırken hata oluştu';
      console.error('❌ Get current location error:', error);
      setError(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const watchLocation = () => {
    if (!navigator.geolocation) {
      setError('Konum servisi desteklenmiyor');
      return;
    }

    if (isWatching) {
      console.warn('Location watching is already active');
      return;
    }

    console.log('👀 Starting location watch...');

    const id = navigator.geolocation.watchPosition(
      (position) => {
        const locationData: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        };

        setCurrentLocation(locationData);
        setError(null);
        console.log('📍 Location updated:', locationData);
      },
      (error) => {
        const message = error.message || 'Konum takibi sırasında hata oluştu';
        console.error('❌ Location watch error:', error);
        setError(message);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 60000,
      }
    );

    setWatchId(id);
    setIsWatching(true);
  };

  const stopWatching = () => {
    if (watchId !== null) {
      console.log('🛑 Stopping location watch...');
      navigator.geolocation.clearWatch(watchId);
      setWatchId(null);
      setIsWatching(false);
    }
  };

  const saveLocation = async (
    name: string,
    location: Location,
    type: SavedLocation['type'],
    notes?: string
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('💾 Saving location:', { name, location, type });

      const newLocation: SavedLocation = {
        id: Date.now().toString(), // Simple ID generation
        name,
        location,
        type,
        notes,
        created_at: new Date().toISOString(),
      };

      // Save to local storage (in a real app, this would be saved to the backend)
      const saved = localStorage.getItem('fishivo_saved_locations');
      const locations = saved ? JSON.parse(saved) : [];
      locations.push(newLocation);
      localStorage.setItem('fishivo_saved_locations', JSON.stringify(locations));

      setSavedLocations(prev => [...prev, newLocation]);

      console.log('✅ Location saved successfully');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Konum kaydedilirken hata oluştu';
      console.error('❌ Save location error:', error);
      setError(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loadSavedLocations = async () => {
    try {
      console.log('📋 Loading saved locations...');

      // Load from local storage
      const saved = localStorage.getItem('fishivo_saved_locations');
      const locations = saved ? JSON.parse(saved) : [];

      setSavedLocations(locations);
      console.log('✅ Saved locations loaded:', locations.length);
    } catch (error) {
      console.error('❌ Load saved locations error:', error);
      // Don't throw, just log the error
    }
  };

  const removeLocation = async (locationId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🗑️ Removing location:', locationId);

      // Remove from local storage
      const saved = localStorage.getItem('fishivo_saved_locations');
      const locations = saved ? JSON.parse(saved) : [];
      const filtered = locations.filter((loc: SavedLocation) => loc.id !== locationId);
      localStorage.setItem('fishivo_saved_locations', JSON.stringify(filtered));

      setSavedLocations(prev => prev.filter(loc => loc.id !== locationId));

      console.log('✅ Location removed successfully');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Konum silinirken hata oluştu';
      console.error('❌ Remove location error:', error);
      setError(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const getLocationName = async (lat: number, lon: number): Promise<string | null> => {
    try {
      console.log('🗺️ Getting location name for:', { lat, lon });

      const locationName = await apiService.getLocationName(lat, lon);
      
      console.log('✅ Location name retrieved:', locationName);

      return locationName?.address || null;
    } catch (error) {
      console.error('❌ Get location name error:', error);
      return null;
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value: LocationContextValue = {
    currentLocation,
    savedLocations,
    isLoading,
    error,
    isLocationEnabled,
    isWatching,
    getCurrentLocation,
    watchLocation,
    stopWatching,
    requestPermission,
    saveLocation,
    loadSavedLocations,
    removeLocation,
    getLocationName,
    clearError,
  };

  return <LocationContext.Provider value={value}>{children}</LocationContext.Provider>;
};

export const useLocation = () => {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error('useLocation must be used within LocationProvider');
  }
  return context;
};
