{"name": "@fishivo/hooks", "version": "1.0.0", "description": "Shared React hooks for Fishivo", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx"}, "keywords": ["hooks", "react", "react-native"], "author": "Fishivo Team", "license": "MIT", "dependencies": {"@fishivo/services": "1.0.0", "@fishivo/shared": "1.0.0", "@supabase/supabase-js": "^2.50.2", "axios": "1.6.7"}, "devDependencies": {"@types/react": "18.2.21", "eslint": "8.56.0", "tsup": "^8.0.0", "typescript": "5.3.3"}, "peerDependencies": {"react": "^18.2.0"}}