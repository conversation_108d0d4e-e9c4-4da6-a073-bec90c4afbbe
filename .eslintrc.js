module.exports = {
  root: true,
  extends: [
    '@react-native',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  rules: {
    // React Native 0.78 optimized rules
    'react-native/no-unused-styles': 'warn',
    'react-native/split-platform-components': 'warn',  
    'react-native/no-inline-styles': 'off', // Allow for flexibility
    'react-native/no-color-literals': 'warn',
    'react-native/no-raw-text': 'off', // Too restrictive
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-explicit-any': 'off', // Allow any for gradual typing
    'prettier/prettier': 'off', // Prevent conflicts
    'react/react-in-jsx-scope': 'off', // React 19 doesn't need this
    'react/jsx-uses-react': 'off', // React 19 doesn't need this
    'no-undef': 'off', // TypeScript handles this
    'semi': ['error', 'always'],
    'quotes': ['error', 'single'],
    'comma-dangle': ['error', 'always-multiline'],
  },
  settings: {
    'react-native/style-sheet-object-names': [
      'StyleSheet',
      'PlatformStyleSheet', 
      'styles',
    ],
  },
  env: {
    'react-native/react-native': true,
    node: true,
    es2021: true,
  },
}; 