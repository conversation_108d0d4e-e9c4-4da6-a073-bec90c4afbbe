{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "composite": true}, "references": [{"path": "./packages/shared"}, {"path": "./packages/config"}, {"path": "./packages/utils"}, {"path": "./packages/services"}, {"path": "./packages/hooks"}, {"path": "./packages/ui"}, {"path": "./packages/database"}, {"path": "./packages/backend"}], "files": []}